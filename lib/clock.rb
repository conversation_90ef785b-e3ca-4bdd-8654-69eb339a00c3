require 'clockwork'
require File.expand_path('../config/boot', __dir__)

require File.expand_path('../config/environment', __dir__)
require 'active_support/time' # Allow numeric durations

include Clockwork

# Goals and OKRs
every(2.hours, 'send_okr_checkin_notification') do
  start_time = Time.current
  end_time = start_time + 2.hours

  # Send notifications for each employee goals setting
  emp_goals_settings_id = GoalsSetting.where(active: true)
    .where('next_reminder_at >= ? AND next_reminder_at <= ?', start_time, end_time)
    .where.not(employee_id: nil).pluck(:id)
  SendOkrCheckInNotification.delay(queue: 'normal').call(emp_goals_settings_id)

  # Update next_reminder time for company goals setting
  company_goals_settings = GoalsSetting.where(active: true)
    .where('next_reminder_at >= ? AND next_reminder_at <= ?', start_time, end_time)
    .where(employee_id: nil, department_id: nil).pluck(:id)
  GoalsSettings::UpdateCompanyGoalsSetting.delay(queue: 'normal').call(company_goals_settings)
end

# disabling for some time untill we rewrite a logic and correct message
# every(1.day, 'TriggerManagerOkrUpdatesReportNotification', at: '3:00', tz: 'UTC') do
#   # Check if it's not Saturday (6) or Sunday (0)
#   unless [0, 6].include?(Time.now.in_time_zone('UTC').wday)
#     OkrUpdates::Notifications::TriggerManagerOkrUpdatesReportNotification.delay.call
#   end
# end

every(1.hours, 'TriggerDailyGoalsDigest') do
  CoreOkr::ActivityLogs::TriggerDailyGoalsDigest.delay.call
end

every(1.day, 'AnalyticsDataProcessorSync', at: '12:30', tz: 'UTC') do
  Analytics::DataProcessor::TriggerSync.call
end

# # Coffee Connect
# every(1.week, 'seed_interest_tags', at: 'Tuesday 03:50', tz: 'UTC') do
#   # call the seed 10 minutes before the coffee connect goes
#   CoffeeConnect::Pairing::Algorithms::SeedInterestBasedTags.delay(queue: 'low').call
# end

# every(1.week, 'create_cc_pairs', at: 'Tuesday 07:00', tz: 'UTC') do
#   @accounts = Account.where(run_coffee_connect: true)
#   @accounts.each do |account|
#     CoffeeConnect::Flow::V2::InitiateCoffeeConnect.delay.call(account.id)
#   end
# end
# this is after two hours from intro, check if rapid fire played
# send some encoraging anaytics
# every(1.week, 'rapid_fire_checking_anaytics', at: 'Tuesday 10:00', tz: 'UTC') do
#   @accounts = Account.where(run_coffee_connect: true)
#   @accounts.each do |account|
#     CoffeeConnect::RapidFire::RapidFireStatusCheckAnalysis.delay.call(account.id)
#   end
# end
# do a checking if they have played rf if not,
# either both or one,
# give them a link that they can use
# to set up meetings
# do this after 24 hours
# every(1.week, 'rapid_fire_checking_anaytics', at: 'Wednesday 07:30', tz: 'UTC') do
#   @accounts = Account.joins(:coffee_connect_settings)
#     .where(run_coffee_connect: true)
#     .where.not(coffee_connect_settings: { pairing_logic: 'same_department_and_random_pairing' })
#   @accounts.each do |account|
#     CoffeeConnect::RapidFire::RapidFireStatusCheckAnalysis.delay(queue: 'low').call(account.id)
#   end
# end

# every(1.week, 'rapid_fire_initative_reminders', at: 'Thursday 07:30', tz: 'UTC') do
#   @accounts = Account.joins(:coffee_connect_settings)
#     .where(run_coffee_connect: true)
#     .where.not(coffee_connect_settings: { pairing_logic: 'same_department_and_random_pairing' })
#   @accounts.each do |account|
#     CoffeeConnect::Reminders::CcInitiativeReminder.delay(queue: 'low').call(account.id)
#   end
# end


every(1.week, 'delete_removed_calendar_events', at: 'Saturday 9:30', tz: 'UTC') do
  @users = User.all.includes(:employee)
  @users.each do |user|
    Calendar::RemoveDeletedCalendarEvents.call(user)
  end
end

every(1.day, 'send_invalid_calendar_events_notification', at: '1:00', tz: 'UTC') do
  InvalidCalEventsSlackNotification.call
end

# on thurday, send the start a meeting/schedule
# for those who have not played the rapid fire so have no
# way to set a meeting
# every(1.week, 'rapid_fire_checking', at: 'Friday 10:00', tz: 'UTC') do
#   @accounts = Account.where(run_coffee_connect: true)
#   @accounts.each do |account|
#     CoffeeConnect::RapidFire::RapidFireStatusCheck.delay.call(account.id)
#   end
# end

# every(1.week, 'cc_initiative_reminder', at: 'Thursday 7:30', tz: 'UTC') do
#   @accounts = Account.where(run_coffee_connect: true)
#   @accounts.each do |account|
#     CoffeeConnect::Reminders::CcInitiativeReminder.delay.call(account.id)
#   end
# end
# every(1.week, 'multi_people_meeting_reminder', at: 'Wednesday 07:00', tz: 'UTC') do
#   @accounts = Account.joins(:coffee_connect_settings)
#     .where(run_coffee_connect: true)
#     .where(coffee_connect_settings: { pairing_logic: 'same_department_and_random_pairing' })
#   @accounts.each do |account|
#     CoffeeConnect::Reminders::CheckingSameDepartmentAndRandom.delay(queue: 'low').call(account.id)
#   end
# end

# every(1.week, 'do_cc_checkin', at: 'Monday 07:00', tz: 'UTC') do
#   @accounts = Account.where(run_coffee_connect: true)
#   @accounts.each do |account|
#     next if account.coffee_connect_settings.last.fixed_times.present?

#     CoffeeConnectCheckin.delay(queue: 'low').call(account.id)
#   end
# end

# every(15.minutes, 'cc_checkin_after_meeting') do
#   @accounts = Account.joins(:coffee_connect_settings).where(
#     run_coffee_connect: true,
#   ).where.not(coffee_connect_settings: { fixed_times: nil })
#   @accounts.each do |account|
#     CoffeeConnect::CheckIn::CoffeeConnectCheckinAfterMeeting.delay(queue: 'low').call(account.id)
#   end
# end

every(1.week, 'sync_team_user_details', at: 'Saturday 03:00', tz: 'UTC') do
  SlackTeam.joins(:account).each do |slack_team|
    FetchSlackUsersDetails.call(slack_team.id)
  end
end

# no more in use
# every(1.week, 'send_cc_summary', at: 'Tuesday 06:00', tz: 'UTC') do
#   @accounts = Account.where(run_coffee_connect: true)
#   @accounts.each do |account|
#     next if account.id == 9

#     SendCoffeeConnectSummary.delay(queue: 'low').call(account)
#   end
# end

# no more in use
# every(10.minutes, 'send_cc_meeting_reminder') do
#   CoffeeConnectMeetingReminder.delay(queue: 'low').call
# end

# no more in use
# every(1.week, 'send_rapid_fire_quiz_no_meeting', at: 'Wednesday 07:00', tz: 'UTC') do
#   CoffeeConnect::RapidFire::RapidFireInitiationNoMeeting.call
# end

# no more in use
# first send, on thur
# running a DelayedJob task
# every(1.week, 'send_rapid_fire_quiz_summary_first', at: 'Friday 07:00', tz: 'UTC') do
#   @accounts = Account.where(run_coffee_connect: true)
#   @accounts.each do |account|
#     CoffeeConnect::RapidFire::SendRapidFireSummary.delay(queue: 'low').call(account)
#   end
# end

# second send, next week tuesdays
# running a DelayedJob task
# every(1.week, 'send_rapid_fire_quiz_summary_second', at: 'Tuesday 04:55', tz: 'UTC') do
#   @accounts = Account.where(run_coffee_connect: true)
#   @accounts.each do |account|
#     CoffeeConnect::RapidFire::SendRapidFireSummary.delay.call(account)
#   end
# end

# every(30.minutes, 'send_rapid_fire_quiz_meeting_set') do
#   CoffeeConnect::RapidFire::RapidFireInitiationMeetingSet.call
# end

# every(1.week, 'send_rapid_fire_quiz', at: [
#  'Wednesday 00:00', 'Wednesday 01:00', 'Wednesday 02:00', 'Wednesday 03:00', 'Wednesday 04:00',
#  'Wednesday 05:00', 'Wednesday 06:00', 'Wednesday 07:00', 'Wednesday 08:00', 'Wednesday 09:00',
#  'Wednesday 10:00', 'Wednesday 11:00', 'Wednesday 12:00', 'Wednesday 13:00', 'Wednesday 14:00',
#  'Wednesday 15:00', 'Wednesday 16:00', 'Wednesday 17:00', 'Wednesday 18:00', 'Wednesday 19:00',
#  'Wednesday 20:00', 'Wednesday 21:00', 'Wednesday 22:00', 'Wednesday 23:00', 'Wednesday 24:00'],
#  tz: 'UTC') do
#   SendCoffeeConnectSummary.delay.call(account)
# end

# every(1.week, 'sync_team_user_details', at: 'Monday 07:00', tz: 'UTC') do
#   MicrosoftTeam.all.each do |ms_team|
#     conversation = ms_team.microsoft_team_conversation.where(conversation_category: 'channel').last
#     next unless conversation

#     FetchMsTeamUsersDetails.delay.call(conversation.id, ms_team)
#   end
# end

# every(1.hour, 'send_action_items_mail') do
#   SendFollowupItemsMail.call()
# end

# every(1.week, 'send_weekly_superlist_digest', at: 'Thursday 10:00') do
#   # super_list_items = SuperListItem.where(
#   created_at: Time.now.beginning_of_day-1.week..Time.now.beginning_of_day
#   ).where.not(reportee_id: nil)
#   SendSuperListDigest.call
# end

## 1-on-1s
every(1.hour, 'send_talking_points_reminder', at: '**:00') do
  SendTalkingPointsReminder.call('1_day_before')
  SendTalkingPointsReminder.call('manager_morning_one_on_one_day')
end

every(6.hours, 'create_new_one_on_ones', at: '**:00') do
  CreateNewOneOnOnes.delay(queue: 'low').call
end

every(5.minutes, 'schedule_action_reminders') do
  ScheduleActionReminders.delay(queue: 'low').call
end

## Engagement
every(2.minutes, 'schedule_feedback_survey') do
  ScheduleRunner.delay(queue: 'normal').call('schedule_feedback_survey')
end

every(2.minutes, 'send_survey_reminder_notifications') do
  ScheduleRunner.delay(queue: 'normal').call('send_survey_reminder_notifications')
end

# every(30.minutes, 'schedule_manager_nudge') do
#   CronManager.call('schedule_manager_nudge')
# end

# every(30.minutes, 'schedule_ceo_reminder') do
#   CronManager.call('schedule_ceo_reminder')
# end

# every(1.day, 'schedule_recurrent_survey', at: '00:00', tz: 'UTC') do
#   CronManager.call('schedule_recurrent_survey')
# end

# every(1.day, 'schedule_pulse_survey', at: '00:30', tz: 'UTC') do
#   CronManager.call('schedule_pulse_survey')
# end

every(1.day, 'schedule_lifecycle_survey', at: '00:30', tz: 'UTC') do
  TriggerLifecycleSurveys.delay(queue: 'low').call
end

# Disabling because we messed up for Dunzo on 19th August
# every(1.week, 'anonymous_message_digest', at: 'Thursday 10:00', tz: 'UTC') do
#   AdminAnonymousMessageDigest.call
# end

every(1.week, 'Calculate Drivers Benchmark', at: 'Sunday 10:00', tz: 'UTC') do
  CreateDriversBenchmark.delay(queue: 'low').call
end

every(1.week, 'Calculate Questions Benchmark', at: 'Sunday 11:00', tz: 'UTC') do
  CreateQuestionsBenchmark.delay(queue: 'low').call
end

#  Engagement slack
# every(2.hour, 'send_feedback_survey_messages_reminder', at: '**:00') do
#   Slack::EngagementMessagesActions::AdminAnonymousMessageNotification.delay.call('feedback_survey')

# end
# every(2.hour, 'send_reminder_email_messages_reminder', at: '**:00') do
#   Slack::EngagementMessagesActions::AdminAnonymousMessageNotification.delay.call('reminder_email')

# end
# every(1.day, 'send_reminder_email_messages_reminder', at: '9:30') do
#   Slack::EngagementMessagesActions::AdminAnonymousMessageNotification.delay.call('ceo_reminder')

# end

# survey during the survey
# every(2.hour, 'send_participation_feedback_survey_reminder', at: '**:00') do
#   Slack::SurveyNotifications::ScheduleParticipation.delay.call('feedback_survey')
# end

# every(1.day, 'send_participation_ceo_reminder', at: '9:30') do
#   Slack::SurveyNotifications::ScheduleParticipation.delay.call('ceo_reminder')
# end

## Calendar related
every(6.hours, 'fetch_calendar_events') do
  FetchCalendarEvents.call
end

## Delete extra created Calendar Settings
every(1.hour, 'delete_extra_calendar_settings') do
  data = CalendarSetting.where("expiration > ?", Time.current).group(:employee_id).count.to_a
  data.each do |key, val|
    if val > 1
      cs = CalendarSetting.where(employee_id: key).where("expiration > ?", Time.current)
      cs.each do |s|
        break if s == cs.last
        s.destroy
      end
      puts cs.count
    end
  end
end

every(1.day, 'update_web_hook', at: '02:00') do
  UpdateCalendarWebhook.delay(queue: 'low').call
end

if Rails.env.production?
  every(1.day, 'Sync To Hubspot', at: '20:30') do
    SyncAllToHubspot.delay(queue: 'low').call
  end

  every(1.day, 'Import account employees from external HRIS platform', at: '00:30') do
    ImportEmployeeData::AllAccountsHrisImporter.delay(queue: 'low').call
  end

  # This was spamming the slack channel. Need better solution.
  # every(5.minutes, 'push untriggered notifications') do
  #   WebPerformanceReview::ReviewCycleNotifications::PushUntriggeredNotifications.delay(queue: 'immediate').call
  # end
end

every(1.day, 'Check token access token validity', at: '01:30') do
  Calendar::Tokens::CheckAllTokenValidity.call
end

every(1.month, 'Add default account cycles', at: '00:00') do
  Monthly_Add_Goal_Cycles.delay(queue: 'low').call
end

every(1.day, 'Sync External Progress Integration', at: '00:00') do
  return unless Rails.env.production? || Rails.env.demo?

  SyncGoalsExternalIntegration.delay(queue: 'low').call
end

# every(1.day, 'Export Paid Account Goals sheet', at: '22:30', tz: 'UTC') do
#   Okr::OkrIntegrations::GoogleSheets::ExportAccountSheets.call if Rails.env.production?
# end

# every(1.day, 'Export Hierachal Google sheets and Folder', at: '22:00') do
  # account_id = 1712    # hardcoded for razorpay as per requirement.
  # Okr::OkrIntegrations::GoogleSheets::Departmental::ExportFolderStructure.call(account_id)
  # Okr::OkrIntegrations::GoogleSheets::Departmental::ExportFolderStructure.call(account_id, 'master')

  # Department.where(account_id: account_id).each do |dept|
  #   Okr::OkrIntegrations::GoogleSheets::Departmental::ExportDepartmentWiseSheet.delay.call(account_id, dept)
  # end

  # Department.where(account_id: account_id).each do |dept|
  #   Okr::OkrIntegrations::GoogleSheets::Departmental::ExportDepartmentWiseSheet.delay.call(account_id, Department.find(1979), 'previous')
  # end

  # Okr::OkrIntegrations::GoogleSheets::Departmental::ExportDepartmentWiseSheet.delay.call(account_id, 0, nil)
  # Okr::OkrIntegrations::GoogleSheets::Departmental::ExportMasterSheet.call(account_id)
# end

every(1.day, 'Schedule Next Biz Review', at: '00:00', tz: 'UTC') do
  # disable this for now, push this to the user to do it on their own
  # BizReviewSchedule.joins(:biz_review).where.not('biz_reviews.account_id = 1712').where(cycle_end_date: DateTime.now.all_day).each do |biz_review_schedule|
  #   # CreateNextBizReviewSchedule.delay(run_at: biz_review_schedule.cycle_end_date).call(biz_review_schedule)
  # end
  BizReview.where(auto_create_next_review: DateTime.now.all_day).each do |biz_review|
    biz_review_schedule = biz_review.biz_review_schedules.order('cycle_start_date DESC').first
    CreateNextBizReviewSchedule.delay(run_at: biz_review.auto_create_next_review).call(biz_review_schedule)
   end
  # BizReviewSchedule.joins(:biz_review).where.not(biz_reviews: { auto_create_next_review: nil })
  # .where(biz_review: { auto_create_next_review: DateTime.now.all_day }).each do |biz_review_schedule|
  # CreateNextBizReviewSchedule.delay(run_at: biz_review_schedule.biz_review.auto_create_next_review).call(biz_review_schedule)
end

# Perfomance Review
every(2.minutes, 'schedule_perfomance_launch') do
  ReviewNotificationScheduler.call
end

every(2.minutes, 'update review cycle notification email delivery status') do
  ReviewCycleEmailDeliveryStatus.call
end

every(1.day, 'Modulate home page actions where enddate is nil') do
  HomePageActionModulator.call
end

# Delete 90 days older version
# every(1.day, 'delete_90_days_old_version', at: '01:00', tz: 'UTC') do
#   PaperTrail::Version.where('created_at < ?', 90.day.ago).delete_all
# end

every(1.day, 'delete_unlinked_90_days_calendar_events', at: '02:00', tz: 'UTC') do
  CalendarEvent.where('one_on_one_id is null and start_time < ?', 90.days.ago).destroy_all
end

every(5.minutes, 'update_eofy_rzp') do
  CreateRazorpayCustomFields.call
end

every(1.day, 'All Employees NPS Feedback Questions', at: '03:00') do
  AllEmployeesFeedbackQuestions.call
end

every(6.hour, 'Update Billing status for employees') do
  EmployeeBilling::DeactivateEmployees.delay(queue: 'low').deactivate_inactive_employees
end

every(1.day, 'OKR Cycle Close Notification', at: '04:00') do
  SendOkrCycleCloseNotifications.delay(queue: 'low').call
end

if Rails.env.demo?
  every(1.day, 'Cancel Expired Demo Accounts') do
    CancelExpiredDemoAccounts.delay(queue: 'low').call
  end
end

# Run once daily at end of business day (6 PM) in each account's timezone
# Refresh dynamic job schedules every hour
every(1.hour, 'reload_manager_mapping_jobs') do
  ManagerChanges::LoadDynamicManagerMappingSchedules.call
end