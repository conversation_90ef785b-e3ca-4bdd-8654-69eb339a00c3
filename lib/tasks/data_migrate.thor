require 'pp'
require 'csv'
require 'date'
require 'thor'
require 'parallel'
require 'json'
require File.expand_path('config/environment.rb')

class DataMigrate < Thor

  desc 'update_default_employee_attributes', 'Update default employee attributes'
  def update_default_employee_attributes
    accounts_with_custom_attributes = EmployeeAttribute.where.not(attr_type: 'system').pluck(:account_id).uniq
    account_ids = EmployeeAttribute.all.pluck(:account_id).uniq - accounts_with_custom_attributes

    Account.where(id: account_ids).find_each do |acc|
      last_emp_attribute_position = acc.employee_attributes.pluck(:position).max

      params = {
        title: "Level",
        enabled: true,
        role_access_config:
          {
            "viewer_type"=>"flexible",
            "allowed_viewer_roles"=>["all_employees"],
            "possible_viewer_roles"=>["admins", "managers", "indirect_managers", "self", "all_employees"]
          },
        attr_type: "system",
        fixed_position: false,
        value_type: "string",
        copyable: false,
        position: last_emp_attribute_position + 1,
      }

      acc.employee_attributes.create(params)

      pp "Added Level Employee Attribute for Account: #{acc.id}"
    end
  end

  desc 'update_employee_filter_conditions', 'Update Employee Filter Conditions'
  def update_employee_filter_conditions
    EmployeeFilterCondition.all.each do |efc|
      value = efc.value
      value_array = value.to_s.gsub(/\(|\)|'/, '').split(',').map(&:strip).compact # Existing way of converting stored string to arrays for FE
      efc.update(json_value: value_array)

      puts "Updated json_value for Employee Filter Condition: #{efc.id}"
    end
  end

  desc 'update_biz_review_permissions', 'Update biz review permissions'
  def update_biz_review_permissions
    query = <<-SQL
      SELECT DISTINCT
        user_id,
        SUBSTRING_INDEX(
          SUBSTRING_INDEX(properties ->> "$.url", 'bizreview/', -1),
          '/',
          1
        ) AS biz_review_id
      FROM
        ahoy_events
      WHERE
        properties -> "$.url" REGEXP 'dashboard/bizreview/[0-9]+/schedule/[0-9]+'
        AND properties ->> "$.url" NOT REGEXP '^http://localhost'
        AND DATE(time) > DATE('2024-01-01')
      ORDER BY
        user_id,
        biz_review_id;
    SQL

    Acl.define_singleton_method(:skip_callbacks) { true } # Do not send access granted mails

    results = ActiveRecord::Base.connection.execute(query)
    user_ids = results.map(&:first).compact.uniq
    users = User.where(id: user_ids).includes(:employee).index_by(&:id)

    results.each_slice(500) do |batch|
      batch.each do |row|
        emp = users[row.first]&.employee
        next if emp.blank? || emp.account_id == 3179

        Acl.find_or_create_by(
          resource_type: 'BizReview',
          resource_id: row.last,
          actorable: emp,
          permission_type: 'edit',
          created_by: emp,
        )

        puts "Processed - User ID: #{row.first}, Biz Review ID: #{row.last}"
      end
    end

  ensure
    Acl.singleton_class.remove_method(:skip_callbacks) if Acl.respond_to?(:skip_callbacks)
  end


  desc 'toggle_one_on_one_suggestion_box_type', 'Toggle the suggestion box type in the one_on_one_settings table for a company between system and custom'
  def toggle_one_on_one_suggestion_box_type(account_id)
    record = OneOnOneSetting.find_by(account_id: account_id)
    if record
      # Toggle the `suggested_talking_points_box_type` attribute
      if record.suggested_talking_points_box_type == 'system'
        record.suggested_talking_points_box_type = 'custom'
        temp = SuggestedTalkingPointCategory.find_by(account_id: account_id, category_name: 'Recommended')
        if !temp.present?
          SuggestedTalkingPointCategory.create!(account_id: account_id, category_name: 'Recommended', is_system: false)  
        end
      else
        record.suggested_talking_points_box_type = 'system'
      end
      record.save
    else
      puts "one_on_one_setting for account with id: #{account_id} not found."
    end
  end 

  desc 'populate_custom_talking_point_data', 'Populates custom suggestion box data of a company from a local CSV file to the one_on_one_agenda_templates and suggested_talking_point_categories tables.'
  def populate_custom_talking_point_data(csv_file_name, account_id)
    record = OneOnOneSetting.find_by(account_id: account_id)

    if record.nil?
      puts "one_on_one_setting for account with id: #{account_id} not found."
      return
    elsif record.suggested_talking_points_box_type == 'system'
      SuggestedTalkingPointCategory.find_or_create_by!(account_id: account_id, category_name: 'Recommended', is_system: false)
    end

    csv_file_key = "talking_points/#{csv_file_name}"
    s3 = Aws::S3::Resource.new(region: ENV['AWS_REGION'])
    obj = s3.bucket(ENV['AWS_S3_BUCKET']).object(csv_file_key)
    obj_file = obj.get.body

    ActiveRecord::Base.transaction do
      CSV.parse(obj_file, { encoding: "UTF-8", headers: true, header_converters: [:downcase, :symbol], converters: :all}) do |row|
        category_name = row[:category]&.strip
        talking_point = row[:talking_point]&.strip
        talking_point_type = row[:talking_point_type] || 2

        next if !category_name || !talking_point

        new_category = SuggestedTalkingPointCategory.find_or_initialize_by(account_id: account_id, category_name: category_name, is_system: false)
        
        if new_category.new_record?
          new_category.position = (SuggestedTalkingPointCategory.where(account_id: account_id, is_system: false).maximum(:position) || 0) + 1
          new_category.position = nil if category_name == 'Recommended'
          new_category.save!
          puts "Created new category '#{category_name}' with position #{new_category.position}."
        end

        new_template = OneOnOneAgendaTemplate.find_or_initialize_by(
          category: category_name,
          account_id: account_id,
          talking_point: talking_point,
          talking_point_type: talking_point_type,
          is_system: false,
          suggested_talking_point_category_id: new_category.id
        )

        if new_template.new_record?
          new_template.position = (new_category.one_on_one_agenda_templates.maximum(:position) || 0) + 1
          new_template.save!
          puts "Created new custom template under '#{category_name}' for account_id: #{account_id}."
        end
      end
    end     
    puts 'CSV Parsed and Data Processed'
  end
  
  desc 'upload_kr_to_existing_goals', 'Uploads key results to existing goals'
  def upload_kr_to_existing_goals(csv_file_name, account_id, admin_employee_id)

    csv_file_key = "okr/#{csv_file_name}"
    s3 = Aws::S3::Resource.new(region: ENV['AWS_REGION'])
    obj = s3.bucket(ENV['AWS_S3_BUCKET']).object(csv_file_key)
    obj_file = obj.get.body

    goals_hash = {}

    goal_progress_hash = {
      'Default progress' => 1,
    }

    goal_progress_status_hash = {
      'Not started' => 0,
      'On track' => 1,
      'Behind' => 2,
      'Completed' => 4,
      'Achieved' => 4,
    }

    goal_activity_progress_status_hash = {
      'Not started' => "not_started",
      'On track' => "on_track",
      'Behind' => "behind",
      'Completed' => "achieved",
      'Achieved' => "achieved",
    }

    admin = Employee.find(admin_employee_id)
    account = Account.find(account_id)
    row_count = 0
    CSV.parse(obj_file, { encoding: "UTF-8", headers: true, header_converters: [:downcase, :symbol], converters: :all}) do |row|
      # puts row_count
      # puts row[:parent_goal_title]
      puts row
      parent_goal_title = row[:parent_goal_title]&.gsub(/\A[[:space:]]+|[[:space:]]+\z/, '').downcase
      # puts row[:departments]
      department_names = row[:departments]&.split(',')&.map(&:strip) || []
      # puts department_names
      departments = Department.where(name: department_names, account_id: account_id)

      if goals_hash[parent_goal_title].nil?
        if parent_goal_title =~ /^\d/
          # Query for company goal
          parent_goal = Goal.find_by('LOWER(TRIM(title)) = ?', parent_goal_title)
        else
          parent_goal = Goal.find_by('LOWER(TRIM(title)) = ? AND goal_type = ? AND goal_cycle_id = ?', parent_goal_title, 1, 207022)
        end
        parent_goal_id = parent_goal.id
      else
        parent_goal_id = goals_hash[parent_goal_title].to_i
      end

      puts "Row Count: #{row_count}"
      puts parent_goal_id

      if parent_goal_id
        puts "Parent Goal: #{parent_goal_id}"
        goals_hash[parent_goal_title] = parent_goal_id
        # Add key result details to the parent goal
        key_result_title = row[:goal_title]&.gsub(/\A[[:space:]]+|[[:space:]]+\z/, '')
        key_result_description = row[:description]&.gsub(/\A[[:space:]]+|[[:space:]]+\z/, '')
        # Add owners to the goal
        owner_emails = row[:owner_emails]&.split(',')&.map(&:strip) || []
        owner_users = User.where(email: owner_emails)
        owners = Employee.where(user: owner_users)
        
        request_params = {
          goal: {
            title: key_result_title,
            added_by_id: admin.id,
            description: key_result_description,
            visibility: 'open',
            due_date: DateTime.strptime(row[:end_date], '%m/%d/%Y').utc,
            goal_cycle_id: 207022,
            goal_type: 'department',
            objective_type: 'key_result',
            start_date: DateTime.strptime(row[:start_date], '%m/%d/%Y').utc,
            account_id: account_id,
            employee_goals_attributes: owners.map { |owner| { employee_id: owner.id } },
            goal_hierarchies: [
              { objective_type: 1, parent_id: parent_goal_id }
            ],
            goal_progress_attributes: {
              goal_progress_type_id: goal_progress_hash[row[:type]],
              progress_start: row[:starting_value].to_f,
              progress_target: row[:target_value].to_f,
              current_progress: row[:current_progress_value].to_f,
              progress_status: goal_progress_status_hash[row[:progress_status]],
            },
            goals_departments_attributes: departments.map { |department| { department_id: department.id} },
            has_milestone_goals: false,
            has_milestones: false,
            milestone_split_type: nil,
            goal_milestones_attributes: [],
            visibility_params: [],
            goal_children: [],
          }
        }.with_indifferent_access

        puts "Constructed request params for key result '#{key_result_title}' under parent goal '#{parent_goal_title}':"
        pp request_params
        service = CoreOkr::CreateGoal::Factory.call(account, admin, request_params[:goal])
        puts service

        if service[:success]
          goal = service[:goal]
          goals_hash[key_result_title.downcase] = goal.id
          puts "Key Result with title '#{key_result_title}' created successfully under parent goal '#{parent_goal_title}'"
          CascadeAncestorsProgress.call(goal.id, admin.id, goal.ancestors.ids, nil, 'creation')
          puts "Ancestors Progress Updated for goal '#{goal.id}'"

          puts "Adding Goal Activities"
          if !row[:comments_progress].nil?
            goal_activities_params = {
              activity_type: "comment",
              mentions: [],
              status: goal_activity_progress_status_hash[row[:progress_status]],
              completed_value: nil,
              description: convert_to_html(row[:comments_progress]),
              description_json: convert_to_json(row[:comments_progress]),
              system_comment: false
            }.with_indifferent_access
            puts "Goal Activity Params: #{goal_activities_params}"
            CoreOkr::Checkin::Factory.call(
              goal,
              admin,
              goal_activities_params,
              {
                milestone_checkin: goal_activities_params[:milestone_checkin],
                okr_update_id: goal_activities_params[:okr_update_id],
              },
            )
          end
        end
      else
        # puts "Parent goal not found: #{parent_goal_title}"
      end
      row_count += 1
    end
    # puts "Total rows processed: #{row_count}"
  end

  desc 'copy_system_records_for_custom_suggestion_box', 'Copy system categories from suggested_talking_point_categories table and their related system talking points from one_on_one_agenda_templates table for custom one-on-one suggestion box'
  def copy_system_records_for_custom_suggestion_box(account_id = nil)
    record = OneOnOneSetting.find_by(account_id: account_id)

    if record.nil?
      puts "one_on_one_setting for account with id: #{account_id} not found."
    elsif record.suggested_talking_points_box_type == 'system'
      puts "Failed to copy system template because company with id: #{account_id} has suggested_talking_points_box_type value as system please change it to custom"
    else
      categories = SuggestedTalkingPointCategory.where(is_system: true).where.not(category_name: 'Recommended')

      last_category = SuggestedTalkingPointCategory.where(account_id: account_id, is_system: false).order('position DESC').first
      i = last_category ? (last_category.position || 0) + 1 : 1
      
      categories.each do |category|
        attributes = {
          account_id: account_id,
          category_name: category.category_name,
          position: i,
          is_system: false,
          parent_category_id: category.id
        }
        
        if SuggestedTalkingPointCategory.exists?(attributes.except(:position))
          new_category = SuggestedTalkingPointCategory.find_by(attributes.except(:position))
          puts "Custom category with name: #{category.category_name} already exists for account_id: #{account_id}"
        else
          # Create a custom category
          new_category = SuggestedTalkingPointCategory.create!(attributes)
          puts "Created new custom category with ID #{new_category.id} for account_id: #{account_id}"
        end

        if new_category.persisted?
          templates = category.one_on_one_agenda_templates.where(is_system: true)
          last_template = new_category.one_on_one_agenda_templates.order('position DESC').first
          j = last_template ? (last_template.position || 0) + 1 : 1

          templates.each do |template|
            attributes = {
              category: new_category.category_name,
              talking_point: template.talking_point,
              talking_point_type: template.talking_point_type,
              parent_agenda_id: template.id,
              bookmark_count: 0,
              manually_recommended_at: nil,
              account_id: account_id,
              is_system: false,
              position: j,
              suggested_talking_point_category_id: new_category.id
            }

            if OneOnOneAgendaTemplate.exists?(attributes.except(:position))
              # Record already exists
              new_template = OneOnOneAgendaTemplate.find_by(attributes.except(:position))
              puts "Custom template with ID #{new_template.id} already exists under #{category.category_name} for account_id: #{account_id}"
            else
              # Create a custom template
              new_template = OneOnOneAgendaTemplate.create!(attributes)
              puts "Created new custom template with ID #{new_template.id} for account_id: #{account_id}"
            end

            if !new_template.persisted?
              puts "Failed to create new custom template: #{new_template.errors.full_messages.join(', ')}"
            end

            j += 1
          end
        else
          puts "Failed to create new custom category: #{new_category.errors.full_messages.join(', ')}"
        end
        i += 1
      end
    end
  end

  desc 'one_on_one_suggestion_box_initial_db_setup', 'Populate suggested_talking_point_categories table with system categories present in the one_on_one_agenda_template table and update suggested_talking_point_category_id in one_on_one_agenda_template table'
  def one_on_one_suggestion_box_initial_db_setup
    SuggestedTalkingPointCategory.find_or_create_by(category_name: 'Recommended', account_id: nil, is_system: true)  
    distinct_categories = OneOnOneAgendaTemplate.where(is_system: true).select(:category).distinct.pluck(:category)
    distinct_categories.each do |category_name|
      SuggestedTalkingPointCategory.find_or_create_by(category_name: category_name, is_system: true)  
    end

    category_id_map = SuggestedTalkingPointCategory.where(is_system: true).pluck(:category_name, :id).to_h
    talking_points = OneOnOneAgendaTemplate.where(is_system: true)

    talking_points.each do |agenda_template|
      next unless agenda_template.category.present?

      category_id = category_id_map[agenda_template.category]

      if category_id.nil?
        new_category = SuggestedTalkingPointCategory.create!(category_name: agenda_template.category, is_system: true)
        category_id_map[agenda_template.category] = new_category.id
        category_id = new_category.id
      end

      agenda_template.update(suggested_talking_point_category_id: category_id)
    end
  end

  desc 'add_ikr_tag', 'Add IKR tags for Redbus Approved Review Goals'
  def add_ikr_tag
    account = Account.find(300)
    goals = Goal.joins(:reviewee_goals).where(account: account, reviewee_goals: {exclude: false}).where.not(reviewee_goals: {approver_id: nil})
    tag = CustomTag.find_or_create_by!(name: 'IKR', account_id: account.id, colour: '#FF0000', added_by: account.admins.first)
    goals.each do |goal|
      CustomTagging.find_or_create_by(custom_tag: tag, taggable: goal)
    end
  end

  desc 'remove_ikr_tag', 'Delete IKR tags for all accounts except Redbus'
  def remove_ikr_tag
    CustomTag.where(name: 'IKR').where.not(account_id: 300).discard_all
  end

  desc 'update_lagging_emp_goals_settings', 'Update goal settings with next reminder in the past'
  def update_lagging_emp_goals_settings
    settings = GoalsSetting.where.not(employee_id: nil).where('next_reminder_at < ?', Time.current)
    Parallel.each(settings, in_threads: 4) do |setting|
      pp "Updating setting - #{setting.id}"
      freq = setting.reminder_frequency
      reminder_day = setting.reminder_day_before_type_cast || 1
      reminder_time = setting.time
      last_sent = setting.reminder_last_sent_on || setting.next_reminder_at
      timezone = setting.employee.timezone || setting.account.timezone || 'Europe/London'
      next_notification_time =  NextOkrNotificationTime.call(reminder_day, reminder_time, last_sent, timezone, freq, setting.day_of_month)
      until next_notification_time > Time.current do
        last_sent = next_notification_time
        next_notification_time = NextOkrNotificationTime.call(reminder_day, reminder_time, last_sent, timezone, freq, setting.day_of_month)
      end

      setting.update!(reminder_last_sent_on: last_sent, next_reminder_at: next_notification_time)
    end
  end

  desc 'create_emp_goals_settings', 'Create goals settings for employees'
  def create_emp_goals_settings
    accounts = Account.all
    Parallel.each(accounts, in_threads: 4) do |account|
      goals_setting = account.company_goals_setting
      next if goals_setting.nil?

      account.employees.each do |employee|
        next_reminder_at = calc_next_reminder_at(goals_setting, employee)
        emp_setting = goals_setting.dup
        emp_setting.department_id = nil
        emp_setting.employee_id = employee.id
        emp_setting.next_reminder_at = next_reminder_at if next_reminder_at != goals_setting.next_reminder_at
        emp_setting.save
        pp 'Creating employee goals setting'
      end
      next_reminder_at = calc_next_reminder_at(goals_setting, nil)
      goals_setting.update(next_reminder_at: next_reminder_at) if next_reminder_at != goals_setting.next_reminder_at
    end
  end

  no_commands do
    # If next_notification date is in the past, account notifications have not been going out,
    # reset next notification date from current date.
    def calc_next_reminder_at(setting, emp)
      tz = emp&.timezone || setting.account.timezone || 'Europe/London'
      if setting.next_reminder_at.nil? || setting.next_reminder_at < Time.current
        day_of_week = GoalsSetting.reminder_days[setting.reminder_day]

        NextOkrNotificationTime.call(
          day_of_week,
          setting.time,
          setting.reminder_last_sent_on,
          tz,
          setting.reminder_frequency,
          setting.day_of_month,
          false,
        )
      else
        day = setting.next_reminder_at
        time = Time.parse(setting.time)
        tz_offset = ActiveSupport::TimeZone[tz].formatted_offset
        Time.new(day.year, day.month, day.day, time.hour, time.min, time.sec, tz_offset)
      end
    end
  end

  desc 'create_home_page_actions', 'create home page actions for employees'
  def create_home_page_actions
    EmployeeChat.where('expires_at > ?', Time.zone.now).each do |ec|
      HomePageAction.find_or_create_by!(employee_id: ec.employee_id, actionable: ec, start_date: ec.schedule.when_to_run, end_date: ec.schedule.when_to_run + 2.weeks,
        action_type: 'take_survey')
    end

    EmployeeBizReviewActionItem.joins(biz_review_action_item: :biz_review_schedule)
      .where('biz_review_schedules.cycle_end_date > ?', Time.zone.now).each do |ebrai|

        HomePageAction.find_or_create_by!(employee_id: ebrai.employee_id, actionable: ebrai.biz_review_action_item,
          start_date: ebrai.biz_review_action_item.created_at, end_date: ebrai.biz_review_action_item.biz_review_schedule.cycle_end_date,
          action_type: 'biz_review_action_item')
    end

    SuperListItem.joins(:one_on_one_action_items)
      .where('one_on_one_action_items.complete = false')
      .where('super_list_items.created_at > ?', 1.month.ago).each do |sli|
        HomePageAction.find_or_create_by!(employee_id: sli.manager_id, actionable: sli, start_date: sli.created_at, end_date: sli.created_at + 1.month,
          action_type: 'one_on_one_action_item')
    end
  end

  desc 'disable_kpis', 'disable kpis for accounts without any kpi'
  def disable_kpis
    accounts = Account.where(enable_kpi: true)
    if Rails.env.demo?
      accounts.update_all(enable_kpi: false)
    else
      Parallel.each(accounts, in_threads: 4) do |account|
        if account.kpis.blank?
          account.update(enable_kpi: false)
        end
        p 'Running . . .'
      end
    end
  end

  desc 'save_inactive_display_columns_to_card', 'updates display_columns property of okr cards with inactive columns'
  def save_inactive_display_columns_to_card
    cards = BizReviewCard.joins(:biz_review_widget).where(biz_review_widgets: { widget_type: 'okr_and_project_details' })
    Parallel.each(cards, in_threads: 4) do |card|
      save_inactive_columns(card)
    end
  end

  no_commands do
    def save_inactive_columns(card)
      pp "Processing BizReviewCard - #{card.id}"
      acc = card&.biz_review_schedule&.biz_review&.account
      if acc
        all_cols = MetadataField.default_biz_review_columns(acc.id)
        display_cols = all_cols.map do |col_data|
          { id: col_data['id'], active: col_data['id'].in?(card.properties['display_columns']) }
        end
      else
        display_cols = card.properties['display_columns'].map do |col_id|
          { id: col_id, active: true }
        end
      end
      card.properties['display_columns'] = display_cols
      card.save!
    end
  end

  desc 'update_display_columns_property', 'update display_columns property for okr_and_project_details widget'
  def update_display_columns_property
    cards = BizReviewCard.joins(:biz_review_widget).where(biz_review_widgets: { widget_type: 'okr_and_project_details' })
    Parallel.each(cards, in_threads: 4) do |card|
      update_display_columns(card)
    end
  end

  no_commands do
    def update_display_columns(card)
      default_fields = MetadataField.where(field_source: 'peoplebox', metadata_type: 'goal_and_project_metadata')
      default_field_names = ['Title', 'Current Progress', 'Progress Breakdown', 'Comments', 'Department Name', 'Due Date']
      if acc.nil?
        card.properties['display_columns'] = default_fields.order(
          "CASE metadata_fields.field_name #{default_field_names.map.with_index do |name, index|
          "WHEN '#{name}' THEN #{index}"
        end.join(" ")} ELSE #{default_field_names.size} END").pluck(:id)
      else
        card_properties = card.properties.deep_symbolize_keys

        cc_ids = card_properties[:custom_checkin][:custom_checkin_fields] + card_properties[:custom_checkin][:non_rca_checkin_fields]
        cc_field_names = CustomCheckinField.where(id: cc_ids).pluck(:field_name)
        gm_fields = MetadataField.where(account: acc, field_name: cc_field_names, metadata_type: 'goal_metadata').pluck(:id)

        okr_metafields = card_properties[:filters][:okr_metafields]
        gm_fields2 = MetadataField.where(account: acc, field_name: okr_metafields, metadata_type: 'goal_metadata').pluck(:id)

        project_metafields = card_properties[:filters][:project_primary_metafields] + card_properties[:filters][:project_secondary_metafields]
        pm_fields = MetadataField.where(account: acc, field_name: project_metafields, metadata_type: 'project_metadata').pluck(:id)

        active_display_columns = default_fields.pluck(:id) + gm_fields + gm_fields2 + pm_fields

        display_columns = MetadataField.default_biz_review_columns(acc.id)
          .where(id: active_display_columns)
          .order(
          Arel.sql(
            "CASE
              WHEN id IN (#{(active_display_columns).join(', ')}) THEN 2
              WHEN field_name IN ('#{default_field_names.join("', '")}') THEN 1
              ELSE 3
              END"
          ))

        card.properties['display_columns'] = display_columns.pluck(:id)
      end
      card.save!
    end
  end

  desc 'create_default_custom_fields', 'add_peoplebox_metadata fields'
  def create_default_custom_fields
    params = [
      {field_name: 'Title', sortable: false, hidable: false },
      {field_name: 'Current Progress', sortable: true, hidable: true },
      {field_name: 'Progress Breakdown', sortable: true, hidable: true },
      {field_name: 'Comments', sortable: true, hidable: true },
      {field_name: 'Department Name', sortable: true, hidable: true },
      {field_name: 'Due Date', sortable: true, hidable: true },
    ]
    params.each do |param|
      MetadataField.find_or_create_by(param.merge(field_type: 'text', metadata_type: 'goal_and_project_metadata', field_source: 'peoplebox'))
    end
  end

  desc 'join_custom_values_and_biz_rev_schedules', 'creates feature_metadata_values join for existing biz reviews'
  def join_custom_values_and_biz_rev_schedules
    cards = BizReviewCard.joins(:biz_review_widget).where(biz_review_widgets: { widget_type: 'okr_and_project_details' })
    Parallel.each(cards, in_threads: 4) do |card|
      create_join(card)
    end
  end

  no_commands do
    def create_join(card)
      pp "Now Processing BizReviewCard - #{card.id}"
      schedule = card.biz_review_schedule
      account = schedule&.biz_review&.account
      return if account.nil?

      card_response = if card.saved_response.present? && schedule.status != 'draft'
                        card.saved_response
                      else
                        employee = account.admins.first || account.employees.first
                        return if employee.nil?

                        OkrAndProjectDetailsBizReviewCard.call(card, employee).as_json.deep_stringify_keys
                      end
      card_response['all_goals'].each do |goal_id, goal_props|
        custom_fields = goal_props['custom_fields']
        goal = Goal.with_discarded.find_by(id: goal_id)
        pp "Goal not found" if goal.blank?
        return if goal.blank?

        create_feature_metadata_values(goal, custom_fields, schedule)
      end

      level_two_goals = card_response['goal_children'].values.flatten
      level_two_goals.each do |goal_id|
        return if card_response['all_goals'][goal_id]['children_present'] == false && card_response['all_goals'][goal_id]['projects_present'] == false

        save_descendants_feature_metadata_values(goal_id, card, schedule)
      end
    end

    def save_descendants_feature_metadata_values(goal_id, card, schedule)
      goal = Goal.with_discarded.find_by(id: goal_id)
      return if goal.blank?

      delta_start_date = schedule.cycle_start_date&.to_datetime
      delta_end_date = schedule.cycle_end_date&.to_datetime
      biz_review_card = card
      biz_review_card_type = card&.biz_review_widget&.widget_type

      goal.descendants.with_discarded.each do |goal|
        goal_response = GoalShowSerializer.new(goal, { data: { delta_start_date: delta_start_date, delta_end_date: delta_end_date,
          biz_review_card: biz_review_card, biz_review_card_type: biz_review_card_type }}).as_json.deep_stringify_keys
        custom_fields = goal_response['custom_fields']
        create_feature_metadata_values(goal, custom_fields, schedule)
      end
    end

    def create_feature_metadata_values(goal, custom_fields, schedule)
      custom_fields['okr_metafields'].each do |id, value|
        m_field = MetadataField.find_by(field_name: value['title'], metadata_type: 'goal_metadata', field_type: value['type'], account_id: goal.account_id)
        m_value = m_field.metadata_values.where(owner_id: goal.id, owner_type: 'Goal', value: value['display_value']).last
        FeatureMetadataValue.create!(metadata_value: m_value, feature: schedule) if m_value
        pp "Created FeatureMetadataValue for Field: #{m_field.field_name}, Schedule: #{schedule.id}" if m_value
      end
      custom_fields['project_primary_metafields'].each do |id, value|
        m_field = MetadataField.find_by(field_name: value['title'], metadata_type: 'project_metadata', field_type: value['type'], account_id: goal.account_id) ||
          MetadataField.find_by(id: id, metadata_type: 'project_metadata', field_type: value['type'], account_id: goal.account_id)
        m_value = m_field.metadata_values.where(owner_id: goal.id, owner_type: 'Goal', value: value['display_value']).last
        FeatureMetadataValue.create!(metadata_value: m_value, feature: schedule) if m_value
        pp "Created FeatureMetadataValue for Field: #{m_field.field_name}, Schedule: #{schedule.id}" if m_value
      end
      custom_fields['project_secondary_metafields'].each do |id, value|
        m_field = MetadataField.find_by(field_name: value['title'], metadata_type: 'project_metadata', field_type: value['type'], account_id: goal.account_id) ||
           MetadataField.find_by(id: id, metadata_type: 'project_metadata', field_type: value['type'], account_id: goal.account_id)
        m_value = m_field.metadata_values.where(owner_id: goal.id, owner_type: 'Goal', value: value['display_value']).last
        FeatureMetadataValue.create!(metadata_value: m_value, feature: schedule) if m_value
        pp "Created FeatureMetadataValue for Field: #{m_field.field_name}, Schedule: #{schedule.id}" if m_value
      end
    end
  end

  desc 'update_metadata_field_source', 'update field source value for all metadata fields'
  def update_metadata_field_source
    # remove duplicate fields
    MetadataField.select("MIN(id) AS id", :field_name, :account_id, :metadata_type, :field_source).group(:field_name, :account_id, :metadata_type).having('COUNT(*) > 1').each do |field|
      fields_to_remove = MetadataField.where(field_name: field.field_name, account_id: field.account_id, metadata_type: field.metadata_type, field_source: field.field_source).where.not(id: field.id)
      pp "#{field.id} => #{fields_to_remove.pluck(:id, :field_name)}"
      fields_to_remove.destroy_all
    end
    # update field_source
    MetadataField.where(metadata_type: 'project_metadata').each do |field|
      source = nil
      field.metadata_values.includes(:owner).each do |val|
        source = val.owner&.parents&.first&.project_integration_field&.integration_config&.integration&.name&.downcase&.gsub(' ', '_')
        break unless source.nil?
      end
      pp "Updating field_source for #{field.field_name}"
      field.update(field_source: source)
    end
    MetadataField.where(metadata_type: 'goal_metadata').update_all(field_source: 'user')
  end

  desc 'migrate_custom_fields_to_metadata_fields', 'copy custom fields and values to metadata fields and values'
  def migrate_custom_fields_to_metadata_fields
    CustomCheckinField.all.each do |field|
      pp "Migrating Field #{field.id} - #{field.field_name}"
      # create field
      field_properties = {
        field_name: field.field_name,
        field_type: field.field_type,
        account_id: field.owner_id,
        metadata_type: 'goal_metadata',
        field_source: 'user'
      }
      created_field = MetadataField.find_or_create_by(field_properties)
      if created_field
        # create acls for created_field
        admins = created_field.account.admins

        Acl.create!(
          resource_type: "MetadataField",
          resource_id: created_field.id,
          actorable_type: "Account",
          actorable_id: created_field.account_id,
          permission_type: "edit",
          created_by_id: admins.first&.id || created_field.account.employees.first.id)

        # create options
        field.custom_checkin_options.each do |option|
          created_field.metadata_options.find_or_create_by(value: option.value, text_value: option.value)
        end

        # create values
        field.custom_checkin_values.each do |value|
          goal = value&.goal_activity&.goal
          next if goal.blank?
          m_value = MetadataValue.create!(
            value: value.value,
            text_value: value.value,
            goal_activity_id: value.goal_activity_id,
            metadata_field_id: created_field.id,
            owner_id: goal.id,
            owner_type: 'Goal')

          # create metadata_value_metadata_options
          value.custom_checkin_options.each do |option|
            m_option = created_field.metadata_options.find_or_create_by!(value: option.value)
            m_value.metadata_options << m_option
          end
        end
      end
    end
  end

  desc 'update_metadata_values', 'copy values to field_type specific columns'
  def update_metadata_values
    MetadataValue.joins(:metadata_field).where(metadata_fields: {field_type: 'text'}).update_all('text_value = value')
    MetadataValue.joins(:metadata_field).where(metadata_fields: {field_type: 'link'}).update_all('link_value = value')
    MetadataValue.joins(:metadata_field).where(metadata_fields: {field_type: 'employee_id'}).update_all('employee_id_value = value')
    # MetadataValue.joins(:metadata_field).where(metadata_fields: {field_type: 'number_field'}).update_all('number_field_value = value')
    MetadataValue.joins(:metadata_field).where(metadata_fields: {field_type: 'date_field'}).each do |m_value|
      m_value.update(date_field_value: m_value.value&.to_datetime)
    end
    MetadataValue.joins(:metadata_field).where(metadata_fields: {field_type: 'single_enum'}).update_all('text_value = value')
    MetadataOption.update_all('text_value = value')
  end

  # 31-05-2023 ACL Notifications
  desc 'update_acl_created_by', 'add created_by to all acls in db'
  def update_acl_created_by
    BizReview.with_discarded.each do |biz_rev|
      admin = biz_rev.account.admins.first || biz_rev.account.employees.first
      next if admin.nil?

      biz_rev.acls.update_all(created_by_id: admin.id)
    end
  end

  desc 'add_show_qualitative_updates_to_okr_cards', 'sets biz review okr cards property - show_qualitative_updates to true'
  def add_show_qualitative_updates_to_okr_cards
    BizReviewCard.joins(:biz_review_widget).where(biz_review_widgets: { widget_type: 'okr_and_project_details' }).find_each do |card|
      card.properties[:show_qualitative_updates] = true
      card.save!
    end
  end

  desc 'add_default_progress_values', 'will populate the existing card project details'
  def add_default_progress_values
    props_value = { default_progress_values: {
      show_native_project_progress: true,
      show_custom_project_progress: true,
    } }
    BizReviewCard.joins(:biz_review_widget).where(biz_review_widgets: { widget_type: 'project_details' }).each do |card|
      card_props = card.properties
      card_props = card_props.merge(props_value)
      card.properties = card_props
      card.save
      puts "Card - #{card.id} updated"
    end
  end
  desc 'create_kpi_units', 'will add kpi units in database'
  def create_kpi_units
    units = [
      { name: 'Number', symbol: nil },
      { name: 'Percentage', symbol: '%' },
      { name: 'Dollar', symbol: '$' },
      { name: 'Indian Rupee', symbol: '₹' },
      { name: 'Euro', symbol: '€' },
      { name: 'GBP', symbol: '£' },
      { name: 'BDT', symbol: '৳' },
      { name: 'SAR', symbol: '﷼' },
      { name: 'AED', symbol: 'د.إ' },
    ]
    units.each { |unit| KpiUnit.find_or_create_by(name: unit[:name]).update(symbol: unit[:symbol]) }
  end


  desc 'add_biz_review_card_controls', 'will populate existing biz review cards controls '
  def add_biz_review_card_controls
    default = {
      can_be_deleted: false,
      title_editable: true,
      card_type: 'system',
      }
    BizReviewCard.where(card_control: nil).or(BizReviewCard.where.not(card_control: default)).each do |card|
      card.update(card_control: default)
      puts "Discarding Card - #{card.id}"
    end
  end
  desc 'delete_biz_review_panels', ' will update bizreiews in draft state and it needs AccountID and accepts string of panel_titles'
  # thor data_migrate:delete_biz_review_panels 123, "Highlights, Action Items, Projects Summary"
  def delete_biz_review_panels(account_id, panel_titles)
    panel_titles = panel_titles.split(',').map(&:strip) if panel_titles.present?
    biz_review_schedules = BizReviewSchedule.joins(:biz_review).where(biz_reviews: { account_id: account_id }, status: 0)
    biz_review_cards = BizReviewCard.all.where(biz_review_schedule_id: biz_review_schedules)
    biz_review_cards.each do |card|
      puts "Discarding Card - #{card.id}"
      if panel_titles.include? card.title

        card.discarded_by_id = card.biz_review_schedule.biz_review.created_by_id
        card.save!
        card.discard
      end
    end
  end
  desc 'update_biz_review_card_layout', 'Update existing biz review cards with a layout'
  def update_biz_review_card_layout
    AddBizReviewWidgets.call
    BizReviewSchedule.joins(:biz_review).where(biz_reviews: { account_id: 1712 }).update_all(layout_editable: false)
    biz_review_cards = BizReviewCard.all
    biz_review_cards.each do |card|
      puts "Updating layout for Card - #{card.id}"
      next if card&.biz_review_widget.properties.key?('layout') == false

      layout = if card&.biz_review_widget.properties.key?('layout')
                card&.biz_review_widget[:properties]['layout']
              end

      layout = layout.merge({i: card&.id})
      card.properties['layout'] = layout
      card.save!
    end
  end
  desc 'update_checkin_time', 'Update goal activity checkin_time to created_at for all non-custom checkins'
  def update_checkin_time
    GoalActivity.where.not(source: ['custom_checkin', 'comment_source']).update_all("checkin_time = created_at")
  end

  desc 'add_slack_email_metebases', 'Metabase reports where params has reveiw cycle id'
  def add_slack_email_metebases
    ReviewCycle.where(id: MetabaseReport.where(reportable_type: 'ReviewCycle').map(&:reportable_id)).each do |review_cycle|
      metabase_reports = [
        { title: 'Slack Logs', metabase_dashboard_id: ENV['META_SLACK_LOG_ID'], height: 4000 },
        { title: 'Email Logs', metabase_dashboard_id: ENV['META_EMAIL_LOG_ID'], height: 4000 },
      ]
      metabase_reports.each do |metabase_report|
        next if metabase_report[:metabase_dashboard_id].nil?
        
        report_data = {
          title: metabase_report[:title],
          metabase_dashboard_id: metabase_report[:metabase_dashboard_id],
          height: metabase_report[:height],
          account: review_cycle.account,
          dashboard_type: 'dashboard',
          params: { review_cycle_id: review_cycle&.id },
          reportable: review_cycle,
        }
        MetabaseReport.find_or_create_by(report_data)
      end
    end
  end

  desc 'update_admin_permissions', ''
  def update_admin_permissions
    Employee.admin.update(account_admin: true, goal_admin: true)
  end

  desc 'create_review_cycle_phases', 'Create Review cycle phases'
  def create_review_cycle_phases
    ReviewCycle.all.each do |review_cycle|
      next if review_cycle.review_cycle_phases.present?

      PerformanceReview::CreateReviewCyclePhases.delay(queue: 'low').call(review_cycle&.id)
    end
  end

  desc 'populate_metebases', 'Metabase reports where params has reveiw cycle id'
  def populate_metebases
    ReviewCycle.where.not(id: MetabaseReport.where(reportable_type: 'ReviewCycle').map(&:reportable_id)).each do |r|
      break if ENV['META_ANALYTICS_ID'].nil?

      report_data = {
        title: 'Analytics',
        metabase_dashboard_id: ENV['META_ANALYTICS_ID'].to_i,
        height: 4000,
        account: r.account,
        dashboard_type: 'dashboard',
        params: { review_cycle_id: r.id },
        reportable: r,
      }
      MetabaseReport.create(report_data)
    end
  end
  desc 'capitalize_names ACCOUNT_ID', 'Capitalize names for people in the company'
  def capitalize_names(account_id)
    account = Account.find(account_id)
    puts "Capitalizing names for #{account.company_name}"

    account.employees.each do |e|
      e.full_name = e.full_name.titleize
      e.first_name = e.first_name.titleize
      e.save!
    end
  end

  desc 'Fix dates in csv account_id', 'Expects account_id'
  def fix_dates(_account_id)
    @employees = Employee.where('date_of_joining < ?', 1000.years.ago)
    @employees.each do |employee|
      date_of_joining = employee.date_of_joining
      year = date_of_joining.year + 2000
      employee.date_of_joining = employee.date_of_joining.change(year: year)
      employee.save
    end
  end

  desc 'create_employee_chat tasks needs', 'account_idCreate employee_chat from campaign and schedules'
  def create_employee_chat(account_id)
    @account = Account.find(account_id)
    @schedule = Schedule.where(account: @account, parent_schedule: nil).first
    @campaign = Campaign.where(account: @account).first
    map_campaign_to_employee_chat(@account, @schedule, @campaign)
    @campaign = Campaign.where(account: @account).second
    if @campaign.present?
      @schedule = Schedule.where(account: @account, parent_schedule: nil).second
      map_campaign_to_employee_chat(@account, @schedule, @campaign)
    end
  end

  desc 'fetch_file_from_s3', 'needs csv_file_key and account_id'
  def fetch_file_from_s3(csv_file_name, account_id)
    company_data = []
    csv_file_key = "csv/#{csv_file_name}"
    s3 = Aws::S3::Resource.new
    bucket = s3.bucket('peoplebox-data')
    bucket.objects.each do |obj|
      next unless obj.key == csv_file_key

      puts 'File FOUND'
      obj_file = obj.get.body
      CSV.parse(obj_file, { encoding: 'UTF-8', headers: true, header_converters: :symbol, converters: :all }) do |row|
        company_data << row.to_hash.merge!({ account_id: account_id.to_i })
      end
      puts 'CSV Parsed'
    end
    data_update(company_data, account_id)
    puts 'Updating Employee Details Completed'
  end

  desc 'data_update task needs', 'company_data, account_id'
  def data_update(company_data, account_id)
    @new_employees = []
    puts 'Checking Employees Info for updating'
    company_data.each do |employee_detail|
      puts employee_detail
      @employee = find_employee(employee_detail, account_id)
      if @employee.present?
        UpdateEmployeeDetails.call(employee_detail, account_id, @employee)
      else
        FromCsvEmployeeCreator.call(employee_detail)
        @new_employee = Employee.where(account_id: account_id,
                                       company_employee_id: employee_detail[:company_employee_id]).first
        @new_employees.push(@new_employee.id)
      end
    end

    puts 'Updating Managers'
    company_data.each do |employee_detail|
      @employee = Employee.where(account_id: account_id,
                                 company_employee_id: employee_detail[:company_employee_id]).first
      next if @employee.nil?

      if @new_employees.include? @employee.id
        unless employee_detail[:reporting_manager_employee_id].nil?
          AssignManagerToEmployee.call(employee_detail, account_id)
        end
      else
        UpdateEmployeeManager.call(employee_detail, account_id, @employee)
      end
    end
  end

  desc 'employee_left task needs csv filename and account_id', './lib/tasks/filename.csv'
  def employee_left(csv_filename, account_id)
    account = Account.find(account_id)
    company_data = []
    @new_employees = []
    puts 'Reading CSV'
    CSV.foreach(csv_filename,
                { encoding: 'UTF-8', headers: true, header_converters: :symbol, converters: :all }) do |row|
      company_data << row[0]
    end
    Employee.where(company_employee_id: company_data, account: account).update(date_of_exit: DateTime.now)
  end

  desc 'Convert emoji hex value to emoji in message', 'args not required'
  def convert_hex_to_emoji
    Message.all.each do |message_obj|
      next unless message_obj.message.match(/1f6/)

      if message_obj.message.include?('1f61e')
        message_obj.message = message_obj.message.gsub('1f61e', "\u{1f61e}")
      elsif message_obj.message.include?('1f610')
        message_obj.message = message_obj.message.gsub('1f610', "\u{1f610}")
      elsif message_obj.message.include?('1f603')
        message_obj.message = message_obj.message.gsub('1f603', "\u{1f603}")
      end
      message_obj.save(validate: false) if message_obj.changed?
    end
  end

  desc 'create_survey_templates needs no', 'parameters'
  def create_survey_templates
    questions = Question.where(status: 'live').where.not(question_type: 'multiple_option')
    questStart = 0
    questEnd = 6
    (1..10).each do |i|
      puts "Creating Template-#{i}"
      template = Template.create!(title: "Template-#{i}")
      puts "Template-#{i} created"
      position = 1
      puts "Adding Questions to Template-#{i}"

      (questStart..questEnd).each do |j|
        question = questions[j]
        TemplateQuestion.create!(template_id: template.id, position: position, question_id: question.id)
        position += 1
      end

      puts "Questions added to Template-#{i}"
      questStart = questEnd + 1
      questEnd = questStart + 6
    end
  end

  desc 'create_template_questions needs no', 'parameters'
  def create_template_questions
    templates = [
      {
        name: 'Monthly Survey',
        question_ids: [4, 247, 346, 291, 248, 249, 254, 278, 371, 256, 285, 263, 264, 272, 471],
      },
      {
        name: 'Q1 Survey',
        question_ids: [4, 247, 248, 249, 291, 361, 367, 360, 303, 391, 392, 254, 256, 278, 299, 341, 370, 371, 285,
                       263, 264, 286, 272, 373, 252],
      },
      {
        name: 'Q2 Survey',
        question_ids: [346, 273, 274, 369, 389, 250, 251, 377, 269, 378, 270, 266, 304, 294, 375, 308, 306, 355, 380,
                       309, 383, 381, 260],
      },
      {
        name: 'Q3 Survey',
        question_ids: [246, 275, 360, 364, 396, 397, 398, 399, 392, 292, 400, 297, 298, 281, 284, 282, 407, 408, 347,
                       265, 374, 314],
      },
      {
        name: 'Q4 Survey',
        question_ids: [4, 247, 248, 249, 254, 256, 285, 264, 250, 251, 389, 392, 292, 396, 399, 404, 405, 308, 306, 266, 304, 284, 297, 298,
                       342],
      },
      {
        name: 'Remote work',
        question_ids: [343, 344, 413, 425, 415, 417, 422, 429, 420, 421, 351, 427, 430, 414, 432],
      },
      {
        name: 'Covid-19',
        question_ids: [348, 349, 353, 354, 356, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443],
      },
      {
        name: 'Manager Support & Career Growth',
        question_ids: [247, 248, 249, 360, 361, 388, 389, 299, 370, 371, 341, 280, 255, 256, 394],
      },
      {
        name: 'Manager Support & Role Clarity',
        question_ids: [301, 361, 364, 365, 388, 389, 392, 369, 373, 374, 302, 285, 286, 288, 289],
      },
      {
        name: 'Role Clarity & Career Growth',
        question_ids: [299, 370, 371, 341, 280, 255, 256, 373, 374, 302, 285, 286, 288, 307],
      },
      {
        name: 'Top Leadership & Culture',
        question_ids: [250, 251, 377, 269, 378, 270, 379, 308, 305, 306, 355, 375, 380, 381, 384],
      },
      {
        name: 'Infrastructure & Culture',
        question_ids: [4, 266, 304, 294, 375, 308, 305, 306, 355, 375, 380, 381, 296],
      },
    ]
    templates.each do |template_info|
      template = Template.create!(title: template_info[:name])
      questions = template_info[:question_ids].uniq
      questions.each_with_index do |question_id, index|
        TemplateQuestion.create!(template: template, position: index + 1, question_id: question_id)
      end
    end
  end

  no_commands do
    def find_employee(employee_detail, account_id)
      employee = Employee.where(account_id: account_id,
                                company_employee_id: employee_detail[:company_employee_id]).first
      if employee.blank?
        user = User.find_by(email: employee_detail[:email])
        if user.present?
          employee = user.employee
        end
      end
      employee
    end
  end

  # 27th December - Subset of audience release
  desc 'update_schedule_respondents needs no', 'parameters'
  def update_schedule_respondents
    Schedule.where(task_type: 'feedback_survey').each do |schedule|
      if !schedule.employee_chats.empty?
        schedule.employee_chats.each do |chat|
          schedule.respondents << chat.employee
        end
      elsif schedule.respondents.empty?
        schedule.account.employees.active.each do |respondent|
          schedule.respondents << respondent
        end
      end
    end
  end

  # 28th May - E-nps question setup
  desc 'unify_enps_questions needs no', 'parameters'
  def unify_enps_questions
    Question.find(1137).update!(question_type: 'enps', account_id: nil)
    # Second enps question is Question ID - 1151
    EmployeeChatQuestion.where(question_id: 1151).update_all(question_id: 1137)
    ScheduleQuestion.where(question_id: 1151).update_all(question_id: 1137)
    Response.where(question_id: 1151).update_all(question_id: 1137)
    BotThread.where(parent_survey_question_id: 1151).update_all(parent_survey_question_id: 1137)
    BotThread.where(question_id: 1151).update_all(question_id: 1137)
    Question.find(1151).destroy
  end

  # 12th July - New Survey Listing
  desc 'update_survey_status', 'parameters'
  def update_survey_status
    # Recurrent Surveys
    Schedule.where(task_type: 'recurrent_survey', scheduled_at: nil).update_all(status: 'draft')
    Schedule.where(task_type: 'recurrent_survey').where.not(scheduled_at: nil, when_to_run: nil).each do |schedule|
      schedule.update(status: 'scheduled') if schedule.one_off_surveys.none?(&:task_executed)
    end
    Schedule.where(task_type: 'recurrent_survey').where.not(scheduled_at: nil, when_to_run: nil).each do |schedule|
      schedule.update(status: 'live') if schedule.one_off_surveys.any?(&:task_executed)
    end
    Schedule.where(task_type: 'recurrent_survey', when_to_run: nil).update_all(status: 'completed')

    # Feedback Surveys
    Schedule.where(task_type: 'feedback_survey', scheduled_at: nil).update_all(status: 'draft')
    Schedule.where(task_type: 'feedback_survey', task_executed: false).where('when_to_run >= ?',
                                                                             Time.zone.now).where.not(scheduled_at: nil).update_all(status: 'scheduled')
    Schedule.where(task_type: 'feedback_survey', task_executed: false).where('when_to_run < ?',
                                                                             Time.zone.now).update_all(status: 'draft')
    Schedule.where(task_type: 'feedback_survey', task_executed: true).where('when_to_run < ?',
                                                                            2.weeks.ago).update_all(status: 'completed')
    Schedule.where(task_type: 'feedback_survey', task_executed: true).where('when_to_run >= ?',
                                                                            2.weeks.ago).update_all(status: 'live')
  end

  # 10 July - Metabase custom reports
  desc 'seed_metabase_reports', 'no params 10 July 2021'
  def seed_metabase_reports
    report_data = [
      { title: 'Usage Reports', metabase_dashboard_id: 36, height: 1400, account: nil },
      { title: 'Goals', metabase_dashboard_id: 38, height: 1200, account: nil },
      { title: '1-on-1s', metabase_dashboard_id: 39, height: 1200, account: nil },
    ]
    report_data.each do |report_datum|
      MetabaseReport.create!(report_datum)
    end
  end

  desc 'create_custom_metabase_reports',
       'title, dashboard_or_question_id, account_id, height, dashboard_type (dashboard/question)'
  def create_custom_metabase_reports(title, dashboard_id, account_id, height, dashboard_type = 'dashboard')
    report_data = {
      title: title,
      metabase_dashboard_id: dashboard_id,
      height: height,
      account_id: account_id,
      dashboard_type: dashboard_type,
    }

    MetabaseReport.create!(report_data)
  end

  # 27th July 2021 - Add Pulse Survey questions
  desc 'add_default_pulse_survey_questions', 'adds default pulse questions to peoplebox question library'
  def add_default_pulse_survey_questions
    questions = Settings.default_pulse_survey_questions
    questions.each do |question_data|
      p "Adding question #{question_data[:position]}"
      driver = Driver.find_by(name: question_data[:driver],
                              account_id: nil) || Driver.create!(name: question_data[:driver],
                                                                 key: question_data[:driver].parameterize(separator: '_'), driver_type: 'System')
      question_params = { driver: driver, question_text: question_data[:question],
                          question_type: question_data[:question_type], key: question_data[:key] }

      question = Question.find_or_create_by(question_params)
    end
  end

  # 3rd Aug 2021 - Checkin v1 (Templates & Template Settings)
  desc 'add_checkin_template_questions', 'adds new questions for checkin to questions library'
  def add_checkin_template_questions
    questions = Settings.checkin_questions
    questions.each do |question_data|
      p "Adding question #{question_data[:position]}"
      q_params = { question_text: question_data[:question], question_type: question_data[:question_type],
                   key: question_data[:key] }
      question = Question.find_or_create_by!(q_params)
      next unless question && question_data.key?(:options)

      question_data[:options].each do |option_data|
        question.options.find_or_create_by(value: option_data[:value], legend: option_data[:value])
      end
    end
  end

  desc 'add_default_account_pulse_template', 'creates default reportee template for account level'
  def add_default_account_pulse_template
    Question.find_by(key: 'pulse_motivation').update(question_text: 'Since last Checkin, my productivity was')
    keys = Settings.checkin_questions.pluck(:key)
    questions = Question.where(key: keys).pluck(:id).map.with_index do |q_id, idx|
      { question_id: q_id, position: idx + 1 }
    end
    accounts = Account.all.pluck(:id)
    templates_params = []
    accounts.each do |account|
      templates_params << { owner_id: account, owner_type: 'Account', template_type: 'reportee_template',
                            pulse_template_questions_attributes: questions }
    end
    PulseTemplate.create(templates_params)
  end

  # 5th September 2021 - Update edited field using jsondiff
  desc 'update_notes_edited', 'updated_notes_edited'
  def update_notes_edited
    OneOnOneNote.where(private: 0, edited: 0).each do |note|
      initial = JSON.parse(note.one_on_one.initial_template.to_json)
      modified = JSON.parse(note.notes_json_text.to_json)
      note.edited = JsonDiff.diff(initial, modified).reject { |x| x['path'] =~ %r{/id$} }.length.positive?
      note.save!
      puts "Updated #{note.id} to edited" if note.edited
      puts "Updated #{note.id} to not-edited" unless note.edited
    rescue StandardError => e
      puts "Exception for #{note.id}"
      puts e
    end
  end

  # 21 Nov 2021 - Identified Surveys
  desc 'identified_survey_bot_message', ''
  def identified_survey_bot_message
    messages =
      ['SALUTATION_IDENTIFIED', 'Hey {{employee_name}}! I am here to collect your feedback']

    messages.each do |_message|
      BotResponse.find_or_create_by!(response_category: messages.first, message: messages.second)
    end
  end

  # 29 Dec 2021 Create Goals Onboarding Checklists for all Admins
  desc 'add_goals_onboarding_checklist_for_admins', "creates goals_onboarding_checklist for all admins that don't have"
  def add_goals_onboarding_checklist_for_admins
    Employee.where(org_role: 'admin').each do |emp|
      return unless emp.goals_onboarding_checklist.nil?

      goal_added = emp.added_by_goals.present?
      progress_checkin_done = if goal_added == true
                                emp.added_by_goals.any? do |goal|
                                  goal.goal_activities.where(employee: emp).present?
                                end
                              else
                                false
                              end
      team_invited = emp.account.employees.where(invited: true).size > 1
      GoalsOnboardingChecklist.find_or_create_by(employee: emp)
        .update(goal_added: goal_added, progress_checkin_done: progress_checkin_done, explored_demo: false, team_invited: team_invited)
      p "Added Checklist for #{emp.full_name}"
    end
  end

  desc 'create_integrations', 'adds_integrations_to_db'
  def create_integrations
    progress_integrations = [
      { name: 'Google Sheets', key: 'google_sheet' },
      { name: 'Jira', key: 'jira' },
      { name: 'BigQuery', key: 'bigquery' },
      { name: 'MySQL', key: 'mysql' },
      { name: 'PostgreSQL', key: 'postgresql' },
      { name: 'Hubspot', key: 'hubspot' },
      { name: 'Looker', key: 'looker' },
      { name: 'Snowflake', key: 'snowflake'}
    ]
    p 'Adding Progress Integrations'
    progress_integrations.each_with_index do |int, idx|
      Integration.find_or_initialize_by(name: int[:name], key: int[:key],
                                        integration_type: 'Progress').update!(position: idx + 1)
      p "#{int.first} found/added."
    end

    project_integrations = [
      { name: 'Asana', key: 'asana' },
      { name: 'Google Sheet', key: 'google_sheet' },
    ]

    p 'Adding Project Integrations'
    project_integrations.each_with_index do |int, idx|
      Integration.find_or_initialize_by(name: int[:name], key: int[:key],
                                        integration_type: 'Project').update!(position: idx + 1)
      p "#{int.first} found/added."
    end
  end

  # 02/02/2022 - UPDATE FOR CUSTOM OKR NOTIFICATION TIMES
  desc 'update_goals_setting', ''
  def update_goals_setting
    GoalsSetting.all.each do |setting|
      return unless setting.next_reminder_at.nil?

      zone = ActiveSupport::TimeZone[setting.account.timezone || 'UTC']
      reminder_time = "#{Date.current.prev_occurring(:wednesday)} 10:00 #{zone.formatted_offset}".to_datetime
      setting.update!(reminder_day: 3, reminder_time: reminder_time,
                      next_reminder_at: NextOkrNotificationTime.call(reminder_time, 3, setting.reminder_last_sent_on, setting.reminder_frequency))
    end
  end

  desc 'add_manager_to_reviewees', 'adds manager_id to each reviewee for past reviewees'
  def add_manager_to_reviewees
    @reviewees = Reviewee.all
    @reviewees.each do |reviewee|
      next if reviewee.employee.manager.blank?

      reviewee.manager_id = reviewee.employee.manager.id
      reviewee.save
      p "Reviewee Id: #{reviewee.id}, Manager Id: #{reviewee.employee.manager}"
    end
  end
  desc 'add_goal_progress_types', 'adds all supported progress types to db'
  def add_goal_progress_types
    progress_types = [
      { title: 'default_progress', min_val: 0.0, max_val: 0.1e3 },
      { title: 'custom_percent', min_val: -0.1e21, max_val: 0.1e21 },
      { title: 'number', min_val: -0.1e21, max_val: 0.1e21 },
      { title: 'dollar', min_val: -0.1e21, max_val: 0.1e21 },
      { title: 'indian_rupee', min_val: -0.1e21, max_val: 0.1e21 },
      { title: 'euro', min_val: -0.1e21, max_val: 0.1e21 },
      { title: 'GBP', min_val: -0.1e21, max_val: 0.1e21 },
      { title: 'BDT', min_val: -0.1e21, max_val: 0.1e21 },
      { title: 'binary', min_val: 0.0, max_val: 0.1e1 },
      { title: 'SAR', min_val: -0.1e21, max_val: 0.1e21 },
      { title: 'AED', min_val: -0.1e21, max_val: 0.1e21 },
    ]
    progress_types.each_with_index do |type_params, position|
      GoalProgressType.find_or_create_by(type_params).update!(position: position)
      p "Updated #{type_params[:title]}"
    end
  end
  desc 'auto_approve_peers', 'automatically approves nominated peers given review_cycle and approver_id'
  def auto_approve_peers(review_cycle_id, approver_id)
    review_cycle = ReviewCycle.find(review_cycle_id)
    reviewer_type = review_cycle.reviewer_types.where(reviewer_type: 'peer').first
    reviewers = review_cycle.reviewers.where.not(nominator_id: nil).where(approver_id: nil,
                                                                          reviewer_type: reviewer_type)
    reviewers.each do |reviewer|
      next unless reviewer.update(approver_id: approver_id)

      puts "Reviewer Id: #{reviewer.id} updated with approver ID: #{approver_id}"
      reviewer.reviewee.update!(peer_approval_done: true)
      puts "Reviewee Id: #{reviewer.reviewee_id} updated with peer_approval_done: #{reviewer.reviewee.peer_approval_done}"
    end
  end

  desc 'update_approve_reviewee_goals',
       'automatically, find or create reviewee goals and approve given review cycle  and approver_id'
  def update_approve_reviewee_goals(review_cycle_id, approver_id)
    review_cycle = ReviewCycle.find(review_cycle_id)
    reviewees = review_cycle.reviewees.where(goal_approval_done: false)
    total_weight = 100
    reviewees.each do |reviewee|
      reviewee_goals = reviewee.reviewee_goals.where(review_cycle_id: review_cycle.id)
        .where(approver_id: nil)
        .where(rejected_by_id: nil)
        .where(exclude: false)
      if reviewee_goals.present?
        count = reviewee_goals.count
        min_weightage = (total_weight / count)
        max_weightage = (total_weight - (min_weightage * (count - 1)))
        reviewee_goals.each_with_index do |reviewee_goal, idx|
          idx = idx += 1
          if idx == count
            if reviewee_goal.update(weightage: max_weightage, approver_id: approver_id)
              puts "Reviewer Id: #{reviewee.id} updated with approver ID: #{approver_id}"
              reviewee.update(goal_approval_done: true)
              puts "Reviewee Id: #{reviewee.id} updated with goal_approval_done: #{reviewee.goal_approval_done}"
            end
          elsif idx < count
            if reviewee_goal.update(weightage: min_weightage, approver_id: approver_id)
              puts "Reviewer Id: #{reviewee.id} updated with approver ID: #{approver_id}"
              reviewee.update(goal_approval_done: true)
              puts "Reviewee Id: #{reviewee.id} updated with goal_approval_done: #{reviewee.goal_approval_done}"
            end
          end
        end
      else
        PerformanceReview::WebPerformanceReview::FetchRevieweeGoalsWeb.call(review_cycle.id, reviewee.id, nil)
        reviewee_goals = reviewee.reviewee_goals.where(review_cycle_id: review_cycle.id)
          .where(rejected_by_id: nil)
          .where(approver_id: nil)
          .where(exclude: false)
        if reviewee_goals.present?
          count = reviewee_goals.count
          min_weightage = (total_weight / count)
          max_weightage = (total_weight - (min_weightage * (count - 1)))
          reviewee_goals.each_with_index do |reviewee_goal, idx|
            idx = idx += 1
            if idx == count
              if reviewee_goal.update(weightage: max_weightage, approver_id: approver_id)
                puts "Reviewer Id: #{reviewee.id} updated with approver ID: #{approver_id}"
                reviewee.update(goal_approval_done: true)
                puts "Reviewee Id: #{reviewee.id} updated with goal_approval_done: #{reviewee.goal_approval_done}"
              end
            elsif idx < count
              if reviewee_goal.update(weightage: min_weightage, approver_id: approver_id)
                puts "Reviewer Id: #{reviewee.id} updated with approver ID: #{approver_id}"
                reviewee.update(goal_approval_done: true)
                puts "Reviewee Id: #{reviewee.id} updated with goal_approval_done: #{reviewee.goal_approval_done}"
              end
            end
          end
        end

      end
    end
  end

  desc 'update_employee_departments', ''
  def update_employee_departments
    Employee.all.each do |employee|
      next if employee.department.nil?

      EmployeeDepartment.create!(employee: employee, department: employee.department)
    end
  end

  desc 'mark_home_page_actions',
       'accepts string of action_types "choose_peers, approve_peers, approve_goals", employees(optional)"1, 2,3"and
       review_cycle_id and based on this mark the home_page_action end_date as DateTime.now'
  # thor data_migrate:mark_home_page_actions 33, "choose_peers, approve_peers, approve_goals", "1,2,3"
  def mark_home_page_actions(review_cycle_id, action_types = nil, employee_ids = nil)
    review_cycle = ReviewCycle.find(review_cycle_id)
    action_types = action_types.split(',').map(&:strip) if action_types.present?
    employee_ids = employee_ids.split(',').map(&:strip) if employee_ids.present?
    home_page_actions = if employee_ids.present?
                          HomePageAction.where(actionable_type: 'ReviewCycle', actionable_id: review_cycle&.id,
                                               employee_id: employee_ids, action_type: action_types)
                        else
                          p review_cycle&.id
                          p HomePageAction.where(actionable_type: 'ReviewCycle', actionable: review_cycle,
                                                 action_type: action_types)
                        end
    home_page_actions.each do |home_page_action|
      if home_page_action.update(end_date: DateTime.now)
        puts "Home page action: #{home_page_action.action_type} updated with end_date: #{home_page_action.end_date}"
      end
    end
  end

  desc 'biz_review_for_all_accounts', ''
  def biz_review_for_all_accounts
    Account.all.each do |account|
      next if account.id == 1712
      
      active_goal_cycles = GoalCycle.account_active_cycles(account.id)
      previous_qtrly_goal_cycle = GoalCycle.account_previous_cycles(account.id).where(cycle: 'quarterly').order(start_date: :desc).first
      previous_monthly_goal_cycle = GoalCycle.account_previous_cycles(account.id).where(cycle: 'monthly').order(start_date: :desc).first
      previous_annual_goal_cycle = GoalCycle.account_previous_cycles(account.id).where(cycle: 'annually').order(start_date: :desc).first

      upcoming_goal_cycles = GoalCycle.account_upcoming_cycles(account.id)

      if account.goals.where(goal_cycle: active_goal_cycles, goal_type: 'company').present?
        goal_cycle_ids = active_goal_cycles.pluck(:id)
        goals = account.goals.where(goal_cycle: active_goal_cycles, goal_type: 'company')
      elsif account.goals.where(goal_cycle: previous_qtrly_goal_cycle, goal_type: 'company').present?
        goal_cycle_ids = [previous_qtrly_goal_cycle.id]
        goals = account.goals.where(goal_cycle: previous_qtrly_goal_cycle, goal_type: 'company')
      elsif account.goals.where(goal_cycle: previous_monthly_goal_cycle, goal_type: 'company').present?
        goal_cycle_ids = [previous_monthly_goal_cycle.id]
        goals = account.goals.where(goal_cycle: previous_monthly_goal_cycle, goal_type: 'company')
      elsif account.goals.where(goal_cycle: previous_annual_goal_cycle, goal_type: 'company').present?
        goal_cycle_ids = [previous_annual_goal_cycle.id]
        goals = account.goals.where(goal_cycle: previous_annual_goal_cycle, goal_type: 'company')
      elsif account.goals.where(goal_cycle: upcoming_goal_cycles, goal_type: 'company').present?
        goal_cycle_ids = upcoming_goal_cycles.pluck(:id)
        goals = account.goals.where(goal_cycle: upcoming_goal_cycles, goal_type: 'company')
      end

      if goals.present? && account.biz_reviews.empty?
        BizReviews::Generators::CreateDefaultBizReview.call({ account_id: account.id, owner_id: account.id, owner_type: 'Account', goal_cycles: goal_cycle_ids })
        p "Biz Review Created for Account #{account.id} - #{account.company_name}"
      end

      account.departments.each do |dept|
        next if dept.biz_reviews.present?

        goal_cycle_ids = nil
        goals = nil

        if dept.goals.where(goal_cycle: active_goal_cycles).present?
          goal_cycle_ids = active_goal_cycles.pluck(:id)
          goals = dept.goals.where(goal_cycle: active_goal_cycles)
        elsif dept.goals.where(goal_cycle: previous_qtrly_goal_cycle).present?
          goal_cycle_ids = [previous_qtrly_goal_cycle.id]
          goals = dept.goals.where(goal_cycle: previous_qtrly_goal_cycle)
        elsif dept.goals.where(goal_cycle: previous_monthly_goal_cycle).present?
          goal_cycle_ids = [previous_monthly_goal_cycle.id]
          goals = dept.goals.where(goal_cycle: previous_monthly_goal_cycle)
        elsif dept.goals.where(goal_cycle: previous_annual_goal_cycle).present?
          goal_cycle_ids = [previous_annual_goal_cycle.id]
          goals = dept.goals.where(goal_cycle: previous_annual_goal_cycle)
        elsif dept.goals.where(goal_cycle: upcoming_goal_cycles).present?
          goal_cycle_ids = upcoming_goal_cycles.pluck(:id)
          goals = dept.goals.where(goal_cycle: upcoming_goal_cycles)
        end

        if goals.present?
          BizReviews::Generators::CreateDefaultBizReview.call({ account_id: account.id, departments: [dept.id], goal_cycles: goal_cycle_ids })
          p "Biz Review Created for Department #{dept.name}, Account #{account.id} - #{account.company_name}"
        end
      end
    end
  end

  desc 'update_system_comment_field_for_goal_activities', ''
  def update_system_comment_field_for_goal_activities
    GoalActivity.where.not('description like ?', 'Updated the progress from%')
                .where.not('description like ?', 'Changed to%')
                .where.not('description like ?', 'Updated progress from%')
                .where.not('description like ?', "%as a roll-up from it's key result.")
                .update_all(system_comment: false)
  end

  desc 'set_biz_reviews_scheduled_at', ''
  def set_biz_reviews_scheduled_at
    BizReview.all.each do |br|
      timezone = br.account.timezone
      br.biz_review_schedules.each do |brs|
        start_date = brs.cycle_start_date.in_time_zone(timezone)
        brs.update(scheduled_at: brs.cycle_start_date, title: get_title(br, start_date))
      end
    end
  end

  desc 'add_build_quarter_to_quarter_two', 'adding build quarter based on departments'
  method_option :dept_ids, type: :array, default: nil
  def add_build_quarter_to_quarter_two
    cycle_id = 55062
    build_quarter_val  = ****************
    dept_ids = options[:dept_ids].map(&:to_i) if options[:dept_ids].present?
    goal_integrations = GoalIntegrationField.joins(:integration_project, integration_config: [:integration]).where(goals: {goal_cycle_id: cycle_id}, integration_configs: { integrations: { name: 'Asana'}})
    goal_integrations = goal_integrations.where(goals: { department_id: dept_ids }) if dept_ids.present?
  
    goal_integrations.each do |pif|
      json = pif.query_json
      json['build_quarters'] = build_quarter_val
      json['build_quarter_name'] = 'Q2'
      pif.save
    end
  end
  desc 'set_activity_type_for_null_row', ''
  def set_activity_type_for_null_row
    grouped_activity = GoalActivity.order(:created_at).group_by(&:goal_id).each do |_, activities|
      activities.each_with_index do |activity, index|
        # next unless activity.activity_type == 'progress_checkin'
      
        old_val = (index == 0 ? 0 : activities[index - 1].completed_value)
        old_status = (index == 0 ? nil : activities[index - 1].status)
        
        next unless activity.completed_value == old_val && activity.status == old_status
    
        activity_type = activity.completed_value == old_val && activity.status == old_status ? 'comment' : 'progress_checkin'
        activity.activity_type = 'comment'
        activity.save
      end
    end
  end
  
  desc 'fix_top_level_kr_to_objective', ''
  def fix_top_level_kr_to_objective
    Goal.where(ancestry: nil, objective_type: 'key_result', is_key_result: true).update_all("objective_type = 'objective', is_key_result = false")
  end

  desc 'set_previous_activity_field', ''
  def set_previous_activity_field
    Goal.all.each do |goal|
      SetPreviousGoalActivity.delay(queue: 'low').call(goal.id)
    end

    # GoalActivity.all.each do |activity|
    #   last = GoalActivity.where(goal_id: activity.goal_id).where('id < ?', activity.id).last
    #   activity.previous_progress_value =  last&.completed_value || 0.0
    #   activity.save
    # end
  end

  no_commands do
    def convert_to_html(multiline_text)
      lines = multiline_text.strip.split("\n")
      html_paragraphs = lines.map { |line| "<p class=\"comment-element-container\">#{line}</p>" }.join
      html_paragraphs
    end

    def convert_to_json(multiline_text)
      lines = multiline_text.strip.split("\n")
      json_content = lines.map do |line|
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: line
            }
          ]
        }
      end
      json_output = { type: "doc", content: json_content }
      JSON.pretty_generate(json_output)
    end

    def get_title(br, start_date)
      case br.frequency
      when 'one_time', 'weekly', 'bi_weekly'
        "#{br.title} - #{start_date.day.ordinalize} #{start_date.strftime('%b, %Y')}"
      when 'monthly'
        "#{br.title} - #{start_date.strftime('%b %Y')}"
      when 'quarterly'
        "#{br.title} - #{get_quarter(br, start_date)} #{start_date.strftime('%Y')}"
      end
    end

    def get_quarter(br, date)
      acc_planning_year = br.account.planning_year.to_date.month
      months_order = (acc_planning_year..12).to_a + (1..acc_planning_year).to_a
      case date.month
      when *months_order[0..2]
        'Q1'
      when *months_order[3..5]
        'Q2'
      when *months_order[6..8]
        'Q3'
      when *months_order[9..11]
        'Q4'
      end
    end

    def fetch_employee_detail(account_id, emp_full_name)
      Employee.where(account_id: account_id)
              .where("lower(full_name) = lower('"+emp_full_name+"')")
    end

    def fetch_metadata_values
        MetadataValue.where(metadata_field_id: [11,12,13], owner_type: "Department")
                    .where('owner_id is not null and value is not null')
                    .where('value != ""')
                    .order(updated_at: :desc)
                    .group_by { |field| field[:value]}
    end
  end

  desc 'set_previous_progress_status_field', ''
  def set_previous_progress_status_field
    puts "set_previous_progress_status_field started"
    Goal.all.each do |goal|
      puts "Goal id #{goal.id}"
      SetPreviousGoalActivityStatus.delay(queue: 'low').call(goal.id)
    end
    puts "set_previous_progress_status_field completed"
  end

  # Account: Razorpay, Assigning/updating dept_id for employees by checking metadata_values
  desc 'add_dept_id_to_employees', 'Assigning/updating dept_id for RZP employees'
  def add_dept_id_to_employees
      metadata_values = fetch_metadata_values
      return if metadata_values.nil?

      emp_not_found = []
      updated_emp = []
      metadata_values = metadata_values.map{|i| i[1].first}
      metadata_values.each do |metadata_value|
          employee = fetch_employee_detail(1712, metadata_value.value.strip)
          if employee.blank?
              emp_not_found << metadata_value
              next
          end

          employee = employee.first
          employee.update!(department_id: metadata_value.owner_id)
          updated_emp << employee
      end
      puts "emp update count; #{updated_emp.count}"
      puts "emp updated ids: #{updated_emp.map{|value| value.id}}"
      puts "Metadata_values count: #{metadata_values.count}"
      puts "Employee not found Count: #{emp_not_found.count}"
      # puts "list: #{emp_not_found}"
  end

  desc 'create_custom_schedule', 'Assigning/updating dept_id for RZP employees'
  def create_custom_biz_review_schedule(account_id, dept_type, frequency)
    account = Account.find(account_id)
    department_ids = Department.where(account_id: account_id, department_type_name: dept_type, active: 1)
    department_ids.each do |department|
      biz_review = BizReview.where(owner_id: department.id, frequency: frequency).last
      metabase = [{title: "Roadmap Health Key projects - Q1 2023", metabase_report_id: 1048}, {title: "Roadmap Health all projects - Q1 2023", metabase_report_id: 1049}]
      if biz_review
        current_schedule = biz_review.biz_review_schedules.last
        start_date = current_schedule.cycle_start_date
        end_date = start_date + 30.days
        puts "Creating schedule for #{biz_review.id}"
        BizReviews::Generators::CreateCustomNextBizReviewSchedule.call(current_schedule, start_date, end_date, nil, metabase, false)
        puts "....Created...."
      else
        puts "Creating for Department Id: #{department.id}"
        params = {owner_id: department.id,
                  owner_type: 'Department',
                  frequency: 'monthly',
                  start_date: '2023-04-21',
                  start_time: '11:00',
                  auto_create_next_review: false,
                  metabase_id_details: metabase,
                  account_id: account_id,
                  created_by_id: account.admins.first.id,
                }
        BizReviews::Generators::CreateBizReviewByUser.call(params)
        puts "....Created...."
      end
    end
  end

  desc 'update_custom_fields', 'Updating custom fields in biz_review okr'
  def update_custom_fields(*biz_review_schedule_ids)
    puts biz_review_schedule_ids
    biz_review_schedule_ids.each do |br_id|
      br = BizReviewSchedule.find(br_id)
      puts br.id
      okr_card = br.biz_review_cards.joins(:biz_review_widget).where(biz_review_widgets: { widget_type: 'okr_and_project_details' }).first
      okr_card.properties["filters"]["okr_metafields"] = ["EOFY"]
      okr_card.properties["custom_checkin"]["enabled"] = true
      okr_card.properties["custom_checkin"]["non_rca_checkin_fields"] = [6, 7]
      okr_card.properties["filters"]["project_primary_metafields"] = ["E2e Planned Date", "E2e Actual Go Live Date", "Comments / Remarks"]
      okr_card.properties["filters"]["project_secondary_metafields"] = []
      okr_card.save!
      project_card = br.biz_review_cards.joins(:biz_review_widget).where(biz_review_widgets: { widget_type: 'project_details' }).first
      project_card.properties["filters"]["display_fields"] = ["E2e Planned Date", "E2e Actual Go Live Date", "Comments / Remarks"]
      project_card.properties["metadata_filters"] = {}
      project_card.save!
    end
  end
end
