# frozen_string_literal: true

source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '2.5.1'

# Core Gems

gem 'activeadmin'
gem 'active_model_serializers', '~> 0.10.6'
gem 'activerecord-import'
gem 'ahoy_email'
gem 'ahoy_matey'
gem 'bcrypt', '~> 3.1.7'
gem 'delayed_job_active_record'
gem 'dotenv-rails', require: 'dotenv/rails-now'
gem 'jsonpath', '~> 0.5.8'
gem 'jwt'
gem 'kaminari'
gem 'mailgun-ruby', '~>1.2.0'
gem 'mysql2', '>= 0.4.4', '< 0.6.0'
gem 'puma', '~> 3.11'
gem 'rack-cors'
gem 'rails', '~> 5.2.0'
gem 'roadie-rails' # For embedding CSS into email
gem 'shortener', github: 'JerryGreen/shortener', branch: 'rails5' # For shortening urls
gem 'thin'
gem 'tzinfo-data'
gem 'will_paginate', '~> 3.1.0'
# gem 'microsoft_bot_connector', :git => 'https://github.com/Peoplebox/microsoft_bot_connector_api.git'

# Exports
gem 'wicked_pdf'
gem 'wkhtmltopdf-binary'

# ML / NLP
gem 'classifier-reborn'
gem 'sentimental'

# Commandline
gem 'god'
gem 'highline'
gem 'simple_command'
gem 'thor'

# Devops
gem 'acts-as-taggable-on'
gem 'clockwork'
gem 'paper_trail'
gem 'pundit'
gem 'sentry-ruby'
gem 'sentry-rails'
gem 'sentry-delayed_job'

# Backend
gem 'after_the_deadline'
gem 'ancestry'
gem 'aws-sdk-s3', '~> 1'
gem 'carrierwave'
gem 'chargebee', '~> 2'
gem 'config'
gem 'discard', '~> 1.2'
gem 'faker', git: 'https://github.com/stympy/faker.git', branch: 'master'
gem 'ffaker', github: 'ffaker/ffaker'
gem 'fog-aws'
gem 'google-api-client', '~> 0.11'
gem 'icalendar', '~> 2.5.3'
gem 'liquid'
gem 'mail'
gem 'microsoft_graph'
gem 'mini_magick'
gem 'parallel'
gem 'rbzip2'
gem 'redcarpet' # Markdown parser
gem 'kramdown' # Another markdown parser
gem 'reverse_markdown', git: 'https://github.com/Peoplebox/reverse_markdown.git', branch: 'master' # HTML to Markdown with custom code
gem 'redis', '~> 4.0'
gem 'rest-client'
gem 'ruby-progressbar'
gem 'rubyzip', '>= 1.0.0'
gem 'slack-notifier'
gem 'slack-ruby-client', '~> 0.15.1'
gem 'typhoeus'
gem 'pg', '~> 0.18.4'
gem 'scenic-mysql_adapter'
gem 'dentaku'
gem 'ruby-odbc'
gem 'sequel'
gem 'deep_cloneable', '~> 3.2.0'
gem 'audit-log', '~> 1.2.0'
gem 'restforce', '~> 5.0.6'
gem 'json-schema'
gem 'posthog-ruby'
gem 'webp-ffi', '~> 0.4.0'
gem 'xmldsig'
gem 'rails_event_store'
gem 'ruby_event_store'
gem 'rails_event_store_active_record'

# Active Admin
gem 'activeadmin_addons'
gem 'active_admin_datetimepicker'
gem 'activeadmin_trumbowyg'
gem 'active_material'

# Use Capistrano for deployment
# gem 'capistrano-rails', group: :development
gem 'bootsnap', '>= 1.1.0', require: false

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platforms: %i[mri mingw x64_mingw]
  gem 'spring'
  gem 'spring-commands-rspec'
  gem 'rspec-rails', '~> 5.0.0'
  # needed to generate seed data
  gem 'factory_bot_rails'
  gem 'awesome_print'
  gem 'benchmark'
  gem 'webmock'
  gem 'pdf-reader'
end

group :test do
  gem 'shoulda-matchers', '~> 4.5.0'
  gem 'database_cleaner-active_record'
  gem 'simplecov', require: false
end

group :development do
  gem 'listen', '>= 3.0.5', '< 3.2'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  # gem 'spring'
  gem 'letter_opener_web'
  # gem 'spring-watcher-listen', '~> 2.0.0'

  # Capistrano for deployment
  gem 'capistrano'
  gem 'capistrano-bundler'
  gem 'capistrano-rails'
  gem 'capistrano-rails-console'
  gem 'capistrano-rbenv'
  gem 'capistrano-thin'

  # Exception handling
  gem 'bullet'
  gem 'ngrok-tunnel'
  gem 'pry-nav'
  gem 'pry-rescue'
  gem 'pry-stack_explorer'
  gem 'rubocop', '~> 1.19.0'

  # Code formatting
  gem 'overcommit', '~> 0.58.0'
  gem 'rubocop-rails', '2.11'

  # lsp support
  gem 'solargraph', '0.44'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'simple_chat', path: 'simple_chat/'
gem 'json-diff', '~> 0.4.1'
gem 'pdftoimage', '~> 0.1.7'

gem 'ruby-saml', '~> 1.11'

# Application Monitoring
gem 'newrelic_rpm'
