# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# Note that this schema.rb definition is the authoritative source for your
# database schema. If you need to create the application database on another
# system, you should be using db:schema:load, not running all the migrations
# from scratch. The latter is a flawed and unsustainable approach (the more migrations
# you'll amass, the slower it'll run and the greater likelihood for issues).
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_08_10_165720) do

  create_table "account_ai_model_configs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "ai_model_config_id", null: false
    t.boolean "active", default: true
    t.boolean "enabled_for_reviews", default: false
    t.boolean "enabled_for_goals", default: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "enable_for_three_sixty_review_summary", default: false
    t.boolean "enable_for_feedbacks", default: false
    t.index ["account_id"], name: "index_account_ai_model_configs_on_account_id"
    t.index ["ai_model_config_id"], name: "index_account_ai_model_configs_on_ai_model_config_id"
    t.index ["discarded_at"], name: "index_account_ai_model_configs_on_discarded_at"
  end

  create_table "account_products", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.bigint "product_id"
    t.datetime "discarded_at"
    t.bigint "discarded_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "product_id"], name: "index_account_products_on_account_id_and_product_id", unique: true
    t.index ["account_id"], name: "index_account_products_on_account_id"
    t.index ["discarded_by_id"], name: "index_account_products_on_discarded_by_id"
    t.index ["product_id"], name: "index_account_products_on_product_id"
  end

  create_table "account_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.boolean "disable_monthly_goal_cycles", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "disable_department_creation", default: false
    t.string "logo_square"
    t.string "logo_rectangle"
    t.string "logo_background_color"
    t.boolean "enable_milestone_goals", default: true
    t.integer "pdf_service", default: 0
    t.index ["account_id"], name: "index_account_settings_on_account_id"
  end

  create_table "accounts", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "company_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "ceo_id"
    t.string "account_type", default: "live"
    t.string "logo"
    t.string "timezone"
    t.string "survey_frequency", default: "monthly"
    t.string "email_client", default: "gmail"
    t.boolean "record_sessions", default: true
    t.string "domain_name"
    t.string "remote_policy"
    t.string "company_size_range"
    t.boolean "run_coffee_connect", default: false
    t.integer "messaging_client"
    t.string "country_code"
    t.string "company_phone_number"
    t.string "glassdoor_review_link", default: ""
    t.integer "planning_year", default: 1
    t.string "preferred_product"
    t.string "trial_objective"
    t.string "signupform_communication_tool"
    t.boolean "enable_goal_metadata", default: false
    t.json "department_hierachy"
    t.json "setting_tokens"
    t.boolean "enable_kpi", default: true
    t.boolean "create_private_reviews", default: false
    t.integer "metabase_type", default: 0
    t.boolean "enable_okr_update", default: true
    t.boolean "talent_management", default: true
    t.boolean "analytics", default: true
  end

  create_table "acl_role_members", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "acl_role_id"
    t.string "member_type"
    t.bigint "member_id"
    t.datetime "discarded_at"
    t.bigint "discarded_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["acl_role_id"], name: "index_acl_role_members_on_acl_role_id"
    t.index ["discarded_by_id"], name: "index_acl_role_members_on_discarded_by_id"
    t.index ["member_type", "member_id"], name: "index_acl_role_members_on_member_type_and_member_id"
  end

  create_table "acl_role_sub_actions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "acl_role_id"
    t.bigint "product_action_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["acl_role_id"], name: "index_acl_role_sub_actions_on_acl_role_id"
    t.index ["discarded_at"], name: "index_acl_role_sub_actions_on_discarded_at"
    t.index ["product_action_id"], name: "index_acl_role_sub_actions_on_product_action_id"
  end

  create_table "acl_roles", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.bigint "account_id"
    t.datetime "discarded_at"
    t.bigint "discarded_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "role_type", default: "custom", null: false
    t.index ["account_id"], name: "index_acl_roles_on_account_id"
    t.index ["discarded_by_id"], name: "index_acl_roles_on_discarded_by_id"
  end

  create_table "acls", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "actorable_type"
    t.bigint "actorable_id"
    t.integer "permission_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.bigint "discarded_by_id"
    t.bigint "created_by_id"
    t.string "targetable_type"
    t.bigint "targetable_id"
    t.index ["actorable_type", "actorable_id"], name: "index_acls_on_actorable_type_and_actorable_id"
    t.index ["created_by_id"], name: "index_acls_on_created_by_id"
    t.index ["discarded_at"], name: "index_acls_on_discarded_at"
    t.index ["discarded_by_id"], name: "index_acls_on_discarded_by_id"
    t.index ["resource_id", "resource_type", "actorable_id", "actorable_type"], name: "index_acls_on_resource_id_and_resource_type_and_actorable"
    t.index ["resource_type", "resource_id", "actorable_type", "actorable_id", "discarded_at"], name: "idx_acls_resource_actorable"
    t.index ["resource_type", "resource_id"], name: "index_acls_on_resource_type_and_resource_id"
    t.index ["targetable_type", "targetable_id"], name: "index_acls_on_targetable_type_and_targetable_id"
  end

  create_table "active_admin_comments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "author_type"
    t.bigint "author_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author_type_and_author_id"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource_type_and_resource_id"
  end

  create_table "activity_logs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "loggable_type"
    t.bigint "loggable_id"
    t.string "actor_type"
    t.bigint "actor_id"
    t.integer "event_type"
    t.json "audited_changes"
    t.text "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["actor_type", "actor_id"], name: "index_activity_logs_on_actor_type_and_actor_id"
    t.index ["loggable_type", "loggable_id"], name: "index_activity_logs_on_loggable_type_and_loggable_id"
  end

  create_table "agenda_growth_hacks", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email"
    t.string "agendas_bookmarked"
    t.boolean "email_sent", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_agenda_growth_hacks_on_email"
  end

  create_table "agenda_logger_managers", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "agenda_id"
    t.bigint "employee_one_on_one_logger_id"
    t.text "agenda_text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["agenda_id"], name: "index_agenda_logger_managers_on_agenda_id"
    t.index ["employee_one_on_one_logger_id"], name: "index_agenda_logger_managers_on_employee_one_on_one_logger_id"
  end

  create_table "agendas", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.text "agenda_text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "agenda_type"
  end

  create_table "ahoy_events", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "visit_id"
    t.bigint "user_id"
    t.string "name"
    t.json "properties"
    t.timestamp "time"
    t.index ["name", "time"], name: "index_ahoy_events_on_name_and_time"
    t.index ["user_id"], name: "index_ahoy_events_on_user_id"
    t.index ["visit_id"], name: "index_ahoy_events_on_visit_id"
  end

  create_table "ahoy_messages", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "token"
    t.text "to"
    t.string "user_type"
    t.bigint "user_id"
    t.string "mailer"
    t.text "subject"
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_campaign"
    t.timestamp "sent_at"
    t.timestamp "opened_at"
    t.timestamp "clicked_at"
    t.boolean "email_bounced"
    t.bigint "simple_chat_chatter_id"
    t.bigint "schedule_id"
    t.string "trackable_type"
    t.bigint "trackable_id"
    t.index ["schedule_id"], name: "index_ahoy_messages_on_schedule_id"
    t.index ["simple_chat_chatter_id"], name: "index_ahoy_messages_on_simple_chat_chatter_id"
    t.index ["token"], name: "index_ahoy_messages_on_token"
    t.index ["trackable_type", "trackable_id"], name: "index_ahoy_messages_on_trackable_type_and_trackable_id"
    t.index ["user_type", "user_id"], name: "index_ahoy_messages_on_user_type_and_user_id"
  end

  create_table "ahoy_visits", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "visit_token"
    t.string "visitor_token"
    t.bigint "user_id"
    t.string "ip"
    t.text "user_agent"
    t.text "referrer"
    t.string "referring_domain"
    t.text "landing_page"
    t.string "browser"
    t.string "os"
    t.string "device_type"
    t.string "country"
    t.string "region"
    t.string "city"
    t.float "latitude"
    t.float "longitude"
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_term"
    t.string "utm_content"
    t.string "utm_campaign"
    t.string "app_version"
    t.string "os_version"
    t.string "platform"
    t.timestamp "started_at"
    t.index ["user_id"], name: "index_ahoy_visits_on_user_id"
    t.index ["visit_token"], name: "index_ahoy_visits_on_visit_token", unique: true
  end

  create_table "ai_model_configs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "identifier", null: false
    t.text "description"
    t.string "base_url"
    t.string "api_key"
    t.decimal "input_token_cost_per_million", precision: 10, scale: 4, default: "0.0", null: false
    t.decimal "output_token_cost_per_million", precision: 10, scale: 4, default: "0.0", null: false
    t.boolean "active", default: true
    t.decimal "service_charges", precision: 10, scale: 4, default: "0.0", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_ai_model_configs_on_discarded_at"
    t.index ["identifier"], name: "index_ai_model_configs_on_identifier", unique: true
  end

  create_table "ai_usage_logs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "gpt_service", null: false
    t.integer "input_tokens", null: false
    t.integer "output_tokens", null: false
    t.bigint "account_id"
    t.string "status"
    t.text "error_message"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "serviceable_type"
    t.bigint "serviceable_id"
    t.bigint "employee_id"
    t.string "request_purpose"
    t.bigint "ai_model_config_id", null: false
    t.index ["account_id"], name: "index_ai_usage_logs_on_account_id"
    t.index ["ai_model_config_id"], name: "index_ai_usage_logs_on_ai_model_config_id"
    t.index ["employee_id"], name: "index_ai_usage_logs_on_employee_id"
    t.index ["resource_type", "resource_id"], name: "index_ai_usage_logs_on_resource_type_and_resource_id"
    t.index ["serviceable_type", "serviceable_id"], name: "index_ai_usage_logs_on_serviceable_type_and_serviceable_id"
  end

  create_table "attachments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.json "files"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "attachable_type"
    t.bigint "attachable_id"
    t.index ["attachable_type", "attachable_id"], name: "index_attachments_on_attachable_type_and_attachable_id"
  end

  create_table "audit_logs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "action", null: false
    t.bigint "user_id"
    t.bigint "record_id"
    t.string "record_type"
    t.text "payload"
    t.text "request"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["action"], name: "index_audit_logs_on_action"
    t.index ["record_type", "record_id"], name: "index_audit_logs_on_record_type_and_record_id"
    t.index ["user_id", "action"], name: "index_audit_logs_on_user_id_and_action"
  end

  create_table "biz_review_action_items", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.boolean "completed", default: false
    t.bigint "biz_review_card_id"
    t.string "item_type"
    t.bigint "item_id"
    t.bigint "biz_review_schedule_id"
    t.bigint "completed_biz_review_schedule_id"
    t.bigint "super_list_item_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["biz_review_card_id"], name: "index_biz_review_action_items_on_biz_review_card_id"
    t.index ["biz_review_schedule_id"], name: "index_biz_review_action_items_on_biz_review_schedule_id"
    t.index ["completed_biz_review_schedule_id"], name: "completed_biz_review_schedule_id"
    t.index ["discarded_at"], name: "index_biz_review_action_items_on_discarded_at"
    t.index ["item_type", "item_id"], name: "index_biz_review_action_items_on_item_type_and_item_id"
    t.index ["super_list_item_id"], name: "index_biz_review_action_items_on_super_list_item_id"
  end

  create_table "biz_review_cards", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "x_axis"
    t.integer "y_axis"
    t.bigint "biz_review_widget_id"
    t.integer "height"
    t.integer "width"
    t.json "properties"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "section_id"
    t.bigint "biz_review_schedule_id"
    t.string "title"
    t.json "saved_response"
    t.bigint "discarded_by_id"
    t.datetime "discarded_at"
    t.json "card_control"
    t.bigint "added_by_id"
    t.string "user_provided_title"
    t.index ["added_by_id"], name: "index_biz_review_cards_on_added_by_id"
    t.index ["biz_review_schedule_id"], name: "index_biz_review_cards_on_biz_review_schedule_id"
    t.index ["biz_review_widget_id"], name: "index_biz_review_cards_on_biz_review_widget_id"
    t.index ["discarded_by_id"], name: "index_biz_review_cards_on_discarded_by_id"
  end

  create_table "biz_review_comments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "biz_review_card_id"
    t.bigint "biz_review_schedule_id"
    t.integer "commented_by_id"
    t.text "body"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.json "comment_json"
    t.text "comment_html"
    t.bigint "parent_id"
    t.string "identifier"
    t.text "selected_text"
    t.index ["biz_review_card_id"], name: "index_biz_review_comments_on_biz_review_card_id"
    t.index ["biz_review_schedule_id"], name: "index_biz_review_comments_on_biz_review_schedule_id"
    t.index ["discarded_at"], name: "index_biz_review_comments_on_discarded_at"
    t.index ["identifier"], name: "index_biz_review_comments_on_identifier"
    t.index ["parent_id", "discarded_at"], name: "index_biz_review_comments_on_parent_id_and_discarded_at"
  end

  create_table "biz_review_notes", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.json "note_text"
    t.bigint "biz_review_card_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "note_html", limit: **********
    t.datetime "discarded_at"
    t.binary "note_ydoc", limit: **********
    t.bigint "last_edited_by_id"
    t.boolean "scheduled", default: false
    t.index ["biz_review_card_id"], name: "index_biz_review_notes_on_biz_review_card_id", unique: true
    t.index ["last_edited_by_id"], name: "index_biz_review_notes_on_last_edited_by_id"
  end

  create_table "biz_review_schedule_goal_activity_mappings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "biz_review_schedule_id"
    t.bigint "goal_id"
    t.bigint "goal_activity_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["biz_review_schedule_id"], name: "index_schedule_goal_activity_mappings_on_biz_review_schedule_id"
    t.index ["goal_activity_id"], name: "index_schedule_goal_activity_mappings_on_goal_activity_id"
    t.index ["goal_id"], name: "index_schedule_goal_activity_mappings_on_goal_id"
  end

  create_table "biz_review_schedules", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.datetime "scheduled_at"
    t.bigint "biz_review_id"
    t.datetime "cycle_start_date"
    t.datetime "cycle_end_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status", default: 0
    t.boolean "layout_editable", default: true
    t.datetime "discarded_at"
    t.index ["biz_review_id", "cycle_start_date"], name: "index_biz_review_schedules_on_biz_review_id_and_cycle_start_date"
    t.index ["biz_review_id"], name: "index_biz_review_schedules_on_biz_review_id"
    t.index ["discarded_at"], name: "index_biz_review_schedules_on_discarded_at"
  end

  create_table "biz_review_widgets", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.integer "widget_type"
    t.json "properties"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "biz_reviews", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.integer "frequency"
    t.datetime "start_date"
    t.datetime "end_date"
    t.boolean "shared", default: true
    t.bigint "created_by_id"
    t.string "owner_type"
    t.bigint "owner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "account_id"
    t.string "biz_review_type"
    t.datetime "discarded_at"
    t.datetime "auto_create_next_review"
    t.index ["account_id"], name: "index_biz_reviews_on_account_id"
    t.index ["created_by_id", "account_id", "discarded_at"], name: "idx_biz_reviews_created_account_discarded"
    t.index ["created_by_id"], name: "index_biz_reviews_on_created_by_id"
    t.index ["discarded_at"], name: "index_biz_reviews_on_discarded_at"
    t.index ["owner_type", "owner_id"], name: "index_biz_reviews_on_owner_type_and_owner_id"
  end

  create_table "bot_responses", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "response_category"
    t.string "message"
    t.bigint "question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "option_id"
    t.string "emotion"
    t.index ["option_id"], name: "index_bot_responses_on_option_id"
    t.index ["question_id"], name: "index_bot_responses_on_question_id"
  end

  create_table "bot_threads", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "question_id"
    t.boolean "is_bot"
    t.text "message"
    t.string "message_type"
    t.bigint "campaign_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "options"
    t.string "emotion"
    t.boolean "is_question"
    t.integer "parent_survey_question_id"
    t.bigint "employee_chat_id"
    t.bigint "bot_response_id"
    t.datetime "discarded_at"
    t.index ["bot_response_id"], name: "index_bot_threads_on_bot_response_id"
    t.index ["campaign_id"], name: "index_bot_threads_on_campaign_id"
    t.index ["discarded_at"], name: "index_bot_threads_on_discarded_at"
    t.index ["employee_chat_id"], name: "index_bot_threads_on_employee_chat_id"
    t.index ["employee_id"], name: "index_bot_threads_on_employee_id"
    t.index ["question_id"], name: "index_bot_threads_on_question_id"
  end

  create_table "byte_size_learnings", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.text "article_content"
    t.text "article_title"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "calendar_events", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.datetime "start_time"
    t.datetime "end_time"
    t.string "attendee_email"
    t.text "cal_event_id"
    t.bigint "manager_id"
    t.bigint "one_on_one_id"
    t.text "html_link"
    t.string "attendee_full_name"
    t.boolean "checked", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "ical_uid"
    t.text "recurring_event_id"
    t.json "attendees"
    t.bigint "meeting_id"
    t.index ["attendee_email"], name: "index_calendar_events_on_attendee_email"
    t.index ["cal_event_id"], name: "index_calendar_events_on_cal_event_id", unique: true, length: 500
    t.index ["ical_uid"], name: "index_calendar_events_on_ical_uid", length: 500
    t.index ["manager_id"], name: "index_calendar_events_on_manager_id"
    t.index ["one_on_one_id"], name: "index_calendar_events_on_one_on_one_id"
    t.index ["recurring_event_id"], name: "index_calendar_events_on_recurring_event_id", length: 500
  end

  create_table "calendar_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "channel_id"
    t.datetime "expiration"
    t.string "resource_id"
    t.text "resource_uri"
    t.string "sync_token"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_calendar_settings_on_employee_id"
  end

  create_table "calendar_table", primary_key: "dt", id: :date, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "y", limit: 2
    t.integer "q", limit: 1
    t.integer "m", limit: 1
    t.integer "d", limit: 1
    t.integer "dw", limit: 1
    t.string "monthName", limit: 9
    t.string "dayName", limit: 9
    t.integer "w", limit: 1
    t.binary "isWeekday", limit: 1
    t.binary "isHoliday", limit: 1
  end

  create_table "calibration_quota", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_calibration_quota_on_account_id"
  end

  create_table "calibration_quota_ranges", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.float "min_score"
    t.float "max_score"
    t.string "legend"
    t.float "quota_percentage"
    t.float "overflow_percentage"
    t.string "rounding_function"
    t.bigint "calibration_quota_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "carryover_unused_quota", default: true
    t.index ["calibration_quota_id"], name: "index_calibration_quota_ranges_on_calibration_quota_id"
  end

  create_table "calibrator_calibratees", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "calibrator_id", null: false
    t.bigint "calibratee_id", null: false
    t.integer "level", null: false
    t.boolean "can_calibrate", default: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["calibratee_id"], name: "index_calibrator_calibratees_on_calibratee_id"
    t.index ["calibrator_id"], name: "index_calibrator_calibratees_on_calibrator_id"
  end

  create_table "calibrators", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "review_cycle_phase_id"
    t.bigint "calibration_quota_id"
    t.string "status", default: "not_enabled"
    t.boolean "is_final_calibrator", default: false
    t.datetime "discarded_at"
    t.datetime "submitted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["calibration_quota_id"], name: "index_calibrators_on_calibration_quota_id"
    t.index ["employee_id"], name: "index_calibrators_on_employee_id"
    t.index ["review_cycle_phase_id"], name: "index_calibrators_on_review_cycle_phase_id"
  end

  create_table "campaign_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "campaign_id"
    t.string "subject"
    t.text "message"
    t.integer "template_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_campaign_templates_on_campaign_id"
  end

  create_table "campaigns", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "campaign_time"
    t.bigint "account_id"
    t.bigint "survey_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "expires_at"
    t.index ["account_id"], name: "index_campaigns_on_account_id"
    t.index ["survey_id"], name: "index_campaigns_on_survey_id"
  end

  create_table "campaigns_employees", id: false, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.bigint "employee_id", null: false
    t.index ["campaign_id", "employee_id"], name: "index_campaigns_employees_on_campaign_id_and_employee_id"
    t.index ["employee_id", "campaign_id"], name: "index_campaigns_employees_on_employee_id_and_campaign_id"
  end

  create_table "checkin_reasons", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.text "reason_text"
    t.integer "linked_rating"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "reason_driver"
  end

  create_table "coffee_connect_badges", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "badge_name"
    t.string "badge_key"
    t.string "badge_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "featured"
  end

  create_table "coffee_connect_given_badges", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "giver_id"
    t.bigint "badge_id"
    t.bigint "meeting_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "coffee_connect_images", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "coffee_connect_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "selected_channel"
    t.datetime "intro_time"
    t.datetime "next_intro"
    t.bigint "account_id"
    t.bigint "omnichat_conversation_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "employee_id"
    t.bigint "last_week_users", default: 0
    t.bigint "this_week_users", default: 0
    t.integer "pairing_logic", default: 4
    t.datetime "pairing_logic_update_at"
    t.integer "cc_meeting_count", default: 2
    t.text "fixed_times"
    t.index ["account_id"], name: "index_coffee_connect_settings_on_account_id"
    t.index ["employee_id"], name: "index_coffee_connect_settings_on_employee_id"
    t.index ["omnichat_conversation_id"], name: "index_coffee_connect_settings_on_omnichat_conversation_id"
  end

  create_table "company_data", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.json "common_context"
    t.json "goal_context"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_company_data_on_account_id"
  end

  create_table "competencies", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.bigint "competency_theme_id"
    t.string "variant_name", default: "main"
    t.bigint "main_competency_id"
    t.index ["account_id"], name: "index_competencies_on_account_id"
    t.index ["competency_theme_id"], name: "index_competencies_on_competency_theme_id"
    t.index ["discarded_at"], name: "index_competencies_on_discarded_at"
    t.index ["main_competency_id"], name: "index_competencies_on_main_competency_id"
  end

  create_table "competency_levels", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.integer "level", null: false
    t.text "description"
    t.text "examples"
    t.bigint "competency_id", null: false
    t.datetime "discarded_at"
    t.index ["competency_id"], name: "index_competency_levels_on_competency_id"
  end

  create_table "competency_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.json "competency_levels"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_competency_settings_on_account_id"
  end

  create_table "competency_themes", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_competency_themes_on_account_id"
  end

  create_table "configurations", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.integer "data_type", default: 0
    t.text "value"
    t.string "configable_type"
    t.bigint "configable_id"
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["configable_type", "configable_id"], name: "index_configurations_on_configable_type_and_configable_id"
  end

  create_table "connected_workspace_user_maps", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "account_id"
    t.bigint "nominator_id"
    t.bigint "connected_workspace_user_id"
    t.string "email"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_connected_workspace_user_maps_on_account_id"
    t.index ["connected_workspace_user_id"], name: "index_on_connected_workspace_user_id"
    t.index ["nominator_id"], name: "index_connected_workspace_user_maps_on_nominator_id"
    t.index ["user_id"], name: "index_connected_workspace_user_maps_on_user_id"
  end

  create_table "connected_workspace_users", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "coupons", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "code"
    t.decimal "amount_off", precision: 15, scale: 2
    t.decimal "percent_off", precision: 15, scale: 2
    t.bigint "plan_id"
    t.datetime "redeemed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["plan_id"], name: "index_coupons_on_plan_id"
  end

  create_table "csv_import_chunks", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "account_id"
    t.integer "import_id"
    t.string "type"
    t.integer "sheet_id"
    t.integer "rows_processed", default: 0
    t.integer "total_row_count"
    t.integer "rows_present"
    t.integer "status", default: 0
    t.datetime "job_started_at"
    t.datetime "job_ended_at"
    t.string "last_error"
    t.string "sheet_name"
    t.json "import_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_csv_import_chunks_on_account_id"
    t.index ["employee_id"], name: "index_csv_import_chunks_on_employee_id"
    t.index ["import_id"], name: "index_csv_import_chunks_on_import_id"
  end

  create_table "custom_checkin_fields", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "field_name"
    t.integer "owner_id"
    t.string "owner_type"
    t.integer "field_type"
    t.boolean "active", default: false
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "custom_checkin_option_values", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "custom_checkin_value_id"
    t.bigint "custom_checkin_option_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["custom_checkin_option_id"], name: "index_custom_checkin_option_values_on_custom_checkin_option_id"
    t.index ["custom_checkin_value_id"], name: "index_custom_checkin_option_values_on_custom_checkin_value_id"
  end

  create_table "custom_checkin_options", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "value"
    t.bigint "custom_checkin_field_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["custom_checkin_field_id"], name: "index_custom_checkin_options_on_custom_checkin_field_id"
  end

  create_table "custom_checkin_values", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "value"
    t.bigint "goal_activity_id"
    t.bigint "custom_checkin_field_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["custom_checkin_field_id"], name: "index_custom_checkin_values_on_custom_checkin_field_id"
    t.index ["goal_activity_id"], name: "index_custom_checkin_values_on_goal_activity_id"
  end

  create_table "custom_taggings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "taggable_type"
    t.bigint "taggable_id"
    t.bigint "custom_tag_id"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["custom_tag_id"], name: "index_custom_taggings_on_custom_tag_id"
    t.index ["discarded_at"], name: "index_custom_taggings_on_discarded_at"
    t.index ["taggable_id", "taggable_type", "custom_tag_id"], name: "index_custom_taggings_on_taggable_and_custom_tag", unique: true
    t.index ["taggable_type", "taggable_id"], name: "index_custom_taggings_on_taggable_type_and_taggable_id"
  end

  create_table "custom_tags", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "colour"
    t.bigint "added_by_id"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_custom_tags_on_account_id"
    t.index ["added_by_id"], name: "index_custom_tags_on_added_by_id"
    t.index ["discarded_at"], name: "index_custom_tags_on_discarded_at"
  end

  create_table "data_exports", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "connector_id"
    t.text "connector_url"
    t.integer "connector_type"
    t.datetime "last_updated_on"
    t.string "owner_type"
    t.bigint "owner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "connector_sub_id"
    t.string "metadata_type"
    t.bigint "metadata_id"
    t.bigint "account_id"
    t.index ["account_id"], name: "index_data_exports_on_account_id"
    t.index ["metadata_type", "metadata_id"], name: "index_data_exports_on_metadata_type_and_metadata_id"
    t.index ["owner_type", "owner_id"], name: "index_data_exports_on_owner_type_and_owner_id"
  end

  create_table "delayed_jobs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "priority", default: 0, null: false
    t.integer "attempts", default: 0, null: false
    t.text "handler", limit: **********
    t.text "last_error"
    t.datetime "run_at"
    t.datetime "locked_at"
    t.datetime "failed_at"
    t.string "locked_by"
    t.string "queue"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["priority", "run_at"], name: "delayed_jobs_priority"
  end

  create_table "department_goal_template_categories", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "department_id"
    t.bigint "goal_template_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_id", "goal_template_category_id"], name: "index_dgtc_on_unique_active", unique: true
    t.index ["department_id"], name: "index_dgtc_on_department_id"
    t.index ["goal_template_category_id"], name: "index_dgtc_on_goal_template_category_id"
  end

  create_table "departments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ancestry"
    t.integer "ancestry_depth", default: 0
    t.integer "department_type", default: 0
    t.string "department_type_name"
    t.datetime "discarded_at"
    t.boolean "active", default: true
    t.index ["account_id"], name: "index_departments_on_account_id"
    t.index ["ancestry"], name: "index_departments_on_ancestry"
    t.index ["discarded_at"], name: "index_departments_on_discarded_at"
  end

  create_table "drivers", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "key"
    t.text "explanation"
    t.bigint "parent_driver_id"
    t.text "icon"
    t.string "driver_type"
    t.text "driver_text"
    t.integer "priority", default: 1
    t.integer "root_driver_id"
    t.integer "driver_level"
    t.bigint "account_id"
    t.index ["key"], name: "index_drivers_on_key"
    t.index ["parent_driver_id"], name: "index_drivers_on_parent_driver_id"
  end

  create_table "employee_attribute_values", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_attribute_id", null: false
    t.bigint "employee_id", null: false
    t.string "value"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_employee_attribute_values_on_discarded_at"
    t.index ["employee_attribute_id"], name: "index_employee_attribute_values_on_employee_attribute_id"
    t.index ["employee_id"], name: "index_employee_attribute_values_on_employee_id"
  end

  create_table "employee_attributes", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.string "title"
    t.boolean "enabled", default: true
    t.json "role_access_config"
    t.integer "attr_type", default: 1
    t.boolean "fixed_position", default: false
    t.integer "value_type", default: 0
    t.boolean "copyable", default: false
    t.integer "position"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_employee_attributes_on_account_id"
    t.index ["discarded_at"], name: "index_employee_attributes_on_discarded_at"
  end

  create_table "employee_audit_logs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "field_name"
    t.string "old_value"
    t.string "new_value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_employee_audit_logs_on_employee_id"
  end

  create_table "employee_biz_review_action_items", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "biz_review_action_item_id"
    t.index ["biz_review_action_item_id"], name: "index_employee_action_item_on_action_item"
    t.index ["employee_id"], name: "index_employee_biz_review_action_items_on_employee_id"
  end

  create_table "employee_chat_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_chat_id"
    t.bigint "question_id"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_employee_chat_questions_on_discarded_at"
    t.index ["employee_chat_id"], name: "index_employee_chat_questions_on_employee_chat_id"
    t.index ["question_id"], name: "index_employee_chat_questions_on_question_id"
  end

  create_table "employee_chats", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "schedule_id"
    t.bigint "account_id"
    t.datetime "expires_at"
    t.datetime "run_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "manager_id"
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_employee_chats_on_account_id"
    t.index ["discarded_at"], name: "index_employee_chats_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_chats_on_employee_id"
    t.index ["manager_id"], name: "index_employee_chats_on_manager_id"
    t.index ["schedule_id"], name: "index_employee_chats_on_schedule_id"
  end

  create_table "employee_checkin_comments", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "employee_checkin_logger_id"
    t.text "comment"
    t.bigint "reportee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_checkin_logger_id"], name: "index_employee_checkin_comments_on_employee_checkin_logger_id"
    t.index ["reportee_id"], name: "index_employee_checkin_comments_on_reportee_id"
  end

  create_table "employee_checkin_graphs", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "simple_chat_message_id"
    t.bigint "simple_chat_chatter_id"
    t.float "reportee_score"
    t.float "team_score"
    t.string "reportee_name"
    t.json "key_concerns"
    t.json "key_strengths"
    t.json "rating_trend"
    t.datetime "start_date"
    t.datetime "end_date"
    t.index ["simple_chat_chatter_id"], name: "index_employee_checkin_graphs_on_simple_chat_chatter_id"
    t.index ["simple_chat_message_id"], name: "index_employee_checkin_graphs_on_simple_chat_message_id"
  end

  create_table "employee_checkin_loggers", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "simple_chat_messages_id"
    t.bigint "reportee_id"
    t.bigint "simple_chat_options_id"
    t.bigint "simple_chat_card_id"
    t.bigint "simple_chat_chatter_id"
    t.integer "employee_rating"
    t.string "comment"
    t.string "response_text"
    t.bigint "manager_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["manager_id"], name: "index_employee_checkin_loggers_on_manager_id"
    t.index ["reportee_id"], name: "index_employee_checkin_loggers_on_reportee_id"
    t.index ["simple_chat_card_id"], name: "index_employee_checkin_loggers_on_simple_chat_card_id"
    t.index ["simple_chat_chatter_id"], name: "index_employee_checkin_loggers_on_simple_chat_chatter_id"
    t.index ["simple_chat_messages_id"], name: "index_employee_checkin_loggers_on_simple_chat_messages_id"
    t.index ["simple_chat_options_id"], name: "index_employee_checkin_loggers_on_simple_chat_options_id"
  end

  create_table "employee_data_imports", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "s3_import_file_url"
    t.text "s3_signed_url"
    t.string "s3_import_file_key"
    t.integer "added_count", default: 0
    t.integer "updated_count", default: 0
    t.integer "failure_count", default: 0
    t.text "s3_failed_url"
    t.string "s3_failed_file_key"
    t.integer "upload_status"
    t.bigint "employee_id"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "failure_reason"
    t.string "source", default: "csv"
    t.json "added_employees"
    t.json "updated_employees"
    t.json "failed_employees"
    t.integer "csvbox_import_id"
    t.index ["account_id"], name: "index_employee_data_imports_on_account_id"
    t.index ["employee_id"], name: "index_employee_data_imports_on_employee_id"
  end

  create_table "employee_departments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "department_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_id"], name: "index_employee_departments_on_department_id"
    t.index ["employee_id"], name: "index_employee_departments_on_employee_id"
  end

  create_table "employee_filter_conditions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "table_attribute"
    t.string "operator"
    t.text "value"
    t.text "dynamic_rule"
    t.boolean "default"
    t.bigint "employee_filter_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "json_value"
    t.index ["employee_filter_id"], name: "index_employee_filter_conditions_on_employee_filter_id"
  end

  create_table "employee_filters", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "employee_goals", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "goal_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_employee_goals_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_goals_on_employee_id"
    t.index ["goal_id"], name: "index_employee_goals_on_goal_id"
  end

  create_table "employee_idp_track_positions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.boolean "current"
    t.bigint "employee_id", null: false
    t.bigint "idp_track_position_id", null: false
    t.bigint "previous_position_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_employee_idp_track_positions_on_employee_id"
    t.index ["idp_track_position_id"], name: "index_employee_idp_track_positions_on_idp_track_position_id"
    t.index ["previous_position_id"], name: "index_employee_idp_track_positions_on_previous_position_id"
  end

  create_table "employee_kpis", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "kpi_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_employee_kpis_on_discarded_at"
    t.index ["employee_id"], name: "index_employee_kpis_on_employee_id"
    t.index ["kpi_id"], name: "index_employee_kpis_on_kpi_id"
  end

  create_table "employee_notification_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "notification_key"
    t.string "notification_channel"
    t.boolean "enabled"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_employee_notification_settings_on_employee_id"
  end

  create_table "employee_one_on_one_loggers", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "simple_chat_messages_id"
    t.bigint "reportee_id"
    t.bigint "manager_id"
    t.bigint "simple_chat_card_id"
    t.bigint "simple_chat_chatter_id"
    t.boolean "scheduled", default: false
    t.datetime "scheduled_time"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["manager_id"], name: "index_employee_one_on_one_loggers_on_manager_id"
    t.index ["reportee_id"], name: "index_employee_one_on_one_loggers_on_reportee_id"
    t.index ["simple_chat_card_id"], name: "index_employee_one_on_one_loggers_on_simple_chat_card_id"
    t.index ["simple_chat_chatter_id"], name: "index_employee_one_on_one_loggers_on_simple_chat_chatter_id"
    t.index ["simple_chat_messages_id"], name: "index_employee_one_on_one_loggers_on_simple_chat_messages_id"
  end

  create_table "employee_question_statuses", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "question_id"
    t.integer "status", default: 0
    t.datetime "next_trigger_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_employee_question_statuses_on_employee_id"
    t.index ["question_id"], name: "index_employee_question_statuses_on_question_id"
  end

  create_table "employee_reasons", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "employee_checkin_logger_id"
    t.text "reason"
    t.bigint "reportee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "checkin_reason_id"
    t.index ["checkin_reason_id"], name: "index_employee_reasons_on_checkin_reason_id"
    t.index ["employee_checkin_logger_id"], name: "index_employee_reasons_on_employee_checkin_logger_id"
    t.index ["reportee_id"], name: "index_employee_reasons_on_reportee_id"
  end

  create_table "employee_satisfactions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.bigint "schedule_id"
    t.bigint "employee_id"
    t.bigint "employee_chat_id"
    t.integer "nps_score"
    t.text "comments"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "satisfaction_type", default: "five_star"
    t.bigint "feedback_id"
    t.string "feedback_type"
    t.bigint "question_id"
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_employee_satisfactions_on_account_id"
    t.index ["discarded_at"], name: "index_employee_satisfactions_on_discarded_at"
    t.index ["employee_chat_id"], name: "index_employee_satisfactions_on_employee_chat_id"
    t.index ["employee_id"], name: "index_employee_satisfactions_on_employee_id"
    t.index ["question_id"], name: "index_employee_satisfactions_on_question_id"
    t.index ["schedule_id"], name: "index_employee_satisfactions_on_schedule_id"
  end

  create_table "employees", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "full_name"
    t.datetime "date_of_joining"
    t.string "department_name"
    t.string "gender"
    t.string "location"
    t.bigint "manager_id"
    t.bigint "user_id"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "company_employee_id"
    t.integer "age"
    t.string "title"
    t.string "first_name"
    t.string "level"
    t.string "org_role", default: "employee"
    t.string "phone_number"
    t.string "anonymous_name"
    t.string "division"
    t.string "business_unit"
    t.datetime "date_of_exit"
    t.string "personal_email_address"
    t.string "primary_phone_number"
    t.string "secondary_phone_number"
    t.integer "reportees_count"
    t.boolean "send_manager_checkin", default: false
    t.string "pulse_frequency", default: "before_one_on_one"
    t.string "timezone"
    t.datetime "calendar_data_last_updated_at"
    t.boolean "invited", default: false
    t.boolean "relationship_updated", default: false
    t.datetime "one_on_ones_updated_at"
    t.bigint "department_id"
    t.boolean "run_coffee_connect", default: false
    t.string "profile_picture"
    t.bigint "calendar_event_organizer_id"
    t.integer "cc_meeting_pref", default: 0
    t.string "employment_type", default: ""
    t.boolean "goal_admin", default: false
    t.boolean "account_admin", default: false
    t.string "employee_source"
    t.boolean "nps_enabled", default: true
    t.string "billing_status", default: "to_be_activated"
    t.datetime "activated_at"
    t.datetime "deactivated_at"
    t.index ["account_id"], name: "index_employees_on_account_id"
    t.index ["calendar_event_organizer_id"], name: "index_employees_on_calendar_event_organizer_id"
    t.index ["company_employee_id"], name: "index_employees_on_company_employee_id"
    t.index ["department_id"], name: "index_employees_on_department_id"
    t.index ["manager_id"], name: "index_employees_on_manager_id"
    t.index ["user_id"], name: "index_employees_on_user_id"
  end

  create_table "engagement_reports", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "report_url"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "signed_report_url"
    t.string "upload_status"
    t.string "report_type"
    t.index ["account_id"], name: "index_engagement_reports_on_account_id"
  end

  create_table "event_store_events", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "event_id", limit: 36, null: false
    t.string "event_type", null: false
    t.binary "metadata"
    t.binary "data", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "valid_at", precision: 6
    t.index ["created_at"], name: "index_event_store_events_on_created_at"
    t.index ["event_id"], name: "index_event_store_events_on_event_id", unique: true
    t.index ["event_type"], name: "index_event_store_events_on_event_type"
    t.index ["valid_at"], name: "index_event_store_events_on_valid_at"
  end

  create_table "event_store_events_in_streams", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "stream", null: false
    t.integer "position"
    t.string "event_id", limit: 36, null: false
    t.datetime "created_at", precision: 6, null: false
    t.index ["created_at"], name: "index_event_store_events_in_streams_on_created_at"
    t.index ["stream", "event_id"], name: "index_event_store_events_in_streams_on_stream_and_event_id", unique: true
    t.index ["stream", "position"], name: "index_event_store_events_in_streams_on_stream_and_position", unique: true
  end

  create_table "external_project_syncs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "integration_level"
    t.integer "integration_level_id"
    t.bigint "employee_id"
    t.bigint "account_id"
    t.integer "sync_status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "failure_reason"
    t.text "validation_report_file"
    t.index ["account_id"], name: "index_external_project_syncs_on_account_id"
    t.index ["employee_id"], name: "index_external_project_syncs_on_employee_id"
  end

  create_table "feature_metadata_values", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "metadata_value_id"
    t.string "feature_type"
    t.bigint "feature_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["feature_type", "feature_id"], name: "index_feature_metadata_values_on_feature_type_and_feature_id"
    t.index ["metadata_value_id"], name: "index_feature_metadata_values_on_metadata_value_id"
  end

  create_table "feedback_block_competencies", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "feedback_question_block_id", null: false
    t.bigint "competency_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competency_id"], name: "index_feedback_block_competencies_on_competency_id"
    t.index ["feedback_question_block_id"], name: "index_feedback_block_competencies_on_feedback_question_block_id"
  end

  create_table "feedback_providers", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "feedback_id", null: false
    t.bigint "employee_id", null: false
    t.bigint "feedback_receiver_id", null: false
    t.string "feedback_status", default: "pending"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_feedback_providers_on_discarded_at"
    t.index ["employee_id", "discarded_at"], name: "index_feedback_providers_on_employee_id_and_discarded_at"
    t.index ["employee_id", "feedback_receiver_id", "discarded_at"], name: "idx_fp_employee_receiver_discarded"
    t.index ["employee_id", "feedback_status", "discarded_at"], name: "idx_fp_employee_status_discarded"
    t.index ["employee_id"], name: "index_feedback_providers_on_employee_id"
    t.index ["feedback_id", "employee_id", "feedback_receiver_id", "discarded_at"], name: "idx_fp_feedback_employee_receiver_discarded"
    t.index ["feedback_id"], name: "index_feedback_providers_on_feedback_id"
    t.index ["feedback_receiver_id", "discarded_at"], name: "idx_fp_receiver_discarded"
    t.index ["feedback_receiver_id"], name: "index_feedback_providers_on_feedback_receiver_id"
  end

  create_table "feedback_question_blocks", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "question_id", null: false
    t.string "serviceable_type", null: false
    t.bigint "serviceable_id", null: false
    t.integer "position"
    t.boolean "is_competency_block", default: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["question_id"], name: "index_feedback_question_blocks_on_question_id"
    t.index ["serviceable_type", "serviceable_id", "discarded_at"], name: "idx_feedback_question_blocks_on_serviceable_type_and_id"
  end

  create_table "feedback_receivers", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "feedback_id", null: false
    t.bigint "employee_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_feedback_receivers_on_employee_id"
    t.index ["feedback_id", "employee_id", "discarded_at"], name: "idx_fr_feedback_employee_discarded"
    t.index ["feedback_id", "employee_id"], name: "index_feedback_receivers_on_feedback_id_and_employee_id", unique: true
    t.index ["feedback_id"], name: "index_feedback_receivers_on_feedback_id"
  end

  create_table "feedback_requests", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "creator_id"
    t.bigint "feedback_provider_id"
    t.string "status"
    t.datetime "expires_at"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "message"
    t.index ["creator_id"], name: "index_feedback_requests_on_creator_id"
    t.index ["expires_at", "discarded_at"], name: "index_feedback_requests_on_expires_at_and_discarded_at"
    t.index ["feedback_provider_id"], name: "index_feedback_requests_on_feedback_provider_id"
    t.index ["status", "discarded_at"], name: "index_feedback_requests_on_status_and_discarded_at"
  end

  create_table "feedback_responses", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "feedback_provider_id"
    t.string "feedback_questionable_type"
    t.bigint "feedback_questionable_id"
    t.text "response_text"
    t.float "response_score"
    t.bigint "option_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["feedback_provider_id", "discarded_at"], name: "idx_feedback_responses_on_feedback_provider_id_and_discarded_at"
    t.index ["feedback_provider_id"], name: "index_feedback_responses_on_feedback_provider_id"
    t.index ["feedback_questionable_type", "feedback_questionable_id", "discarded_at"], name: "idx_fr_questionable_type_id_discarded"
    t.index ["option_id"], name: "index_feedback_responses_on_option_id"
  end

  create_table "feedback_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.text "title"
    t.text "description"
    t.bigint "creator_id"
    t.string "template_type"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "template_type"], name: "index_feedback_templates_on_account_id_and_template_type"
    t.index ["account_id"], name: "index_feedback_templates_on_account_id"
    t.index ["creator_id"], name: "index_feedback_templates_on_creator_id"
    t.index ["discarded_at"], name: "index_feedback_templates_on_discarded_at"
    t.index ["template_type"], name: "index_feedback_templates_on_template_type"
  end

  create_table "feedbacks", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.bigint "creator_id", null: false
    t.bigint "feedback_template_id"
    t.string "feedback_template_key"
    t.string "visibility"
    t.string "status", default: "initiated", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "discarded_at"], name: "index_feedbacks_on_account_id_and_discarded_at"
    t.index ["account_id"], name: "index_feedbacks_on_account_id"
    t.index ["creator_id"], name: "index_feedbacks_on_creator_id"
    t.index ["feedback_template_id"], name: "index_feedbacks_on_feedback_template_id"
    t.index ["visibility", "discarded_at"], name: "index_feedbacks_on_visibility_and_discarded_at"
  end

  create_table "follow_up_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "option_id"
    t.string "sentiment"
    t.integer "follow_up_question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["follow_up_question_id"], name: "index_follow_up_questions_on_follow_up_question_id"
    t.index ["option_id"], name: "index_follow_up_questions_on_option_id"
  end

  create_table "global_averages", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.decimal "benchmark", precision: 10
    t.string "benchmarkable_type"
    t.bigint "benchmarkable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "items_count", default: 0
    t.index ["benchmarkable_type", "benchmarkable_id"], name: "index_global_averages_on_benchmarkable_type_and_benchmarkable_id"
  end

  create_table "goal_activities", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "description"
    t.decimal "completed_value", precision: 30, scale: 2
    t.datetime "discarded_at"
    t.bigint "goal_id"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status"
    t.integer "source", default: 0
    t.bigint "source_key_result_id"
    t.datetime "checkin_time"
    t.integer "activity_type", default: 0
    t.boolean "system_comment", default: true
    t.decimal "previous_progress_value", precision: 30, scale: 2
    t.datetime "edited_at"
    t.bigint "edited_by_id"
    t.integer "previous_progress_status"
    t.integer "checkin_type", default: 0
    t.text "old_description"
    t.json "description_json"
    t.integer "previous_progress_type"
    t.integer "progress_type"
    t.bigint "activity_log_id"
    t.integer "milestone_summary_method"
    t.index ["activity_log_id"], name: "index_goal_activities_on_activity_log_id"
    t.index ["discarded_at"], name: "index_goal_activities_on_discarded_at"
    t.index ["edited_by_id"], name: "index_goal_activities_on_edited_by_id"
    t.index ["employee_id"], name: "index_goal_activities_on_employee_id"
    t.index ["goal_id"], name: "index_goal_activities_on_goal_id"
    t.index ["source_key_result_id"], name: "index_goal_activities_on_source_key_result_id"
  end

  create_table "goal_cycles", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "cycle"
    t.string "year"
    t.string "cycle_type"
    t.datetime "start_date"
    t.datetime "end_date"
    t.bigint "account_id"
    t.boolean "active", default: true
    t.index ["account_id"], name: "index_goal_cycles_on_account_id"
  end

  create_table "goal_filters", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "employee_id"
    t.integer "filterable_id"
    t.string "filterable_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.integer "priority", default: 0
    t.index ["discarded_at"], name: "index_goal_filters_on_discarded_at"
    t.index ["filterable_id", "filterable_type", "employee_id"], name: "idx_goal_filters_on_filterable_and_emp", unique: true
  end

  create_table "goal_hierarchies", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "parent_id", null: false
    t.bigint "child_id", null: false
    t.integer "depth", default: 0, null: false
    t.integer "objective_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["child_id"], name: "index_goal_hierarchies_on_child_id"
    t.index ["depth"], name: "index_goal_hierarchies_on_depth"
    t.index ["parent_id"], name: "index_goal_hierarchies_on_parent_id"
  end

  create_table "goal_integration_fields", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.json "query_json"
    t.string "metric_key"
    t.boolean "active", default: true
    t.bigint "goal_id"
    t.bigint "integration_config_id"
    t.bigint "added_by_id"
    t.bigint "integration_field_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "project_id"
    t.integer "integration_level", default: 0
    t.index ["added_by_id"], name: "index_goal_integration_fields_on_added_by_id"
    t.index ["goal_id", "integration_field_id"], name: "goal_and_integration_field_index", unique: true
    t.index ["goal_id"], name: "index_goal_integration_fields_on_goal_id"
    t.index ["integration_config_id"], name: "index_goal_integration_fields_on_integration_config_id"
    t.index ["integration_field_id"], name: "index_goal_integration_fields_on_integration_field_id"
    t.index ["project_id"], name: "index_goal_integration_fields_on_project_id"
  end

  create_table "goal_integration_values", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.json "response_json"
    t.bigint "goal_integration_field_id"
    t.bigint "activity_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_id"], name: "index_goal_integration_values_on_activity_id"
    t.index ["goal_integration_field_id"], name: "index_goal_integration_values_on_goal_integration_field_id"
  end

  create_table "goal_kpis", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "goal_id"
    t.bigint "kpi_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["goal_id"], name: "index_goal_kpis_on_goal_id"
    t.index ["kpi_id"], name: "index_goal_kpis_on_kpi_id"
  end

  create_table "goal_mentions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "goal_activity_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_goal_mentions_on_employee_id"
    t.index ["goal_activity_id"], name: "index_goal_mentions_on_goal_activity_id"
  end

  create_table "goal_milestones", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "goal_id", null: false
    t.bigint "milestone_goal_id"
    t.bigint "goal_cycle_id", null: false
    t.decimal "progress_start", precision: 30, scale: 2
    t.decimal "progress_target", precision: 30, scale: 2
    t.integer "milestone_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["goal_cycle_id"], name: "index_goal_milestones_on_goal_cycle_id"
    t.index ["goal_id"], name: "index_goal_milestones_on_goal_id"
    t.index ["milestone_goal_id"], name: "index_goal_milestones_on_milestone_goal_id"
  end

  create_table "goal_priorities", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "goal_id"
    t.integer "position", null: false
    t.integer "entity_id", null: false
    t.integer "entity_type", null: false
    t.bigint "last_updated_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["entity_id", "entity_type"], name: "index_goal_priorities_on_entity_id_and_entity_type"
    t.index ["goal_id", "entity_id", "entity_type", "position"], name: "index_goal_priority", unique: true
    t.index ["goal_id", "entity_id", "entity_type"], name: "index_goal_priorities_on_goal_id_and_entity_id_and_entity_type", unique: true
    t.index ["goal_id"], name: "index_goal_priorities_on_goal_id"
    t.index ["last_updated_by_id"], name: "index_goal_priorities_on_last_updated_by_id"
  end

  create_table "goal_progress_types", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.boolean "connected_to_children", default: false
    t.decimal "min_val", precision: 30, scale: 2
    t.decimal "max_val", precision: 30, scale: 2
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.index ["discarded_at"], name: "index_goal_progress_types_on_discarded_at"
  end

  create_table "goal_progresses", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.decimal "progress_start", precision: 30, scale: 2
    t.decimal "progress_target", precision: 30, scale: 2
    t.decimal "current_progress", precision: 30, scale: 2
    t.decimal "progress_percent", precision: 30, scale: 2
    t.datetime "discarded_at"
    t.bigint "goal_id"
    t.bigint "goal_progress_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "progress_status", default: 0
    t.decimal "progress_weight", precision: 10, default: "0"
    t.index ["discarded_at"], name: "index_goal_progresses_on_discarded_at"
    t.index ["goal_id"], name: "index_goal_progresses_on_goal_id"
    t.index ["goal_progress_type_id"], name: "index_goal_progresses_on_goal_progress_type_id"
  end

  create_table "goal_table_filter_conditions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "table_attribute"
    t.string "operator"
    t.text "value"
    t.text "dynamic_rule"
    t.boolean "default"
    t.bigint "goal_table_filter_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["goal_table_filter_id"], name: "index_goal_table_filter_conditions_on_goal_table_filter_id"
  end

  create_table "goal_table_filters", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "goal_template_categories", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_goal_template_categories_on_account_id"
  end

  create_table "goal_template_template_categories", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "goal_template_id"
    t.bigint "goal_template_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["goal_template_category_id"], name: "index_gttc_on_goal_template_category_id"
    t.index ["goal_template_id", "goal_template_category_id"], name: "index_gttc_on_unique_active", unique: true
    t.index ["goal_template_id"], name: "index_gttc_on_goal_template_id"
  end

  create_table "goal_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.string "title"
    t.text "description"
    t.integer "progress_type"
    t.string "progress_start"
    t.string "progress_target"
    t.integer "goal_type"
    t.boolean "restricted", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ancestry"
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_goal_templates_on_account_id"
    t.index ["ancestry"], name: "index_goal_templates_on_ancestry"
    t.index ["discarded_at"], name: "index_goal_templates_on_discarded_at"
  end

  create_table "goals", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.boolean "public", default: true
    t.datetime "due_date"
    t.integer "goal_type"
    t.string "department_name"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "progress_percent", precision: 15, scale: 2
    t.bigint "added_by_id"
    t.datetime "discarded_at"
    t.bigint "department_id"
    t.string "ancestry"
    t.integer "ancestry_depth", default: 0
    t.text "description"
    t.bigint "goal_cycle_id"
    t.datetime "start_date"
    t.boolean "is_key_result"
    t.boolean "weighted_goal", default: false
    t.integer "objective_type"
    t.bigint "duplicated_from_id"
    t.boolean "native", default: true
    t.boolean "is_milestone", default: false
    t.boolean "has_milestones", default: false
    t.boolean "has_milestone_goals", default: false
    t.integer "milestone_split_type"
    t.integer "visibility", default: 0
    t.integer "check_in_type"
    t.integer "milestone_summary_method"
    t.index ["account_id"], name: "index_goals_on_account_id"
    t.index ["added_by_id"], name: "index_goals_on_added_by_id"
    t.index ["ancestry"], name: "index_goals_on_ancestry"
    t.index ["department_id"], name: "index_goals_on_department_id"
    t.index ["discarded_at", "goal_cycle_id"], name: "index_goals_on_discarded_at_and_goal_cycle_id"
    t.index ["discarded_at"], name: "index_goals_on_discarded_at"
    t.index ["duplicated_from_id"], name: "index_goals_on_duplicated_from_id"
    t.index ["goal_cycle_id"], name: "index_goals_on_goal_cycle_id"
    t.index ["visibility"], name: "index_goals_on_visibility"
  end

  create_table "goals_departments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "goal_id", null: false
    t.bigint "department_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_id"], name: "index_goals_departments_on_department_id"
    t.index ["goal_id", "department_id"], name: "index_goals_departments_on_goal_id_and_department_id"
    t.index ["goal_id"], name: "index_goals_departments_on_goal_id"
  end

  create_table "goals_onboarding_checklists", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.boolean "goal_added", default: false
    t.boolean "progress_checkin_done", default: false
    t.boolean "explored_demo", default: false
    t.boolean "team_invited", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_goals_onboarding_checklists_on_employee_id", unique: true
  end

  create_table "goals_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "reminder_frequency"
    t.bigint "account_id"
    t.datetime "reminder_last_sent_on"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "reminder_time"
    t.integer "reminder_day"
    t.bigint "department_id"
    t.datetime "next_reminder_at"
    t.boolean "active", default: true
    t.string "time"
    t.string "day_of_month"
    t.bigint "employee_id"
    t.boolean "disable_department_creation", default: false, null: false
    t.index ["account_id"], name: "index_goals_settings_on_account_id"
    t.index ["department_id"], name: "index_goals_settings_on_department_id"
    t.index ["employee_id"], name: "index_goals_settings_on_employee_id"
  end

  create_table "health_scores", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.date "score_date"
    t.float "aggregate_score"
    t.float "usage_score"
    t.float "one_on_one_score"
    t.float "goals_score"
    t.float "engagement_score"
    t.float "coffee_connect_score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_health_scores_on_account_id"
  end

  create_table "home_page_actions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "employee_id"
    t.datetime "start_date"
    t.datetime "end_date"
    t.string "action_type"
    t.json "content"
    t.string "actionable_type"
    t.bigint "actionable_id"
    t.datetime "discarded_at"
    t.string "pageable_type"
    t.bigint "pageable_id"
    t.index ["actionable_type", "actionable_id"], name: "index_home_page_actions_on_actionable_type_and_actionable_id"
    t.index ["discarded_at"], name: "index_home_page_actions_on_discarded_at"
    t.index ["employee_id"], name: "index_home_page_actions_on_employee_id"
    t.index ["pageable_type", "pageable_id"], name: "index_home_page_actions_on_pageable_type_and_pageable_id"
  end

  create_table "hris_configs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "url"
    t.string "domain"
    t.string "source"
    t.json "config_json"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "last_synced_at"
    t.boolean "enable_sync", default: true
    t.integer "sync_frequency", default: 3
    t.index ["account_id"], name: "index_hris_configs_on_account_id"
  end

  create_table "idp_teams", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "account_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_idp_teams_on_account_id"
    t.index ["name"], name: "index_idp_teams_on_name"
  end

  create_table "idp_track_position_competencies", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.float "weightage", default: 0.0
    t.bigint "idp_track_position_id", null: false
    t.bigint "competency_id", null: false
    t.bigint "competency_level_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competency_id"], name: "index_idp_track_position_competencies_on_competency_id"
    t.index ["competency_level_id"], name: "index_idp_track_position_competencies_on_competency_level_id"
    t.index ["idp_track_position_id"], name: "index_idp_track_position_competencies_on_idp_track_position_id"
  end

  create_table "idp_track_positions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "label"
    t.integer "level", null: false
    t.bigint "idp_track_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["idp_track_id"], name: "index_idp_track_positions_on_idp_track_id"
    t.index ["name"], name: "index_idp_track_positions_on_name"
  end

  create_table "idp_tracks", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "idp_team_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["idp_team_id"], name: "index_idp_tracks_on_idp_team_id"
    t.index ["name"], name: "index_idp_tracks_on_name"
  end

  create_table "insights", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "insight_text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "account_id"
    t.text "subject"
    t.integer "votes"
    t.index ["account_id"], name: "index_insights_on_account_id"
  end

  create_table "integration_configs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.boolean "shared", default: true
    t.json "auth_json"
    t.bigint "account_id"
    t.bigint "added_by_id"
    t.bigint "integration_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_integration_configs_on_account_id"
    t.index ["added_by_id"], name: "index_integration_configs_on_added_by_id"
    t.index ["integration_id"], name: "index_integration_configs_on_integration_id"
  end

  create_table "integration_fields", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "field_type"
    t.bigint "integration_id"
    t.index ["field_type", "integration_id"], name: "index_integration_fields_on_field_type_and_integration_id", unique: true
    t.index ["integration_id"], name: "index_integration_fields_on_integration_id"
  end

  create_table "integrations", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "integration_type"
    t.string "key", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
  end

  create_table "ints", id: false, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "i", limit: 1
  end

  create_table "key_result_checkins", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "description"
    t.decimal "completed_value", precision: 15, scale: 2
    t.bigint "key_result_id"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_key_result_checkins_on_discarded_at"
    t.index ["employee_id"], name: "index_key_result_checkins_on_employee_id"
    t.index ["key_result_id"], name: "index_key_result_checkins_on_key_result_id"
  end

  create_table "key_results", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.datetime "due_date"
    t.integer "progress_type"
    t.decimal "progress_start", precision: 15, scale: 2
    t.decimal "progress_end", precision: 15, scale: 2
    t.decimal "current_progress", precision: 15, scale: 2
    t.boolean "completed", default: false
    t.bigint "goal_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "progress_percent", precision: 15, scale: 2
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_key_results_on_discarded_at"
    t.index ["goal_id"], name: "index_key_results_on_goal_id"
  end

  create_table "kpi_activities", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "kpi_id"
    t.string "description"
    t.float "current_value"
    t.float "current_target"
    t.float "previous_value"
    t.float "previous_target"
    t.bigint "employee_id"
    t.datetime "checkin_time"
    t.integer "activity_type"
    t.datetime "edited_at"
    t.bigint "edited_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_kpi_activities_on_discarded_at"
    t.index ["edited_by_id"], name: "index_kpi_activities_on_edited_by_id"
    t.index ["employee_id"], name: "index_kpi_activities_on_employee_id"
    t.index ["kpi_id"], name: "index_kpi_activities_on_kpi_id"
  end

  create_table "kpi_units", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "symbol"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_kpi_units_on_discarded_at"
  end

  create_table "kpis", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.integer "visibility"
    t.integer "summarize_with"
    t.integer "kpi_type"
    t.bigint "department_id"
    t.bigint "account_id"
    t.bigint "added_by_id"
    t.bigint "kpi_unit_id"
    t.integer "relation_to_target"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_kpis_on_account_id"
    t.index ["added_by_id"], name: "index_kpis_on_added_by_id"
    t.index ["department_id"], name: "index_kpis_on_department_id"
    t.index ["discarded_at"], name: "index_kpis_on_discarded_at"
    t.index ["kpi_unit_id"], name: "index_kpis_on_kpi_unit_id"
  end

  create_table "manager_depts", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "simple_chat_message_id"
    t.bigint "simple_chat_chatter_id"
    t.float "dept_score"
    t.string "dept_name"
    t.string "driver_name"
    t.text "question_text"
    t.index ["simple_chat_chatter_id"], name: "index_manager_depts_on_simple_chat_chatter_id"
    t.index ["simple_chat_message_id"], name: "index_manager_depts_on_simple_chat_message_id"
  end

  create_table "manager_feedback_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "manager_id"
    t.json "questions"
    t.bigint "simple_chat_chatter_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["manager_id"], name: "index_manager_feedback_questions_on_manager_id"
    t.index ["simple_chat_chatter_id"], name: "index_manager_feedback_questions_on_simple_chat_chatter_id"
  end

  create_table "marketing_leads", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_marketing_leads_on_email"
    t.index ["user_id"], name: "index_marketing_leads_on_user_id"
  end

  create_table "meeting_attendees", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "attendee_email"
    t.integer "attendee_type"
    t.bigint "employee_id"
    t.bigint "meeting_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_meeting_attendees_on_employee_id"
    t.index ["meeting_id"], name: "index_meeting_attendees_on_meeting_id"
  end

  create_table "meeting_categories", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "meeting_extra_infos", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "tagging_id_one"
    t.bigint "tagging_id_two"
    t.bigint "meeting_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "meeting_info_type"
    t.bigint "option_id"
    t.index ["meeting_id"], name: "index_meeting_extra_infos_on_meeting_id"
  end

  create_table "meeting_reminders", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.boolean "reminder_sent", default: false
    t.integer "reminder_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_meeting_reminders_on_employee_id"
  end

  create_table "meeting_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.text "template_content"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_meeting_templates_on_account_id"
  end

  create_table "meetings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.datetime "start_time"
    t.datetime "end_time"
    t.boolean "archived", default: false
    t.bigint "meeting_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "occur", default: false
    t.text "video_call_url"
    t.integer "meeting_status"
    t.index ["meeting_category_id"], name: "index_meetings_on_meeting_category_id"
  end

  create_table "mentions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_mentions_on_employee_id"
    t.index ["resource_type", "resource_id"], name: "index_mentions_on_resource_type_and_resource_id"
  end

  create_table "messages", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.bigint "response_id"
    t.text "message"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_messages_on_employee_id"
    t.index ["response_id"], name: "index_messages_on_response_id"
  end

  create_table "metabase_groups", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.bigint "employee_id"
    t.string "metabase_email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_metabase_groups_on_employee_id"
  end

  create_table "metabase_reports", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.integer "metabase_dashboard_id"
    t.string "title"
    t.integer "height"
    t.string "dashboard_type", default: "dashboard"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "params"
    t.bigint "employee_id"
    t.string "reportable_type"
    t.bigint "reportable_id"
    t.index ["account_id"], name: "index_metabase_reports_on_account_id"
    t.index ["employee_id"], name: "index_metabase_reports_on_employee_id"
    t.index ["reportable_type", "reportable_id"], name: "index_metabase_reports_on_reportable_type_and_reportable_id"
  end

  create_table "metadata_fields", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "field_name"
    t.integer "field_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "account_id"
    t.integer "metadata_type"
    t.integer "field_source"
    t.boolean "sortable", default: true
    t.boolean "hidable", default: true
    t.bigint "created_by_id"
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_metadata_fields_on_account_id"
    t.index ["created_by_id"], name: "index_metadata_fields_on_created_by_id"
    t.index ["discarded_at"], name: "index_metadata_fields_on_discarded_at"
  end

  create_table "metadata_options", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "value"
    t.bigint "metadata_field_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "external_id"
    t.text "text_value"
    t.text "link_value"
    t.integer "employee_id_value"
    t.datetime "date_field_value"
    t.float "number_field_value"
    t.index ["metadata_field_id"], name: "index_metadata_options_on_metadata_field_id"
  end

  create_table "metadata_value_metadata_options", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "metadata_value_id"
    t.bigint "metadata_option_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["metadata_option_id"], name: "index_metadata_value_metadata_options_on_metadata_option_id"
    t.index ["metadata_value_id"], name: "index_metadata_value_metadata_options_on_metadata_value_id"
  end

  create_table "metadata_values", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "metadata_field_id"
    t.text "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "owner_type"
    t.bigint "owner_id"
    t.bigint "goal_activity_id"
    t.text "text_value"
    t.text "link_value"
    t.integer "employee_id_value"
    t.datetime "date_field_value"
    t.float "number_field_value"
    t.bigint "created_by_id"
    t.datetime "discarded_at"
    t.index ["created_by_id"], name: "index_metadata_values_on_created_by_id"
    t.index ["discarded_at"], name: "index_metadata_values_on_discarded_at"
    t.index ["goal_activity_id"], name: "index_metadata_values_on_goal_activity_id"
    t.index ["metadata_field_id"], name: "index_metadata_values_on_metadata_field_id"
    t.index ["owner_type", "owner_id"], name: "index_metadata_values_on_owner_type_and_owner_id"
  end

  create_table "microsoft_teams", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "service_url"
    t.string "channel_id"
    t.string "channel_name"
    t.string "team_id"
    t.string "team_name"
    t.string "tenant_id"
    t.bigint "account_id"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_microsoft_teams_on_account_id"
    t.index ["discarded_at"], name: "index_microsoft_teams_on_discarded_at"
    t.index ["employee_id"], name: "index_microsoft_teams_on_employee_id"
    t.index ["team_id"], name: "index_microsoft_teams_on_team_id"
    t.index ["tenant_id"], name: "index_microsoft_teams_on_tenant_id"
  end

  create_table "nova_chat_messages", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "nova_conversation_id", null: false
    t.string "role", null: false
    t.text "message", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_nova_chat_messages_on_discarded_at"
    t.index ["nova_conversation_id"], name: "index_nova_chat_messages_on_nova_conversation_id"
  end

  create_table "nova_conversations", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.bigint "account_ai_model_config_id", null: false
    t.string "name", default: "Untitled Conversation", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_ai_model_config_id"], name: "index_nova_conversations_on_account_ai_model_config_id"
    t.index ["discarded_at"], name: "index_nova_conversations_on_discarded_at"
    t.index ["employee_id"], name: "index_nova_conversations_on_employee_id"
  end

  create_table "okr_update_goal_activity_mappings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "okr_update_id"
    t.bigint "goal_activity_id"
    t.bigint "goal_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["goal_activity_id"], name: "index_okr_update_goal_activity_mappings_on_goal_activity_id"
    t.index ["goal_id"], name: "index_okr_update_goal_activity_mappings_on_goal_id"
    t.index ["okr_update_id"], name: "index_okr_update_goal_activity_mappings_on_okr_update_id"
  end

  create_table "okr_update_recipients", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "okr_update_id", null: false
    t.string "recipient_type", null: false
    t.bigint "recipient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["okr_update_id"], name: "index_okr_update_recipients_on_okr_update_id"
    t.index ["recipient_type", "recipient_id"], name: "index_okr_update_recipients_on_recipient_type_and_recipient_id"
  end

  create_table "okr_updates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "resource_type"
    t.bigint "resource_id"
    t.bigint "goals_setting_id"
    t.integer "status", default: 0
    t.integer "current_step", default: 0
    t.json "summary_json"
    t.text "summary_html"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "created_by_id"
    t.integer "posted_by_id"
    t.index ["goals_setting_id"], name: "index_okr_updates_on_goals_setting_id"
    t.index ["resource_type", "resource_id"], name: "index_okr_updates_on_resource_type_and_resource_id"
  end

  create_table "okr_updates_goal_cycles", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "okr_update_id"
    t.bigint "goal_cycle_id"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["goal_cycle_id"], name: "index_okr_updates_goal_cycles_on_goal_cycle_id"
    t.index ["okr_update_id", "goal_cycle_id"], name: "index_okr_updates_goal_cycles_on_okr_update_id_and_goal_cycle_id", unique: true
    t.index ["okr_update_id"], name: "index_okr_updates_goal_cycles_on_okr_update_id"
  end

  create_table "okr_updates_goals", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "okr_update_id"
    t.bigint "goal_id"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["goal_id"], name: "index_okr_updates_goals_on_goal_id"
    t.index ["okr_update_id", "goal_id"], name: "index_okr_updates_goals_on_okr_update_id_and_goal_id", unique: true
    t.index ["okr_update_id"], name: "index_okr_updates_goals_on_okr_update_id"
  end

  create_table "omnichat_block_types", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "block_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "omnichat_message_type_id"
    t.index ["omnichat_message_type_id"], name: "index_omnichat_block_types_on_omnichat_message_type_id"
  end

  create_table "omnichat_blocks", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "block_id"
    t.json "block_content"
    t.bigint "omnichat_conversation_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "omnichat_block_type_id"
    t.bigint "omnichat_log_id"
    t.bigint "source_id"
    t.string "source_type"
    t.index ["block_id"], name: "index_omnichat_blocks_on_block_id"
    t.index ["omnichat_block_type_id"], name: "index_omnichat_blocks_on_omnichat_block_type_id"
    t.index ["omnichat_conversation_id"], name: "index_omnichat_blocks_on_omnichat_conversation_id"
    t.index ["omnichat_log_id"], name: "index_omnichat_blocks_on_omnichat_log_id"
  end

  create_table "omnichat_conversations", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "conversation_id"
    t.integer "conversation_category"
    t.string "name"
    t.bigint "conversationable_id"
    t.string "conversationable_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "feature_id"
    t.string "feature_type"
    t.bigint "members_count"
    t.datetime "discarded_at"
    t.index ["conversation_id"], name: "index_omnichat_conversations_on_conversation_id"
    t.index ["discarded_at", "conversation_category", "conversationable_id", "conversationable_type"], name: "idx_omnichat_convo_on_discarded_at_category_conversationable"
    t.index ["discarded_at"], name: "index_omnichat_conversations_on_discarded_at"
  end

  create_table "omnichat_logs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "message_id"
    t.json "message_sent"
    t.json "message_slack_parsed"
    t.bigint "omnichat_conversation_id"
    t.bigint "omnichat_message_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "log_source", default: "peoplebox"
    t.string "trackable_type"
    t.bigint "trackable_id"
    t.index ["message_id"], name: "index_omnichat_logs_on_message_id"
    t.index ["omnichat_conversation_id"], name: "index_omnichat_logs_on_omnichat_conversation_id"
    t.index ["omnichat_message_type_id"], name: "index_omnichat_logs_on_omnichat_message_type_id"
    t.index ["trackable_type", "trackable_id"], name: "index_omnichat_logs_on_trackable_type_and_trackable_id"
  end

  create_table "omnichat_message_types", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "message_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "omnichat_users", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "team_user_id"
    t.string "first_name"
    t.string "last_name"
    t.string "email"
    t.string "user_role"
    t.bigint "userable_id"
    t.string "userable_type"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "chat_handle"
    t.text "profile_picture_url"
    t.datetime "discarded_at"
    t.index ["discarded_at", "employee_id"], name: "index_omnichat_users_on_discarded_at_and_employee_id"
    t.index ["discarded_at"], name: "index_omnichat_users_on_discarded_at"
    t.index ["employee_id"], name: "index_omnichat_users_on_employee_id"
    t.index ["team_user_id"], name: "index_omnichat_users_on_team_user_id"
  end

  create_table "onboardings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.boolean "welcome_screen", default: true
    t.boolean "one_on_one_created", default: false
    t.boolean "relationship_added", default: false
    t.boolean "report_invited", default: false
    t.boolean "performance_rating", default: false
    t.boolean "talking_points", default: false
    t.boolean "action_items", default: false
    t.boolean "slack_integrated", default: false
    t.integer "completed_steps_count", default: 0
    t.boolean "onboarding_complete", default: false
    t.integer "onboarding_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_onboardings_on_employee_id"
  end

  create_table "one_on_one_action_items", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "item_text"
    t.boolean "private", default: false
    t.bigint "one_on_one_id"
    t.bigint "super_list_item_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "owner_id"
    t.bigint "other_party_id"
    t.boolean "complete", default: false
    t.index ["one_on_one_id"], name: "index_one_on_one_action_items_on_one_on_one_id"
    t.index ["other_party_id"], name: "index_one_on_one_action_items_on_other_party_id"
    t.index ["owner_id"], name: "index_one_on_one_action_items_on_owner_id"
    t.index ["super_list_item_id"], name: "index_one_on_one_action_items_on_super_list_item_id"
  end

  create_table "one_on_one_agenda_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "category"
    t.text "talking_point"
    t.integer "talking_point_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "parent_agenda_id"
    t.integer "bookmark_count", default: 0
    t.datetime "manually_recommended_at"
    t.integer "account_id"
    t.boolean "is_system", default: true
    t.integer "position"
    t.boolean "hidden", default: false
    t.datetime "discarded_at"
    t.bigint "suggested_talking_point_category_id"
    t.index ["category"], name: "index_one_on_one_agenda_templates_on_category"
    t.index ["discarded_at"], name: "index_one_on_one_agenda_templates_on_discarded_at"
    t.index ["parent_agenda_id"], name: "index_one_on_one_agenda_templates_on_parent_agenda_id"
    t.index ["suggested_talking_point_category_id"], name: "index_one_on_one_agenda_templates_on_suggested_tpc"
  end

  create_table "one_on_one_agendas", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.boolean "private", default: false
    t.bigint "one_on_one_id"
    t.bigint "super_list_item_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "owner_id"
    t.bigint "other_party_id"
    t.boolean "complete", default: false
    t.bigint "one_on_one_note_id"
    t.string "talking_point_item_id"
    t.bigint "one_on_one_agenda_template_id"
    t.index ["one_on_one_agenda_template_id"], name: "index_one_on_one_agendas_on_one_on_one_agenda_template_id"
    t.index ["one_on_one_id"], name: "index_one_on_one_agendas_on_one_on_one_id"
    t.index ["one_on_one_note_id"], name: "index_one_on_one_agendas_on_one_on_one_note_id"
    t.index ["other_party_id"], name: "index_one_on_one_agendas_on_other_party_id"
    t.index ["owner_id"], name: "index_one_on_one_agendas_on_owner_id"
    t.index ["super_list_item_id"], name: "index_one_on_one_agendas_on_super_list_item_id"
    t.index ["talking_point_item_id"], name: "index_one_on_one_agendas_on_talking_point_item_id"
  end

  create_table "one_on_one_note_versions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.json "notes_json_text"
    t.integer "last_modified_by"
    t.string "rev_type"
    t.bigint "one_on_one_note_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["one_on_one_note_id"], name: "index_one_on_one_note_versions_on_one_on_one_note_id"
  end

  create_table "one_on_one_notes", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "note_text"
    t.boolean "private", default: true
    t.bigint "one_on_one_id"
    t.bigint "owner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "notes_json_text"
    t.integer "last_modified_by"
    t.boolean "edited", default: false
    t.text "markdown"
    t.integer "action_items_count", default: 0
    t.integer "talking_points_count", default: 0
    t.index ["one_on_one_id"], name: "index_one_on_one_notes_on_one_on_one_id"
    t.index ["owner_id"], name: "index_one_on_one_notes_on_owner_id"
  end

  create_table "one_on_one_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.boolean "shared_notes_visibility", default: false
    t.boolean "productivity_tracking", default: true
    t.bigint "account_id"
    t.boolean "completion_tracking", default: true
    t.boolean "manager_completion_only", default: false
    t.datetime "notes_visibility_changed_at"
    t.string "suggested_talking_points_box_type", default: "system"
    t.boolean "completion_email", default: false
    t.json "shared_notes_visibility_config_json"
    t.index ["account_id"], name: "index_one_on_one_settings_on_account_id"
  end

  create_table "one_on_ones", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.datetime "scheduled_time"
    t.bigint "creator_id"
    t.bigint "reportee_id"
    t.bigint "manager_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "cal_id"
    t.datetime "start_time"
    t.datetime "end_time"
    t.boolean "employee_reminder", default: false
    t.boolean "manager_reminder", default: false
    t.integer "status", default: 0
    t.text "ical_uid"
    t.text "recurring_event_id"
    t.bigint "completed_by_id"
    t.datetime "completed_at"
    t.integer "one_on_one_type"
    t.index ["cal_id"], name: "index_one_on_ones_on_cal_id", unique: true
    t.index ["creator_id"], name: "index_one_on_ones_on_creator_id"
    t.index ["ical_uid"], name: "index_one_on_ones_on_ical_uid", length: 500
    t.index ["manager_id"], name: "index_one_on_ones_on_manager_id"
    t.index ["recurring_event_id"], name: "index_one_on_ones_on_recurring_event_id", length: 500
    t.index ["reportee_id"], name: "index_one_on_ones_on_reportee_id"
  end

  create_table "options", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "value"
    t.bigint "question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "legend"
    t.bigint "driver_id"
    t.boolean "is_others_option", default: false
    t.boolean "score_visible", default: true
    t.index ["driver_id"], name: "index_options_on_driver_id"
    t.index ["question_id"], name: "index_options_on_question_id"
  end

  create_table "payments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.bigint "subscription_id"
    t.decimal "amount", precision: 15, scale: 2
    t.decimal "amount_inr", precision: 15, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "amount_payable", precision: 15, scale: 2
    t.bigint "coupon_id"
    t.index ["account_id"], name: "index_payments_on_account_id"
    t.index ["coupon_id"], name: "index_payments_on_coupon_id"
    t.index ["subscription_id"], name: "index_payments_on_subscription_id"
  end

  create_table "pbx_credit_ledgers", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.boolean "active", default: true
    t.bigint "account_id", null: false
    t.bigint "depositor_id"
    t.string "deposit_source", null: false
    t.decimal "deposit", precision: 10, scale: 4, null: false
    t.decimal "balance", precision: 10, scale: 4, null: false
    t.datetime "expiry_date", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_pbx_credit_ledgers_on_account_id"
    t.index ["depositor_id"], name: "index_pbx_credit_ledgers_on_depositor_id"
    t.index ["discarded_at"], name: "index_pbx_credit_ledgers_on_discarded_at"
  end

  create_table "pbx_credit_usage_logs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "pbx_credit_ledger_id", null: false
    t.bigint "employee_id", null: false
    t.decimal "credits_debited", precision: 10, scale: 4, null: false
    t.decimal "balance_post_debit", precision: 10, scale: 4, null: false
    t.string "serviceable_type", null: false
    t.bigint "serviceable_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_pbx_credit_usage_logs_on_discarded_at"
    t.index ["employee_id"], name: "index_pbx_credit_usage_logs_on_employee_id"
    t.index ["pbx_credit_ledger_id"], name: "index_pbx_credit_usage_logs_on_pbx_credit_ledger_id"
    t.index ["serviceable_type", "serviceable_id"], name: "index_pbx_credit_usage_logs_on_serviceable"
  end

  create_table "pdf_configurations", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "pdf_type", null: false
    t.json "settings"
    t.string "configurable_type"
    t.bigint "configurable_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["configurable_type", "configurable_id"], name: "index_pdf_configurations_configurable"
    t.index ["pdf_type", "configurable_id", "configurable_type", nil], name: "index_pdf_configurations_pdf_type_configurable", unique: true
  end

  create_table "peer_configs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id", null: false
    t.bigint "peer_selection_role_id"
    t.bigint "peer_approval_role_id"
    t.boolean "selection_required", default: false, null: false
    t.boolean "approval_required", default: false, null: false
    t.integer "min_reviewers"
    t.integer "max_reviewers"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_peer_configs_on_discarded_at"
    t.index ["peer_approval_role_id"], name: "index_peer_configs_on_peer_approval_role_id"
    t.index ["peer_selection_role_id"], name: "index_peer_configs_on_peer_selection_role_id"
    t.index ["review_cycle_id"], name: "index_peer_configs_on_review_cycle_id"
  end

  create_table "perception_gaps", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.integer "question_id"
    t.string "question_text"
    t.float "manager_rating"
    t.float "team_rating"
    t.bigint "manager_id"
    t.bigint "simple_chat_chatter_id"
    t.bigint "simple_chat_message_id"
    t.string "driver_name"
    t.string "performance_text"
    t.string "perception_text"
    t.text "perception_graph_sentence"
    t.index ["manager_id"], name: "index_perception_gaps_on_manager_id"
    t.index ["simple_chat_chatter_id"], name: "index_perception_gaps_on_simple_chat_chatter_id"
    t.index ["simple_chat_message_id"], name: "index_perception_gaps_on_simple_chat_message_id"
  end

  create_table "performance_ratings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "rating", null: false
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "raw_rating", limit: 53
    t.index ["employee_id"], name: "index_performance_ratings_on_employee_id"
  end

  create_table "plans", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.decimal "price", precision: 15, scale: 2
    t.string "code"
    t.string "plan_type", default: "per_seat"
    t.integer "max_seats"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "validity_days"
    t.integer "usage_limit"
    t.boolean "visibility", default: false
    t.string "billing_cycle"
    t.string "currency_code"
  end

  create_table "product_action_hierarchies", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "parent_action_id", null: false
    t.bigint "sub_action_id", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_product_action_hierarchies_on_discarded_at"
    t.index ["parent_action_id"], name: "index_product_action_hierarchies_on_parent_action_id"
    t.index ["sub_action_id"], name: "index_product_action_hierarchies_on_sub_action_id"
  end

  create_table "product_actions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "key", null: false
    t.string "name", null: false
    t.boolean "active", default: true, null: false
    t.bigint "product_id"
    t.datetime "discarded_at"
    t.bigint "discarded_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "description"
    t.index ["discarded_by_id"], name: "index_product_actions_on_discarded_by_id"
    t.index ["key"], name: "index_product_actions_on_key", unique: true
    t.index ["product_id"], name: "index_product_actions_on_product_id"
  end

  create_table "products", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "key", null: false
    t.string "name", null: false
    t.datetime "discarded_at"
    t.bigint "discarded_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_by_id"], name: "index_products_on_discarded_by_id"
    t.index ["key"], name: "index_products_on_key", unique: true
  end

  create_table "profile_configurations", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id", null: false
    t.json "config_json", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_profile_configurations_on_account_id"
    t.index ["discarded_at"], name: "index_profile_configurations_on_discarded_at"
  end

  create_table "project_integration_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.bigint "integration_id"
    t.bigint "integration_config_id"
    t.json "field_mapping"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_project_integration_settings_on_account_id"
    t.index ["integration_config_id"], name: "index_project_integration_settings_on_integration_config_id"
    t.index ["integration_id"], name: "index_project_integration_settings_on_integration_id"
  end

  create_table "pulse_badges", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "badge_label"
    t.string "logo"
    t.text "badge_description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "pulse_comments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.text "comment_text"
    t.string "commentable_type"
    t.bigint "commentable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["commentable_type", "commentable_id"], name: "index_pulse_comments_on_commentable_type_and_commentable_id"
    t.index ["discarded_at"], name: "index_pulse_comments_on_discarded_at"
    t.index ["employee_id"], name: "index_pulse_comments_on_employee_id"
  end

  create_table "pulse_likes", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "employee_id"
    t.string "likeable_type"
    t.bigint "likeable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_pulse_likes_on_discarded_at"
    t.index ["employee_id"], name: "index_pulse_likes_on_employee_id"
    t.index ["likeable_type", "likeable_id"], name: "index_pulse_likes_on_likeable_type_and_likeable_id"
  end

  create_table "pulse_praise_receivers", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "pulse_praise_id"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id"], name: "index_pulse_praise_receivers_on_employee_id"
    t.index ["pulse_praise_id"], name: "index_pulse_praise_receivers_on_pulse_praise_id"
  end

  create_table "pulse_praises", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.bigint "employee_id"
    t.bigint "pulse_badge_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_private", default: false
    t.index ["employee_id"], name: "index_pulse_praises_on_employee_id"
    t.index ["pulse_badge_id"], name: "index_pulse_praises_on_pulse_badge_id"
  end

  create_table "pulse_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "question_id"
    t.bigint "pulse_id"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_pulse_questions_on_discarded_at"
    t.index ["pulse_id"], name: "index_pulse_questions_on_pulse_id"
    t.index ["question_id"], name: "index_pulse_questions_on_question_id"
  end

  create_table "pulse_reasons", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "reason_text"
    t.string "reason_type"
    t.integer "linked_rating"
    t.bigint "question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "driver_name"
    t.text "super_list_text"
    t.text "question_text"
    t.index ["question_id"], name: "index_pulse_reasons_on_question_id"
  end

  create_table "pulse_response_meta_infos", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "pulse_response_id"
    t.bigint "pulse_id"
    t.bigint "pulse_reason_id"
    t.text "meta_info_content"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "need_help", default: false
    t.integer "reason_adjective"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_pulse_response_meta_infos_on_discarded_at"
    t.index ["pulse_id"], name: "index_pulse_response_meta_infos_on_pulse_id"
    t.index ["pulse_reason_id"], name: "index_pulse_response_meta_infos_on_pulse_reason_id"
    t.index ["pulse_response_id"], name: "index_pulse_response_meta_infos_on_pulse_response_id"
  end

  create_table "pulse_responses", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "question_id"
    t.bigint "employee_id"
    t.bigint "pulse_id"
    t.bigint "schedule_id"
    t.bigint "option_id"
    t.integer "score"
    t.text "response_text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "pulse_question_id"
    t.integer "priority_position"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_pulse_responses_on_discarded_at"
    t.index ["employee_id"], name: "index_pulse_responses_on_employee_id"
    t.index ["option_id"], name: "index_pulse_responses_on_option_id"
    t.index ["pulse_id"], name: "index_pulse_responses_on_pulse_id"
    t.index ["pulse_question_id"], name: "index_pulse_responses_on_pulse_question_id"
    t.index ["question_id"], name: "index_pulse_responses_on_question_id"
    t.index ["schedule_id"], name: "index_pulse_responses_on_schedule_id"
  end

  create_table "pulse_template_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "pulse_template_id"
    t.bigint "question_id"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["pulse_template_id"], name: "index_pulse_template_questions_on_pulse_template_id"
    t.index ["question_id"], name: "index_pulse_template_questions_on_question_id"
  end

  create_table "pulse_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "owner_type"
    t.bigint "owner_id"
    t.integer "owner_role"
    t.integer "template_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["owner_type", "owner_id"], name: "index_pulse_templates_on_owner_type_and_owner_id"
  end

  create_table "pulses", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "schedule_id"
    t.bigint "account_id"
    t.bigint "employee_id"
    t.bigint "manager_id"
    t.bigint "reportee_id"
    t.datetime "start_date"
    t.datetime "end_date"
    t.integer "status", default: 0
    t.integer "pulse_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "submitted_on"
    t.datetime "reviewed_on"
    t.bigint "one_on_one_id"
    t.string "pulseable_type"
    t.bigint "pulseable_id"
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_pulses_on_account_id"
    t.index ["discarded_at"], name: "index_pulses_on_discarded_at"
    t.index ["employee_id"], name: "index_pulses_on_employee_id"
    t.index ["manager_id"], name: "index_pulses_on_manager_id"
    t.index ["one_on_one_id"], name: "index_pulses_on_one_on_one_id"
    t.index ["pulseable_type", "pulseable_id"], name: "index_pulses_on_pulseable_type_and_pulseable_id"
    t.index ["reportee_id"], name: "index_pulses_on_reportee_id"
    t.index ["schedule_id"], name: "index_pulses_on_schedule_id"
  end

  create_table "question_rounding_limits", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "question_id"
    t.decimal "min_score", precision: 5, scale: 2
    t.decimal "max_score", precision: 5, scale: 2
    t.decimal "rounded_score", precision: 5, scale: 2
    t.string "legend"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["question_id"], name: "index_question_rounding_limits_on_question_id"
  end

  create_table "question_stack_traces", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "campaign_id"
    t.bigint "employee_id"
    t.integer "parent_question_id"
    t.integer "child_question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "exclude_options"
    t.string "transition_message"
    t.boolean "separate_transition_message"
    t.bigint "employee_chat_id"
    t.index ["campaign_id"], name: "index_question_stack_traces_on_campaign_id"
    t.index ["employee_chat_id"], name: "index_question_stack_traces_on_employee_chat_id"
    t.index ["employee_id"], name: "index_question_stack_traces_on_employee_id"
  end

  create_table "question_to_question_mappings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "prev_question_id"
    t.integer "next_question_id"
    t.integer "weight"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "acknowledgement"
  end

  create_table "question_transitions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "question_id"
    t.integer "linked_question_id"
    t.string "linking_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "question_text"
    t.string "question_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "hints"
    t.string "key"
    t.string "nps_low"
    t.string "nps_high"
    t.text "positive_quest"
    t.text "negative_quest"
    t.bigint "driver_id"
    t.string "status", default: "live"
    t.bigint "account_id"
    t.boolean "root_question"
    t.string "followup_type"
    t.string "question_sentiment"
    t.boolean "can_be_skipped", default: false
    t.text "perspective_question_text"
    t.string "category"
    t.boolean "need_help", default: false
    t.string "tab_name"
    t.string "serviceable_type"
    t.bigint "serviceable_id"
    t.datetime "discarded_at"
    t.string "alias_text"
    t.string "input_text_type"
    t.string "nps_view_type", default: "multiple_choice"
    t.boolean "is_text_required"
    t.boolean "is_nps_required"
    t.float "max_rating"
    t.float "min_rating"
    t.boolean "score_visible"
    t.index ["account_id"], name: "index_questions_on_account_id"
    t.index ["category", "key", "discarded_at"], name: "idx_questions_category_key_discarded_at"
    t.index ["discarded_at"], name: "index_questions_on_discarded_at"
    t.index ["driver_id"], name: "index_questions_on_driver_id"
    t.index ["serviceable_type", "serviceable_id"], name: "index_questions_on_serviceable_type_and_serviceable_id"
  end

  create_table "questions_surveys", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "question_id", null: false
    t.bigint "survey_id", null: false
    t.integer "position"
    t.index ["question_id", "survey_id"], name: "index_questions_surveys_on_question_id_and_survey_id"
    t.index ["survey_id", "question_id"], name: "index_questions_surveys_on_survey_id_and_question_id"
  end

  create_table "rating_ranges", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.float "range_start", null: false
    t.float "range_end", null: false
    t.string "legend", null: false
    t.bigint "question_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_rating_ranges_on_discarded_at"
    t.index ["question_id"], name: "index_rating_ranges_on_question_id"
  end

  create_table "reports", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.bigint "created_by_id"
    t.integer "account_id"
    t.integer "report_type", default: 0
    t.integer "group"
    t.integer "sub_group"
    t.json "filter_options"
    t.json "query_json"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "view_type", default: 0
    t.boolean "upfront", default: true
    t.string "group_title"
    t.text "selected_rows"
    t.text "selected_columns"
    t.index ["created_by_id"], name: "index_reports_on_created_by_id"
    t.index ["discarded_at"], name: "index_reports_on_discarded_at"
  end

  create_table "reports_products", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "report_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["product_id"], name: "index_reports_products_on_product_id"
    t.index ["report_id"], name: "index_reports_products_on_report_id"
  end

  create_table "response_actions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "response_id"
    t.boolean "sent_thanks"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["response_id"], name: "index_response_actions_on_response_id"
  end

  create_table "response_drivers", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "response_id"
    t.string "driver_key"
    t.integer "sentiment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "driver_id"
    t.index ["driver_id"], name: "index_response_drivers_on_driver_id"
    t.index ["response_id"], name: "index_response_drivers_on_response_id"
  end

  create_table "responses", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "response_text"
    t.bigint "question_id"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "option_id"
    t.integer "nps_score"
    t.bigint "employee_chat_id"
    t.integer "response_text_word_count"
    t.boolean "is_responded", default: false
    t.integer "responded_by_whom_id"
    t.boolean "is_closed", default: false
    t.datetime "closed_at"
    t.datetime "reminder_mail_sent_at"
    t.integer "reminder_mail_sent_count", default: 0
    t.bigint "schedule_id"
    t.decimal "response_text_sentiment", precision: 4, scale: 3
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_responses_on_discarded_at"
    t.index ["employee_chat_id"], name: "index_responses_on_employee_chat_id"
    t.index ["employee_id"], name: "index_responses_on_employee_id"
    t.index ["question_id"], name: "index_responses_on_question_id"
    t.index ["response_text_word_count"], name: "index_responses_on_response_text_word_count"
    t.index ["schedule_id"], name: "index_responses_on_schedule_id"
  end

  create_table "review_copy_messages", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id"
    t.string "section"
    t.json "content"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "subject"
    t.text "email_content"
    t.bigint "review_cycle_phase_id"
    t.integer "notification_type", default: 0
    t.integer "notification_channel", default: 0
    t.bigint "account_id"
    t.datetime "discarded_at"
    t.index ["account_id"], name: "index_review_copy_messages_on_account_id"
    t.index ["discarded_at"], name: "index_review_copy_messages_on_discarded_at"
    t.index ["review_cycle_phase_id"], name: "index_review_copy_messages_on_review_cycle_phase_id"
  end

  create_table "review_cycle_automations", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "trigger"
    t.string "action"
    t.bigint "trigger_reviewer_type_id"
    t.bigint "action_reviewer_type_id"
    t.bigint "trigger_phase_type_id"
    t.bigint "action_phase_type_id"
    t.bigint "review_cycle_id", null: false
    t.boolean "enabled", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["action_phase_type_id"], name: "index_review_cycle_automations_on_action_phase_type_id"
    t.index ["action_reviewer_type_id"], name: "index_review_cycle_automations_on_action_reviewer_type_id"
    t.index ["review_cycle_id"], name: "index_review_cycle_automations_on_review_cycle_id"
    t.index ["trigger_phase_type_id"], name: "index_review_cycle_automations_on_trigger_phase_type_id"
    t.index ["trigger_reviewer_type_id"], name: "index_review_cycle_automations_on_trigger_reviewer_type_id"
  end

  create_table "review_cycle_calibration_comments", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "text"
    t.string "comment_type", null: false
    t.bigint "commenter_id"
    t.bigint "reviewee_id"
    t.datetime "discarded_at"
    t.datetime "created_at"
    t.index ["commenter_id"], name: "index_review_cycle_calibration_comments_on_commenter_id"
    t.index ["discarded_at"], name: "index_review_cycle_calibration_comments_on_discarded_at"
    t.index ["reviewee_id"], name: "index_review_cycle_calibration_comments_on_reviewee_id"
  end

  create_table "review_cycle_calibration_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id"
    t.bigint "question_id"
    t.string "visibility", default: "multiple"
    t.boolean "editable", default: true
    t.datetime "discarded_at"
    t.string "column_name"
    t.integer "position", default: 100
    t.bigint "past_review_cycle_id"
    t.string "column_type"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "question_access", default: "public_access"
    t.bigint "reviewer_type_id"
    t.index ["past_review_cycle_id"], name: "index_review_cycle_calibration_questions_on_past_review_cycle_id"
    t.index ["question_id"], name: "index_review_cycle_calibration_questions_on_question_id"
    t.index ["review_cycle_id"], name: "index_review_cycle_calibration_questions_on_review_cycle_id"
    t.index ["reviewer_type_id"], name: "index_review_cycle_calibration_questions_on_reviewer_type_id"
  end

  create_table "review_cycle_goal_cycles", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id"
    t.bigint "goal_cycle_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_review_cycle_goal_cycles_on_discarded_at"
    t.index ["goal_cycle_id"], name: "index_review_cycle_goal_cycles_on_goal_cycle_id"
    t.index ["review_cycle_id"], name: "index_review_cycle_goal_cycles_on_review_cycle_id"
  end

  create_table "review_cycle_notification_recipients", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id", null: false
    t.bigint "review_cycle_phase_id", null: false
    t.bigint "review_cycle_notification_id", null: false
    t.bigint "employee_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.datetime "delivered_at"
    t.string "status"
    t.string "reason"
    t.integer "reviewer_type_id"
    t.string "backfill_type"
    t.index ["backfill_type"], name: "index_review_cycle_notification_recipients_on_backfill_type"
    t.index ["discarded_at"], name: "index_review_cycle_notification_recipients_on_discarded_at"
    t.index ["employee_id"], name: "index_review_cycle_notification_recipients_on_employee_id"
    t.index ["review_cycle_id"], name: "index_review_cycle_notification_recipients_on_review_cycle_id"
    t.index ["review_cycle_notification_id"], name: "index_notifications_on_review_cycle_notification_id"
    t.index ["review_cycle_phase_id", "employee_id"], name: "idx_rcnr_phase_employee"
    t.index ["review_cycle_phase_id"], name: "index_recipients_on_review_cycle_phase_id"
  end

  create_table "review_cycle_notifications", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id", null: false
    t.bigint "review_cycle_phase_id", null: false
    t.string "task_type"
    t.datetime "run_at"
    t.string "channel"
    t.datetime "task_executed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.bigint "review_copy_message_id"
    t.string "recipients_type"
    t.bigint "creator_id"
    t.index ["creator_id"], name: "index_review_cycle_notifications_on_creator_id"
    t.index ["discarded_at"], name: "index_review_cycle_notifications_on_discarded_at"
    t.index ["review_copy_message_id"], name: "fk_rails_57afb1a4ff"
    t.index ["review_cycle_id"], name: "index_review_cycle_notifications_on_review_cycle_id"
    t.index ["review_cycle_phase_id"], name: "index_review_cycle_notifications_on_review_cycle_phase_id"
  end

  create_table "review_cycle_phases", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "phase_type"
    t.bigint "review_cycle_id", null: false
    t.datetime "start_time"
    t.datetime "end_time"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.float "position"
    t.bigint "reviewer_type_id"
    t.json "config_json"
    t.index ["discarded_at"], name: "index_review_cycle_phases_on_discarded_at"
    t.index ["review_cycle_id"], name: "index_review_cycle_phases_on_review_cycle_id"
    t.index ["reviewer_type_id"], name: "index_review_cycle_phases_on_reviewer_type_id"
  end

  create_table "review_cycle_question_visibilities", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_matrix_config_id"
    t.bigint "review_cycle_template_id"
    t.bigint "review_cycle_template_question_id"
    t.bigint "template_question_id"
    t.boolean "is_public_question"
    t.string "response_visibility", limit: 20
    t.boolean "show_text"
    t.boolean "show_score"
    t.boolean "show_legend"
    t.boolean "show_average_score"
    t.boolean "show_average_legend"
    t.boolean "show_overall_score"
    t.boolean "show_overall_legend"
    t.integer "score_decimal_limit"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_review_cycle_question_visibilities_on_discarded_at"
    t.index ["review_cycle_matrix_config_id", "response_visibility", "is_public_question"], name: "index_rcqv_on_rcmcid_response_visibility_is_public"
    t.index ["review_cycle_matrix_config_id", "review_cycle_template_id", "review_cycle_template_question_id", "template_question_id"], name: "cindex_review_cycle_question_visibilities_on_all_refs", unique: true
    t.index ["review_cycle_matrix_config_id"], name: "index_rc_question_visibility_on_matrix_config_id"
    t.index ["review_cycle_template_id"], name: "index_rc_question_visibility_on_rc_template_id"
    t.index ["review_cycle_template_question_id", "review_cycle_matrix_config_id", "is_public_question"], name: "index_rcqv_on_rc_template_question_id_rcmcid_is_public"
    t.index ["review_cycle_template_question_id"], name: "index_rc_question_visibility_on_rc_template_question_id"
    t.index ["template_question_id", "review_cycle_matrix_config_id", "is_public_question"], name: "index_rcqv_on_template_question_id_rcmcid_is_public"
    t.index ["template_question_id"], name: "index_rc_question_visibility_on_template_question_id"
  end

  create_table "review_cycle_reusable_template_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_reusable_template_id"
    t.integer "position"
    t.string "block_type"
    t.bigint "question_id"
    t.text "content"
    t.string "goal_fields"
    t.string "formula"
    t.string "alignment"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "default_weights"
    t.index ["question_id"], name: "index_review_cycle_reusable_template_questions_on_question_id"
    t.index ["review_cycle_reusable_template_id"], name: "reusable_template_question"
  end

  create_table "review_cycle_reusable_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.string "category"
    t.bigint "account_id_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id_id"], name: "index_review_cycle_reusable_templates_on_account_id_id"
  end

  create_table "review_cycle_template_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_template_id"
    t.bigint "question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.string "block_type"
    t.text "content"
    t.json "goal_fields"
    t.string "question_access", default: "public_access"
    t.datetime "discarded_at"
    t.text "formula"
    t.string "alignment"
    t.boolean "show_goal_progress", default: true
    t.json "default_weights"
    t.json "legend_formula"
    t.index ["discarded_at"], name: "index_review_cycle_template_questions_on_discarded_at"
    t.index ["question_id"], name: "index_review_cycle_template_questions_on_question_id"
    t.index ["review_cycle_template_id"], name: "index_cycle_template_questions_on_cycle_template_id"
  end

  create_table "review_cycle_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "reviewer_type_id"
    t.string "employee_attribute"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.bigint "template_id"
    t.index ["discarded_at"], name: "index_review_cycle_templates_on_discarded_at"
    t.index ["employee_id"], name: "index_review_cycle_templates_on_employee_id"
    t.index ["reviewer_type_id", "employee_attribute", "discarded_at"], name: "index_reviewer_type_id_employee_attribute_discarded_at_unique", unique: true
    t.index ["reviewer_type_id"], name: "index_review_cycle_templates_on_reviewer_type_id"
    t.index ["template_id"], name: "index_review_cycle_templates_on_template_id"
  end

  create_table "review_cycle_user_roles", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "role_type"
    t.bigint "review_cycle_id"
    t.bigint "reviewer_type_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_review_cycle_user_roles_on_discarded_at"
    t.index ["review_cycle_id", "discarded_at", "reviewer_type_id"], name: "index_rcur_on_review_cycle_id_discarded_at_reviewer_type_id"
    t.index ["review_cycle_id"], name: "index_review_cycle_user_roles_on_review_cycle_id"
    t.index ["reviewer_type_id", "discarded_at"], name: "index_rcur_on_reviewer_type_id_discarded_at"
    t.index ["reviewer_type_id"], name: "index_review_cycle_user_roles_on_reviewer_type_id"
    t.index ["role_type", "review_cycle_id", "reviewer_type_id"], name: "cindex_rc_user_role_on_user_role_rc_id_rt_id", unique: true
  end

  create_table "review_cycle_visibility_matrices", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id"
    t.bigint "review_cycle_user_role_id"
    t.bigint "reviewer_type_id"
    t.string "platform_type"
    t.string "overall_visibility"
    t.string "block_access"
    t.json "visibility_options"
    t.integer "precedence"
    t.bigint "edited_by_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_review_cycle_visibility_matrices_on_discarded_at"
    t.index ["edited_by_id"], name: "index_review_cycle_visibility_matrices_on_edited_by_id"
    t.index ["platform_type", "review_cycle_user_role_id"], name: "index_rcvm_on_platform_type_review_cycle_user_role_id"
    t.index ["review_cycle_id", "review_cycle_user_role_id"], name: "index_rcvm_on_review_cycle_id_review_cycle_user_role_id"
    t.index ["review_cycle_id"], name: "index_review_cycle_visibility_matrices_on_review_cycle_id"
    t.index ["review_cycle_user_role_id", "reviewer_type_id", "platform_type"], name: "cindex_rc_visibility_matrix_on_rc_role_rt_id_and_platform_type", unique: true
    t.index ["review_cycle_user_role_id"], name: "index_rc_visibility_on_rc_user_role"
    t.index ["reviewer_type_id", "platform_type", "overall_visibility"], name: "index_rcvm_on_reviewer_type_id_platform_type_visibility"
    t.index ["reviewer_type_id"], name: "index_review_cycle_visibility_matrices_on_reviewer_type_id"
  end

  create_table "review_cycles", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "title"
    t.bigint "account_id"
    t.datetime "start_date"
    t.string "template_employee_attribute"
    t.bigint "creator_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "last_synced_at"
    t.boolean "anonymize_reports", default: true
    t.boolean "view_unsubmitted_reviews", default: false
    t.string "review_cycle_phase", default: "choose_peers"
    t.json "gsheet_info"
    t.boolean "show_key_results", default: false
    t.boolean "auto_progress_manager", default: false
    t.string "auto_progress_employee"
    t.string "status", default: "draft"
    t.datetime "discarded_at"
    t.string "auto_create_one_on_one", default: "disabled"
    t.datetime "archived_at"
    t.integer "duplicated_from"
    t.json "charts_metadata"
    t.json "competency_config_json"
    t.string "review_type", default: "performance_review"
    t.json "goal_config_json"
    t.integer "key_result_depth", default: 1
    t.datetime "end_date"
    t.string "default_channel"
    t.boolean "automation_enabled", default: false, null: false
    t.boolean "has_same_visibility_config", default: true, null: false
    t.datetime "enforce_system_manager_mapping"
    t.index ["account_id"], name: "index_review_cycles_on_account_id"
    t.index ["archived_at"], name: "index_review_cycles_on_archived_at"
    t.index ["creator_id"], name: "index_review_cycles_on_creator_id"
    t.index ["discarded_at"], name: "index_review_cycles_on_discarded_at"
  end

  create_table "review_responses", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id"
    t.bigint "review_cycle_template_question_id"
    t.bigint "question_id"
    t.float "score"
    t.text "response_text"
    t.bigint "option_id"
    t.bigint "reviewer_id"
    t.bigint "reviewee_id"
    t.bigint "reviewer_employee_id"
    t.bigint "reviewee_employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "goal_id"
    t.datetime "discarded_at"
    t.float "calibrated_score"
    t.bigint "calibrator_id"
    t.bigint "calibrated_score_option_id"
    t.bigint "reviewee_competency_id"
    t.text "calibrated_response_text"
    t.bigint "template_question_id"
    t.index ["calibrated_score_option_id"], name: "index_review_responses_on_calibrated_score_option_id"
    t.index ["calibrator_id"], name: "index_review_responses_on_calibrator_id"
    t.index ["discarded_at"], name: "index_review_responses_on_discarded_at"
    t.index ["goal_id"], name: "index_review_responses_on_goal_id"
    t.index ["option_id"], name: "index_review_responses_on_option_id"
    t.index ["question_id"], name: "index_review_responses_on_question_id"
    t.index ["review_cycle_id"], name: "index_review_responses_on_review_cycle_id"
    t.index ["review_cycle_template_question_id"], name: "index_review_responses_on_review_cycle_template_question_id"
    t.index ["reviewee_competency_id"], name: "index_review_responses_on_reviewee_competency_id"
    t.index ["reviewee_employee_id"], name: "index_review_responses_on_reviewee_employee_id"
    t.index ["reviewee_id"], name: "index_review_responses_on_reviewee_id"
    t.index ["reviewer_employee_id"], name: "index_review_responses_on_reviewer_employee_id"
    t.index ["reviewer_id", "goal_id", "question_id"], name: "index_by_reviewer_id_and_goal_id_and_question_id", unique: true
    t.index ["reviewer_id", "goal_id"], name: "index_review_responses_on_reviewer_id_and_goal_id", unique: true
    t.index ["reviewer_id", "reviewee_competency_id", "question_id", "review_cycle_template_question_id"], name: "index_responses_competency_and_template", unique: true
    t.index ["reviewer_id"], name: "index_review_responses_on_reviewer_id"
    t.index ["template_question_id"], name: "index_review_responses_on_template_question_id"
  end

  create_table "review_summaries", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.json "summaries"
    t.datetime "discarded_at"
    t.bigint "reviewee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at", "reviewee_id"], name: "index_review_summaries_on_discarded_at_and_reviewee_id", unique: true
    t.index ["reviewee_id"], name: "index_review_summaries_on_reviewee_id"
  end

  create_table "reviewee_competencies", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "reviewee_id"
    t.bigint "review_cycle_id"
    t.bigint "competency_id"
    t.bigint "approver_id"
    t.bigint "rejected_by_id"
    t.text "description"
    t.float "weightage", default: 0.0
    t.boolean "exclude", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.bigint "competency_theme_id"
    t.string "competency_theme_name"
    t.string "competency_name"
    t.index ["approver_id"], name: "index_reviewee_competencies_on_approver_id"
    t.index ["competency_id"], name: "index_reviewee_competencies_on_competency_id"
    t.index ["competency_theme_id"], name: "index_reviewee_competencies_on_competency_theme_id"
    t.index ["discarded_at"], name: "index_reviewee_competencies_on_discarded_at"
    t.index ["rejected_by_id"], name: "index_reviewee_competencies_on_rejected_by_id"
    t.index ["review_cycle_id"], name: "index_reviewee_competencies_on_review_cycle_id"
    t.index ["reviewee_id"], name: "index_reviewee_competencies_on_reviewee_id"
  end

  create_table "reviewee_goal_event_logs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "reviewee_id"
    t.bigint "event_creator_id"
    t.string "event_type", null: false
    t.text "comment"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_reviewee_goal_event_logs_on_discarded_at"
    t.index ["event_creator_id"], name: "index_reviewee_goal_event_logs_on_event_creator_id"
    t.index ["reviewee_id"], name: "index_reviewee_goal_event_logs_on_reviewee_id"
  end

  create_table "reviewee_goals", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "reviewee_id"
    t.bigint "review_cycle_id"
    t.bigint "goal_id"
    t.float "weightage"
    t.boolean "exclude", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.bigint "approver_id"
    t.bigint "rejected_by_id"
    t.index ["approver_id"], name: "index_reviewee_goals_on_approver_id"
    t.index ["discarded_at"], name: "index_reviewee_goals_on_discarded_at"
    t.index ["goal_id"], name: "index_reviewee_goals_on_goal_id"
    t.index ["rejected_by_id"], name: "index_reviewee_goals_on_rejected_by_id"
    t.index ["review_cycle_id"], name: "index_reviewee_goals_on_review_cycle_id"
    t.index ["reviewee_id", "goal_id", "review_cycle_id"], name: "index_reviewee_goals_reviewee_id_goal_id_review_cycle_id_unique", unique: true
    t.index ["reviewee_id"], name: "index_reviewee_goals_on_reviewee_id"
  end

  create_table "reviewee_snapshots", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "reviewee_id", null: false
    t.bigint "employee_id"
    t.bigint "reviewer_id"
    t.string "custom_attribute"
    t.string "reason"
    t.boolean "peer_selection_done", default: false
    t.boolean "peer_approval_done", default: false
    t.boolean "goal_selection_done", default: false
    t.boolean "goal_approval_done", default: false
    t.datetime "created_at", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.index ["employee_id"], name: "index_reviewee_snapshots_on_employee_id"
    t.index ["reason"], name: "index_reviewee_snapshots_on_reason"
    t.index ["reviewee_id"], name: "index_reviewee_snapshots_on_reviewee_id"
    t.index ["reviewer_id"], name: "index_reviewee_snapshots_on_reviewer_id"
  end

  create_table "reviewees", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id"
    t.bigint "employee_id"
    t.boolean "self_review_done", default: false
    t.boolean "peer_selection_done", default: false
    t.boolean "peer_approval_done", default: false
    t.float "review_writing_percent", default: 0.0
    t.boolean "manager_summary_done", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "confidential", default: false
    t.boolean "release_review_done", default: false
    t.bigint "manager_id"
    t.boolean "goal_approval_done", default: false
    t.datetime "discarded_at"
    t.string "custom_attribute"
    t.boolean "goal_selection_done", default: false
    t.json "custom_variables"
    t.bigint "one_on_one_id"
    t.boolean "competency_selection_done", default: false
    t.boolean "competency_approval_done", default: false
    t.boolean "auto_approve_goals_after_edit", default: false
    t.boolean "goal_changes_requested", default: false, null: false
    t.index ["discarded_at"], name: "index_reviewees_on_discarded_at"
    t.index ["employee_id"], name: "index_reviewees_on_employee_id"
    t.index ["manager_id"], name: "index_reviewees_on_manager_id"
    t.index ["one_on_one_id"], name: "index_reviewees_on_one_on_one_id"
    t.index ["review_cycle_id"], name: "index_reviewees_on_review_cycle_id"
  end

  create_table "reviewer_types", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "reviewer_type"
    t.bigint "review_cycle_id"
    t.string "first_person"
    t.string "second_person"
    t.boolean "nomination_required", default: false
    t.boolean "approval_required", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "autofill"
    t.integer "include_in_release_review", default: 1
    t.boolean "define_goals", default: false
    t.boolean "define_goal_weights", default: false
    t.boolean "goal_approval_required", default: false
    t.datetime "discarded_at"
    t.integer "min_reviewers", default: 1
    t.integer "max_reviewers"
    t.boolean "include_goals", default: false
    t.boolean "include_competency", default: false
    t.boolean "define_competency", default: false
    t.boolean "define_competency_weights", default: false
    t.boolean "competency_approval_required", default: false
    t.boolean "can_read_reviews", default: false
    t.boolean "limit_to_participants", default: false
    t.boolean "enable_calibration_view", default: false
    t.boolean "enable_nine_box", default: false
    t.boolean "can_review_anyone", default: false
    t.boolean "standalone_launch", default: false
    t.integer "creation_source", default: 0
    t.index ["discarded_at"], name: "index_reviewer_types_on_discarded_at"
    t.index ["review_cycle_id"], name: "index_reviewer_types_on_review_cycle_id"
  end

  create_table "reviewers", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "review_cycle_id"
    t.bigint "reviewer_type_id"
    t.bigint "reviewee_id"
    t.bigint "employee_id"
    t.bigint "nominator_id"
    t.bigint "approver_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "rejected_by_id"
    t.boolean "review_submitted", default: false
    t.datetime "discarded_at"
    t.bigint "nominator_role_id"
    t.bigint "approver_role_id"
    t.index ["approver_id"], name: "index_reviewers_on_approver_id"
    t.index ["approver_role_id"], name: "index_reviewers_on_approver_role_id"
    t.index ["discarded_at"], name: "index_reviewers_on_discarded_at"
    t.index ["employee_id"], name: "index_reviewers_on_employee_id"
    t.index ["nominator_id"], name: "index_reviewers_on_nominator_id"
    t.index ["nominator_role_id"], name: "index_reviewers_on_nominator_role_id"
    t.index ["rejected_by_id"], name: "index_reviewers_on_rejected_by_id"
    t.index ["review_cycle_id"], name: "index_reviewers_on_review_cycle_id"
    t.index ["reviewee_id"], name: "index_reviewers_on_reviewee_id"
    t.index ["reviewer_type_id"], name: "index_reviewers_on_reviewer_type_id"
  end

  create_table "schedule_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "schedule_id"
    t.bigint "question_id"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_library_question", default: false
    t.boolean "is_manager_question", default: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_schedule_questions_on_discarded_at"
    t.index ["question_id", "schedule_id"], name: "index_schedule_questions_on_question_id_and_schedule_id"
    t.index ["question_id"], name: "index_schedule_questions_on_question_id"
    t.index ["schedule_id", "question_id"], name: "index_schedule_questions_on_schedule_id_and_question_id"
    t.index ["schedule_id"], name: "index_schedule_questions_on_schedule_id"
  end

  create_table "schedule_respondents", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "schedule_id"
    t.bigint "respondent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_schedule_respondents_on_discarded_at"
    t.index ["respondent_id"], name: "index_schedule_respondents_on_respondent_id"
    t.index ["schedule_id"], name: "index_schedule_respondents_on_schedule_id"
  end

  create_table "schedule_shared_admins", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "schedule_id"
    t.bigint "employee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_schedule_shared_admins_on_discarded_at"
    t.index ["employee_id"], name: "index_schedule_shared_admins_on_employee_id"
    t.index ["schedule_id"], name: "index_schedule_shared_admins_on_schedule_id"
  end

  create_table "schedule_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "schedule_id"
    t.bigint "template_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_schedule_templates_on_discarded_at"
    t.index ["schedule_id"], name: "index_schedule_templates_on_schedule_id"
    t.index ["template_id"], name: "index_schedule_templates_on_template_id"
  end

  create_table "schedules", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "when_to_run"
    t.string "task_type"
    t.integer "cycle_year"
    t.integer "cycle_quarter"
    t.boolean "task_executed"
    t.bigint "account_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.bigint "parent_schedule_id"
    t.datetime "scheduled_at"
    t.string "title"
    t.datetime "discarded_at"
    t.datetime "start_date"
    t.datetime "end_date"
    t.string "status"
    t.string "frequency"
    t.bigint "schedule_creator_id"
    t.integer "default_pulse_size"
    t.integer "next_pulse_size"
    t.string "enps_frequency"
    t.boolean "enps_active"
    t.boolean "send_email"
    t.boolean "send_slack_notification"
    t.boolean "auto_add_participants_when_live"
    t.integer "visibility"
    t.text "subject"
    t.text "email_content"
    t.string "manager_email_subject"
    t.text "manager_email_content"
    t.boolean "identified_survey"
    t.string "life_cycle_attribute"
    t.integer "life_cycle_days"
    t.boolean "send_to_personal_email", default: true
    t.json "life_cycle_subset"
    t.boolean "low_rating_comment_required", default: true
    t.index ["account_id"], name: "index_schedules_on_account_id"
    t.index ["discarded_at"], name: "index_schedules_on_discarded_at"
    t.index ["parent_schedule_id"], name: "index_schedules_on_parent_schedule_id"
    t.index ["schedule_creator_id"], name: "index_schedules_on_schedule_creator_id"
  end

  create_table "schedules_simple_chat_chatters", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "schedule_id"
    t.bigint "simple_chat_chatter_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["schedule_id"], name: "index_schedules_simple_chat_chatters_on_schedule_id"
    t.index ["simple_chat_chatter_id"], name: "index_schedules_simple_chat_chatters_on_simple_chat_chatter_id"
  end

  create_table "shortened_urls", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "owner_id"
    t.string "owner_type", limit: 20
    t.text "url", null: false
    t.string "unique_key", limit: 10, null: false
    t.string "category"
    t.integer "use_count", default: 0, null: false
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_shortened_urls_on_category"
    t.index ["owner_id", "owner_type"], name: "index_shortened_urls_on_owner_id_and_owner_type"
    t.index ["unique_key"], name: "index_shortened_urls_on_unique_key", unique: true
    t.index ["url"], name: "index_shortened_urls_on_url", length: 300
  end

  create_table "simple_chat_card_options", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.text "content"
    t.text "representational_content"
    t.integer "next_card_id"
    t.bigint "simple_chat_card_id"
    t.string "option_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["next_card_id"], name: "index_simple_chat_card_options_on_next_card_id"
    t.index ["simple_chat_card_id"], name: "index_simple_chat_card_options_on_simple_chat_card_id"
  end

  create_table "simple_chat_cards", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "simple_chat_story_id"
    t.string "card_type"
    t.text "content"
    t.integer "prev_card_id"
    t.integer "next_card_id"
    t.integer "loop_card_id"
    t.boolean "user_response_required"
    t.string "input_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "image_url"
    t.index ["loop_card_id"], name: "index_simple_chat_cards_on_loop_card_id"
    t.index ["next_card_id"], name: "index_simple_chat_cards_on_next_card_id"
    t.index ["prev_card_id"], name: "index_simple_chat_cards_on_prev_card_id"
    t.index ["simple_chat_story_id"], name: "index_simple_chat_cards_on_simple_chat_story_id"
  end

  create_table "simple_chat_chatters", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "simple_chat_story_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["simple_chat_story_id"], name: "index_simple_chat_chatters_on_simple_chat_story_id"
    t.index ["user_type", "user_id"], name: "index_simple_chat_chatters_on_user_type_and_user_id"
  end

  create_table "simple_chat_messages", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "simple_chat_chatter_id"
    t.bigint "simple_chat_card_id"
    t.bigint "simple_chat_card_option_id"
    t.boolean "is_bot"
    t.text "content"
    t.text "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["simple_chat_card_id"], name: "index_simple_chat_messages_on_simple_chat_card_id"
    t.index ["simple_chat_card_option_id"], name: "index_simple_chat_messages_on_simple_chat_card_option_id"
    t.index ["simple_chat_chatter_id"], name: "index_simple_chat_messages_on_simple_chat_chatter_id"
  end

  create_table "simple_chat_stories", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.string "storyable_type"
    t.bigint "storyable_id"
    t.bigint "simple_chat_card_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["simple_chat_card_id"], name: "index_simple_chat_stories_on_simple_chat_card_id"
    t.index ["storyable_type", "storyable_id"], name: "index_simple_chat_stories_on_storyable_type_and_storyable_id"
  end

  create_table "simple_chat_variables", options: "ENGINE=InnoDB DEFAULT CHARSET=latin1", force: :cascade do |t|
    t.bigint "simple_chat_chatter_id"
    t.string "key"
    t.json "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["simple_chat_chatter_id"], name: "index_simple_chat_variables_on_simple_chat_chatter_id"
  end

  create_table "skip_reviewers", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "reviewee_id"
    t.bigint "employee_id"
    t.bigint "review_cycle_id"
    t.string "goals_status", default: "to_be_approved", null: false
    t.datetime "discarded_at"
    t.index ["employee_id"], name: "index_skip_reviewers_on_employee_id"
    t.index ["review_cycle_id"], name: "index_skip_reviewers_on_review_cycle_id"
    t.index ["reviewee_id"], name: "index_skip_reviewers_on_reviewee_id"
  end

  create_table "slack_teams", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "team_id"
    t.string "team_name"
    t.string "user_access_token"
    t.string "bot_access_token"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "employee_id"
    t.bigint "account_id"
    t.index ["account_id"], name: "index_slack_teams_on_account_id"
    t.index ["employee_id"], name: "index_slack_teams_on_employee_id"
  end

  create_table "sso_settings", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.boolean "enabled", default: false
    t.text "idp_url"
    t.text "idp_entity_id"
    t.text "idp_certificate"
    t.text "idp_metadata"
    t.text "idp_metadata_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_sso_settings_on_account_id"
  end

  create_table "subscriptions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "account_id"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "plan_id"
    t.integer "max_seats"
    t.datetime "starts_at"
    t.string "status", default: "active"
    t.string "chargebee_subscription_id"
    t.string "billing_cycle"
    t.decimal "next_billing_amount", precision: 10
    t.string "customer_card_status"
    t.boolean "checkout_completed", default: true, null: false
    t.boolean "skip_chargebee_check", default: false
    t.index ["account_id"], name: "index_subscriptions_on_account_id"
    t.index ["plan_id"], name: "index_subscriptions_on_plan_id"
  end

  create_table "suggested_talking_point_categories", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "account_id"
    t.string "category_name", null: false
    t.integer "position"
    t.boolean "is_system", default: true
    t.boolean "hidden", default: false
    t.datetime "discarded_at"
    t.bigint "parent_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_suggested_talking_point_categories_on_discarded_at"
    t.index ["parent_category_id"], name: "index_suggested_talking_point_categories_on_parent_category_id"
  end

  create_table "super_list_items", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.integer "position"
    t.boolean "completed", default: false
    t.bigint "reportee_id"
    t.bigint "manager_id"
    t.bigint "pulse_response_id"
    t.integer "source"
    t.integer "action_type"
    t.boolean "shared", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status"
    t.integer "item_type"
    t.bigint "one_on_one_note_id"
    t.string "check_list_item_id"
    t.datetime "send_reminder_on"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_super_list_items_on_discarded_at"
    t.index ["manager_id"], name: "index_super_list_items_on_manager_id"
    t.index ["one_on_one_note_id"], name: "index_super_list_items_on_one_on_one_note_id"
    t.index ["pulse_response_id"], name: "index_super_list_items_on_pulse_response_id"
    t.index ["reportee_id"], name: "index_super_list_items_on_reportee_id"
  end

  create_table "super_list_priorities", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.decimal "task_score", precision: 7, scale: 2
    t.decimal "static_score", precision: 7, scale: 2
    t.decimal "time_score", precision: 7, scale: 2
    t.decimal "sentiment_score", precision: 7, scale: 2
    t.decimal "priority_score", precision: 7, scale: 2
    t.decimal "source_score", precision: 7, scale: 2
    t.bigint "super_list_item_id"
    t.integer "manager_priority"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["super_list_item_id"], name: "index_super_list_priorities_on_super_list_item_id"
    t.index ["task_score"], name: "index_super_list_priorities_on_task_score"
  end

  create_table "survey_notification_messages", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "header"
    t.text "body_content"
    t.bigint "survey_notification_id", null: false
    t.integer "notification_type", null: false
    t.integer "notification_channel", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_survey_notification_messages_on_discarded_at"
    t.index ["survey_notification_id"], name: "index_survey_notification_messages_on_survey_notification_id"
  end

  create_table "survey_notification_recipients", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "discarded_at"
    t.bigint "employee_id", null: false
    t.bigint "survey_notification_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_survey_notification_recipients_on_discarded_at"
    t.index ["employee_id"], name: "index_survey_notification_recipients_on_employee_id"
    t.index ["survey_notification_id"], name: "index_survey_notification_recipients_on_survey_notification_id"
  end

  create_table "survey_notifications", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "task_type"
    t.integer "channel"
    t.datetime "run_at"
    t.datetime "task_executed_at"
    t.datetime "discarded_at"
    t.bigint "survey_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_survey_notifications_on_discarded_at"
    t.index ["survey_id"], name: "index_survey_notifications_on_survey_id"
  end

  create_table "surveys", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "key"
  end

  create_table "switch_user_logs", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "super_admin_id"
    t.bigint "switched_to_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "log_type", default: 0
    t.integer "switched_from_id"
    t.index ["super_admin_id"], name: "index_switch_user_logs_on_super_admin_id"
    t.index ["switched_to_id"], name: "index_switch_user_logs_on_switched_to_id"
  end

  create_table "taggings", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "tag_id"
    t.string "taggable_type"
    t.integer "taggable_id"
    t.string "tagger_type"
    t.integer "tagger_id"
    t.string "context", limit: 128
    t.datetime "created_at"
    t.index ["context"], name: "index_taggings_on_context"
    t.index ["tag_id", "taggable_id", "taggable_type", "context", "tagger_id", "tagger_type"], name: "taggings_idx", unique: true
    t.index ["tag_id"], name: "index_taggings_on_tag_id"
    t.index ["taggable_id", "taggable_type", "context"], name: "index_taggings_on_taggable_id_and_taggable_type_and_context"
    t.index ["taggable_id", "taggable_type", "tagger_id", "context"], name: "taggings_idy"
    t.index ["taggable_id"], name: "index_taggings_on_taggable_id"
    t.index ["taggable_type"], name: "index_taggings_on_taggable_type"
    t.index ["tagger_id", "tagger_type"], name: "index_taggings_on_tagger_id_and_tagger_type"
    t.index ["tagger_id"], name: "index_taggings_on_tagger_id"
  end

  create_table "tags", id: :integer, options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", collation: "utf8mb3_bin"
    t.integer "taggings_count", default: 0
    t.index ["name"], name: "index_tags_on_name", unique: true
  end

  create_table "tasks", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.bigint "project_id"
    t.datetime "start_date"
    t.datetime "due_date"
    t.bigint "added_by_id"
    t.bigint "owner_id"
    t.boolean "completed", default: false
    t.bigint "goal_cycle_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["added_by_id"], name: "index_tasks_on_added_by_id"
    t.index ["discarded_at"], name: "index_tasks_on_discarded_at"
    t.index ["goal_cycle_id"], name: "index_tasks_on_goal_cycle_id"
    t.index ["owner_id"], name: "index_tasks_on_owner_id"
    t.index ["project_id"], name: "index_tasks_on_project_id"
  end

  create_table "template_categories", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.integer "category_type"
    t.integer "position"
    t.integer "template_category_type", default: 1
    t.bigint "account_id"
    t.bigint "creator_id"
    t.index ["account_id"], name: "index_template_categories_on_account_id"
    t.index ["creator_id"], name: "index_template_categories_on_creator_id"
    t.index ["template_category_type"], name: "index_template_categories_on_template_category_type"
  end

  create_table "template_category_templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "template_id"
    t.bigint "template_category_id"
  end

  create_table "template_questions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "template_id"
    t.integer "position"
    t.bigint "question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "block_type"
    t.text "content"
    t.json "goal_fields"
    t.string "question_access"
    t.json "custom_fields"
    t.text "formula"
    t.string "alignment"
    t.boolean "show_goal_progress", default: false
    t.datetime "discarded_at"
    t.json "default_weights"
    t.json "legend_formula"
    t.index ["discarded_at"], name: "index_template_questions_on_discarded_at"
    t.index ["question_id"], name: "index_template_questions_on_question_id"
    t.index ["template_id"], name: "index_template_questions_on_template_id"
  end

  create_table "templates", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "title"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "estimated_time"
    t.string "description"
    t.string "suitable_for"
    t.string "status", default: "live"
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_templates_on_discarded_at"
  end

  create_table "tokens", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "user_id"
    t.text "access_token"
    t.text "refresh_token"
    t.integer "expires_at"
    t.string "provider"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "calendar_grant", default: true
    t.index ["provider"], name: "index_tokens_on_provider"
    t.index ["user_id"], name: "index_tokens_on_user_id"
  end

  create_table "users", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email", null: false
    t.string "password_digest"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "role", default: 0
    t.string "uniq_code"
    t.integer "login_count", default: 0
    t.boolean "self_signed_up", default: false
    t.string "api_token"
    t.boolean "bypass_sso", default: false
    t.index ["email"], name: "index_users_on_email", unique: true
  end

  create_table "versions", options: "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "item_type", limit: 191, null: false
    t.integer "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.datetime "created_at"
    t.json "object"
    t.json "object_changes"
    t.index ["created_at"], name: "index_versions_on_created_at"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  add_foreign_key "account_ai_model_configs", "accounts"
  add_foreign_key "account_ai_model_configs", "ai_model_configs"
  add_foreign_key "account_products", "accounts"
  add_foreign_key "account_products", "employees", column: "discarded_by_id"
  add_foreign_key "account_products", "products"
  add_foreign_key "account_settings", "accounts"
  add_foreign_key "acl_role_members", "acl_roles"
  add_foreign_key "acl_role_members", "employees", column: "discarded_by_id"
  add_foreign_key "acl_role_sub_actions", "acl_roles"
  add_foreign_key "acl_role_sub_actions", "product_actions"
  add_foreign_key "acl_roles", "accounts"
  add_foreign_key "acl_roles", "employees", column: "discarded_by_id"
  add_foreign_key "acls", "employees", column: "created_by_id"
  add_foreign_key "acls", "employees", column: "discarded_by_id"
  add_foreign_key "ahoy_messages", "schedules"
  add_foreign_key "ahoy_messages", "simple_chat_chatters"
  add_foreign_key "ai_usage_logs", "ai_model_configs"
  add_foreign_key "biz_review_action_items", "biz_review_cards"
  add_foreign_key "biz_review_action_items", "biz_review_schedules"
  add_foreign_key "biz_review_action_items", "biz_review_schedules", column: "completed_biz_review_schedule_id"
  add_foreign_key "biz_review_action_items", "super_list_items"
  add_foreign_key "biz_review_cards", "biz_review_schedules"
  add_foreign_key "biz_review_cards", "biz_review_widgets"
  add_foreign_key "biz_review_cards", "employees", column: "added_by_id"
  add_foreign_key "biz_review_cards", "employees", column: "discarded_by_id"
  add_foreign_key "biz_review_comments", "biz_review_cards"
  add_foreign_key "biz_review_comments", "biz_review_schedules"
  add_foreign_key "biz_review_notes", "biz_review_cards"
  add_foreign_key "biz_review_notes", "employees", column: "last_edited_by_id"
  add_foreign_key "biz_review_schedule_goal_activity_mappings", "biz_review_schedules"
  add_foreign_key "biz_review_schedule_goal_activity_mappings", "goal_activities"
  add_foreign_key "biz_review_schedule_goal_activity_mappings", "goals"
  add_foreign_key "biz_review_schedules", "biz_reviews"
  add_foreign_key "biz_reviews", "accounts"
  add_foreign_key "biz_reviews", "employees", column: "created_by_id"
  add_foreign_key "bot_responses", "options"
  add_foreign_key "bot_responses", "questions"
  add_foreign_key "bot_threads", "bot_responses"
  add_foreign_key "bot_threads", "campaigns"
  add_foreign_key "bot_threads", "employee_chats"
  add_foreign_key "bot_threads", "employees"
  add_foreign_key "bot_threads", "questions"
  add_foreign_key "calibration_quota", "accounts"
  add_foreign_key "calibration_quota_ranges", "calibration_quota", column: "calibration_quota_id"
  add_foreign_key "calibrator_calibratees", "calibrators"
  add_foreign_key "calibrator_calibratees", "reviewees", column: "calibratee_id"
  add_foreign_key "calibrators", "calibration_quota", column: "calibration_quota_id"
  add_foreign_key "calibrators", "employees"
  add_foreign_key "calibrators", "review_cycle_phases"
  add_foreign_key "campaign_templates", "campaigns"
  add_foreign_key "campaigns", "accounts"
  add_foreign_key "campaigns", "surveys"
  add_foreign_key "coffee_connect_settings", "employees"
  add_foreign_key "company_data", "accounts"
  add_foreign_key "competencies", "accounts"
  add_foreign_key "competencies", "competencies", column: "main_competency_id"
  add_foreign_key "competencies", "competency_themes"
  add_foreign_key "competency_levels", "competencies"
  add_foreign_key "competency_settings", "accounts"
  add_foreign_key "competency_themes", "accounts"
  add_foreign_key "connected_workspace_user_maps", "accounts"
  add_foreign_key "connected_workspace_user_maps", "connected_workspace_users"
  add_foreign_key "connected_workspace_user_maps", "users"
  add_foreign_key "connected_workspace_user_maps", "users", column: "nominator_id"
  add_foreign_key "coupons", "plans"
  add_foreign_key "csv_import_chunks", "accounts"
  add_foreign_key "csv_import_chunks", "employees"
  add_foreign_key "custom_checkin_option_values", "custom_checkin_options"
  add_foreign_key "custom_checkin_option_values", "custom_checkin_values"
  add_foreign_key "custom_checkin_options", "custom_checkin_fields"
  add_foreign_key "custom_checkin_values", "custom_checkin_fields"
  add_foreign_key "custom_checkin_values", "goal_activities"
  add_foreign_key "custom_taggings", "custom_tags"
  add_foreign_key "custom_tags", "accounts"
  add_foreign_key "custom_tags", "employees", column: "added_by_id"
  add_foreign_key "department_goal_template_categories", "departments"
  add_foreign_key "department_goal_template_categories", "goal_template_categories"
  add_foreign_key "departments", "accounts"
  add_foreign_key "employee_attribute_values", "employee_attributes"
  add_foreign_key "employee_attribute_values", "employees"
  add_foreign_key "employee_attributes", "accounts"
  add_foreign_key "employee_audit_logs", "employees"
  add_foreign_key "employee_biz_review_action_items", "biz_review_action_items"
  add_foreign_key "employee_biz_review_action_items", "employees"
  add_foreign_key "employee_chat_questions", "employee_chats"
  add_foreign_key "employee_chat_questions", "questions"
  add_foreign_key "employee_chats", "accounts"
  add_foreign_key "employee_chats", "employees"
  add_foreign_key "employee_chats", "employees", column: "manager_id"
  add_foreign_key "employee_chats", "schedules"
  add_foreign_key "employee_data_imports", "accounts"
  add_foreign_key "employee_data_imports", "employees"
  add_foreign_key "employee_departments", "departments"
  add_foreign_key "employee_departments", "employees"
  add_foreign_key "employee_filter_conditions", "employee_filters"
  add_foreign_key "employee_goals", "employees"
  add_foreign_key "employee_goals", "goals"
  add_foreign_key "employee_idp_track_positions", "employee_idp_track_positions", column: "previous_position_id"
  add_foreign_key "employee_idp_track_positions", "employees"
  add_foreign_key "employee_idp_track_positions", "idp_track_positions"
  add_foreign_key "employee_kpis", "employees"
  add_foreign_key "employee_kpis", "kpis"
  add_foreign_key "employee_question_statuses", "employees"
  add_foreign_key "employee_question_statuses", "questions"
  add_foreign_key "employee_satisfactions", "accounts"
  add_foreign_key "employee_satisfactions", "employee_chats"
  add_foreign_key "employee_satisfactions", "employees"
  add_foreign_key "employee_satisfactions", "questions"
  add_foreign_key "employee_satisfactions", "schedules"
  add_foreign_key "employees", "accounts"
  add_foreign_key "employees", "departments"
  add_foreign_key "employees", "employees", column: "calendar_event_organizer_id"
  add_foreign_key "employees", "users"
  add_foreign_key "engagement_reports", "accounts"
  add_foreign_key "external_project_syncs", "accounts"
  add_foreign_key "external_project_syncs", "employees"
  add_foreign_key "feature_metadata_values", "metadata_values"
  add_foreign_key "feedback_block_competencies", "competencies"
  add_foreign_key "feedback_block_competencies", "feedback_question_blocks"
  add_foreign_key "feedback_providers", "employees"
  add_foreign_key "feedback_providers", "feedback_receivers"
  add_foreign_key "feedback_providers", "feedbacks"
  add_foreign_key "feedback_question_blocks", "questions"
  add_foreign_key "feedback_receivers", "employees"
  add_foreign_key "feedback_receivers", "feedbacks"
  add_foreign_key "feedback_requests", "employees", column: "creator_id"
  add_foreign_key "feedback_requests", "feedback_providers"
  add_foreign_key "feedback_responses", "feedback_providers"
  add_foreign_key "feedback_responses", "options"
  add_foreign_key "feedback_templates", "accounts"
  add_foreign_key "feedback_templates", "employees", column: "creator_id"
  add_foreign_key "feedbacks", "accounts"
  add_foreign_key "feedbacks", "employees", column: "creator_id"
  add_foreign_key "feedbacks", "feedback_templates"
  add_foreign_key "follow_up_questions", "options"
  add_foreign_key "goal_activities", "activity_logs"
  add_foreign_key "goal_activities", "employees"
  add_foreign_key "goal_activities", "employees", column: "edited_by_id"
  add_foreign_key "goal_activities", "goals"
  add_foreign_key "goal_activities", "goals", column: "source_key_result_id"
  add_foreign_key "goal_cycles", "accounts"
  add_foreign_key "goal_hierarchies", "goals", column: "child_id"
  add_foreign_key "goal_hierarchies", "goals", column: "parent_id"
  add_foreign_key "goal_integration_fields", "employees", column: "added_by_id"
  add_foreign_key "goal_integration_fields", "goals"
  add_foreign_key "goal_integration_fields", "goals", column: "project_id"
  add_foreign_key "goal_integration_fields", "integration_configs"
  add_foreign_key "goal_integration_fields", "integration_fields"
  add_foreign_key "goal_integration_values", "goal_activities", column: "activity_id"
  add_foreign_key "goal_integration_values", "goal_integration_fields"
  add_foreign_key "goal_kpis", "goals"
  add_foreign_key "goal_kpis", "kpis"
  add_foreign_key "goal_mentions", "employees"
  add_foreign_key "goal_mentions", "goal_activities"
  add_foreign_key "goal_milestones", "goal_cycles"
  add_foreign_key "goal_milestones", "goals"
  add_foreign_key "goal_milestones", "goals", column: "milestone_goal_id"
  add_foreign_key "goal_priorities", "employees", column: "last_updated_by_id"
  add_foreign_key "goal_priorities", "goals"
  add_foreign_key "goal_progresses", "goal_progress_types"
  add_foreign_key "goal_progresses", "goals"
  add_foreign_key "goal_table_filter_conditions", "goal_table_filters"
  add_foreign_key "goal_template_categories", "accounts"
  add_foreign_key "goal_template_template_categories", "goal_template_categories"
  add_foreign_key "goal_template_template_categories", "goal_templates"
  add_foreign_key "goal_templates", "accounts"
  add_foreign_key "goals", "accounts"
  add_foreign_key "goals", "departments"
  add_foreign_key "goals", "employees", column: "added_by_id"
  add_foreign_key "goals", "goal_cycles"
  add_foreign_key "goals", "goals", column: "duplicated_from_id"
  add_foreign_key "goals_departments", "departments"
  add_foreign_key "goals_departments", "goals"
  add_foreign_key "goals_onboarding_checklists", "employees"
  add_foreign_key "goals_settings", "accounts"
  add_foreign_key "goals_settings", "departments"
  add_foreign_key "goals_settings", "employees"
  add_foreign_key "health_scores", "accounts"
  add_foreign_key "home_page_actions", "employees"
  add_foreign_key "hris_configs", "accounts"
  add_foreign_key "idp_teams", "accounts"
  add_foreign_key "idp_track_position_competencies", "competencies"
  add_foreign_key "idp_track_position_competencies", "competency_levels"
  add_foreign_key "idp_track_position_competencies", "idp_track_positions"
  add_foreign_key "idp_track_positions", "idp_tracks"
  add_foreign_key "idp_tracks", "idp_teams"
  add_foreign_key "insights", "accounts"
  add_foreign_key "integration_configs", "accounts"
  add_foreign_key "integration_configs", "employees", column: "added_by_id"
  add_foreign_key "integration_configs", "integrations"
  add_foreign_key "integration_fields", "integrations"
  add_foreign_key "key_result_checkins", "employees"
  add_foreign_key "key_result_checkins", "key_results"
  add_foreign_key "key_results", "goals"
  add_foreign_key "kpi_activities", "employees"
  add_foreign_key "kpi_activities", "employees", column: "edited_by_id"
  add_foreign_key "kpi_activities", "kpis"
  add_foreign_key "kpis", "accounts"
  add_foreign_key "kpis", "departments"
  add_foreign_key "kpis", "employees", column: "added_by_id"
  add_foreign_key "kpis", "kpi_units"
  add_foreign_key "meeting_extra_infos", "meetings"
  add_foreign_key "meeting_templates", "accounts"
  add_foreign_key "mentions", "employees"
  add_foreign_key "metabase_groups", "employees"
  add_foreign_key "metabase_reports", "accounts"
  add_foreign_key "metabase_reports", "employees"
  add_foreign_key "metadata_fields", "accounts"
  add_foreign_key "metadata_fields", "employees", column: "created_by_id"
  add_foreign_key "metadata_options", "metadata_fields"
  add_foreign_key "metadata_value_metadata_options", "metadata_options"
  add_foreign_key "metadata_value_metadata_options", "metadata_values"
  add_foreign_key "metadata_values", "employees", column: "created_by_id"
  add_foreign_key "metadata_values", "goal_activities"
  add_foreign_key "metadata_values", "metadata_fields"
  add_foreign_key "microsoft_teams", "accounts"
  add_foreign_key "microsoft_teams", "employees"
  add_foreign_key "nova_chat_messages", "nova_conversations"
  add_foreign_key "nova_conversations", "account_ai_model_configs"
  add_foreign_key "nova_conversations", "employees"
  add_foreign_key "okr_update_goal_activity_mappings", "goal_activities"
  add_foreign_key "okr_update_goal_activity_mappings", "goals"
  add_foreign_key "okr_update_goal_activity_mappings", "okr_updates"
  add_foreign_key "okr_update_recipients", "okr_updates"
  add_foreign_key "okr_updates_goal_cycles", "goal_cycles"
  add_foreign_key "okr_updates_goal_cycles", "okr_updates"
  add_foreign_key "okr_updates_goals", "goals"
  add_foreign_key "okr_updates_goals", "okr_updates"
  add_foreign_key "omnichat_block_types", "omnichat_message_types"
  add_foreign_key "omnichat_blocks", "omnichat_block_types"
  add_foreign_key "omnichat_blocks", "omnichat_conversations"
  add_foreign_key "omnichat_blocks", "omnichat_logs"
  add_foreign_key "omnichat_logs", "omnichat_conversations"
  add_foreign_key "omnichat_logs", "omnichat_message_types"
  add_foreign_key "omnichat_users", "employees"
  add_foreign_key "one_on_one_agenda_templates", "suggested_talking_point_categories"
  add_foreign_key "one_on_one_agendas", "one_on_one_agenda_templates"
  add_foreign_key "one_on_one_agendas", "one_on_one_notes"
  add_foreign_key "one_on_one_note_versions", "one_on_one_notes"
  add_foreign_key "one_on_one_settings", "accounts"
  add_foreign_key "options", "drivers"
  add_foreign_key "options", "questions"
  add_foreign_key "payments", "accounts"
  add_foreign_key "payments", "coupons"
  add_foreign_key "payments", "subscriptions"
  add_foreign_key "pbx_credit_ledgers", "accounts"
  add_foreign_key "pbx_credit_ledgers", "employees", column: "depositor_id"
  add_foreign_key "pbx_credit_usage_logs", "employees"
  add_foreign_key "pbx_credit_usage_logs", "pbx_credit_ledgers"
  add_foreign_key "peer_configs", "review_cycle_user_roles", column: "peer_approval_role_id"
  add_foreign_key "peer_configs", "review_cycle_user_roles", column: "peer_selection_role_id"
  add_foreign_key "peer_configs", "review_cycles"
  add_foreign_key "performance_ratings", "employees"
  add_foreign_key "product_action_hierarchies", "product_actions", column: "parent_action_id"
  add_foreign_key "product_action_hierarchies", "product_actions", column: "sub_action_id"
  add_foreign_key "product_actions", "employees", column: "discarded_by_id"
  add_foreign_key "product_actions", "products"
  add_foreign_key "products", "employees", column: "discarded_by_id"
  add_foreign_key "profile_configurations", "accounts"
  add_foreign_key "project_integration_settings", "accounts"
  add_foreign_key "project_integration_settings", "integration_configs"
  add_foreign_key "project_integration_settings", "integrations"
  add_foreign_key "pulse_comments", "employees"
  add_foreign_key "pulse_likes", "employees"
  add_foreign_key "pulse_praise_receivers", "employees"
  add_foreign_key "pulse_praise_receivers", "pulse_praises"
  add_foreign_key "pulse_praises", "employees"
  add_foreign_key "pulse_praises", "pulse_badges"
  add_foreign_key "pulse_questions", "pulses"
  add_foreign_key "pulse_questions", "questions"
  add_foreign_key "pulse_reasons", "questions"
  add_foreign_key "pulse_response_meta_infos", "pulse_reasons"
  add_foreign_key "pulse_response_meta_infos", "pulse_responses"
  add_foreign_key "pulse_response_meta_infos", "pulses"
  add_foreign_key "pulse_responses", "employees"
  add_foreign_key "pulse_responses", "options"
  add_foreign_key "pulse_responses", "pulses"
  add_foreign_key "pulse_responses", "questions"
  add_foreign_key "pulse_responses", "schedules"
  add_foreign_key "pulse_template_questions", "pulse_templates"
  add_foreign_key "pulse_template_questions", "questions"
  add_foreign_key "pulses", "accounts"
  add_foreign_key "pulses", "employees"
  add_foreign_key "pulses", "employees", column: "manager_id"
  add_foreign_key "pulses", "employees", column: "reportee_id"
  add_foreign_key "pulses", "one_on_ones"
  add_foreign_key "pulses", "schedules"
  add_foreign_key "question_rounding_limits", "questions"
  add_foreign_key "question_stack_traces", "campaigns"
  add_foreign_key "question_stack_traces", "employee_chats"
  add_foreign_key "question_stack_traces", "employees"
  add_foreign_key "questions", "accounts"
  add_foreign_key "questions", "drivers"
  add_foreign_key "rating_ranges", "questions"
  add_foreign_key "reports", "employees", column: "created_by_id"
  add_foreign_key "response_actions", "responses"
  add_foreign_key "response_drivers", "drivers"
  add_foreign_key "responses", "employee_chats"
  add_foreign_key "responses", "employees"
  add_foreign_key "responses", "questions"
  add_foreign_key "responses", "schedules"
  add_foreign_key "review_copy_messages", "accounts"
  add_foreign_key "review_copy_messages", "review_cycle_phases"
  add_foreign_key "review_cycle_automations", "review_cycle_phases", column: "action_phase_type_id"
  add_foreign_key "review_cycle_automations", "review_cycle_phases", column: "trigger_phase_type_id"
  add_foreign_key "review_cycle_automations", "reviewer_types", column: "action_reviewer_type_id"
  add_foreign_key "review_cycle_automations", "reviewer_types", column: "trigger_reviewer_type_id"
  add_foreign_key "review_cycle_calibration_comments", "employees", column: "commenter_id"
  add_foreign_key "review_cycle_calibration_comments", "reviewees"
  add_foreign_key "review_cycle_calibration_questions", "questions"
  add_foreign_key "review_cycle_calibration_questions", "review_cycles"
  add_foreign_key "review_cycle_calibration_questions", "review_cycles", column: "past_review_cycle_id"
  add_foreign_key "review_cycle_goal_cycles", "goal_cycles"
  add_foreign_key "review_cycle_goal_cycles", "review_cycles"
  add_foreign_key "review_cycle_notification_recipients", "employees"
  add_foreign_key "review_cycle_notification_recipients", "review_cycle_notifications"
  add_foreign_key "review_cycle_notification_recipients", "review_cycle_phases"
  add_foreign_key "review_cycle_notification_recipients", "review_cycles"
  add_foreign_key "review_cycle_notifications", "employees", column: "creator_id"
  add_foreign_key "review_cycle_notifications", "review_copy_messages"
  add_foreign_key "review_cycle_notifications", "review_cycle_phases"
  add_foreign_key "review_cycle_notifications", "review_cycles"
  add_foreign_key "review_cycle_phases", "review_cycles"
  add_foreign_key "review_cycle_phases", "reviewer_types"
  add_foreign_key "review_cycle_question_visibilities", "review_cycle_template_questions"
  add_foreign_key "review_cycle_question_visibilities", "review_cycle_templates"
  add_foreign_key "review_cycle_question_visibilities", "review_cycle_visibility_matrices", column: "review_cycle_matrix_config_id"
  add_foreign_key "review_cycle_question_visibilities", "template_questions"
  add_foreign_key "review_cycle_reusable_template_questions", "questions"
  add_foreign_key "review_cycle_reusable_template_questions", "review_cycle_reusable_templates"
  add_foreign_key "review_cycle_template_questions", "questions"
  add_foreign_key "review_cycle_template_questions", "review_cycle_templates"
  add_foreign_key "review_cycle_templates", "employees"
  add_foreign_key "review_cycle_templates", "reviewer_types"
  add_foreign_key "review_cycle_templates", "templates"
  add_foreign_key "review_cycle_user_roles", "review_cycles"
  add_foreign_key "review_cycle_user_roles", "reviewer_types"
  add_foreign_key "review_cycle_visibility_matrices", "employees", column: "edited_by_id"
  add_foreign_key "review_cycle_visibility_matrices", "review_cycle_user_roles"
  add_foreign_key "review_cycle_visibility_matrices", "review_cycles"
  add_foreign_key "review_cycle_visibility_matrices", "reviewer_types"
  add_foreign_key "review_cycles", "accounts"
  add_foreign_key "review_cycles", "employees", column: "creator_id"
  add_foreign_key "review_responses", "employees", column: "calibrator_id"
  add_foreign_key "review_responses", "employees", column: "reviewee_employee_id"
  add_foreign_key "review_responses", "employees", column: "reviewer_employee_id"
  add_foreign_key "review_responses", "goals"
  add_foreign_key "review_responses", "options"
  add_foreign_key "review_responses", "options", column: "calibrated_score_option_id"
  add_foreign_key "review_responses", "questions"
  add_foreign_key "review_responses", "review_cycle_template_questions"
  add_foreign_key "review_responses", "review_cycles"
  add_foreign_key "review_responses", "reviewee_competencies"
  add_foreign_key "review_responses", "reviewees"
  add_foreign_key "review_responses", "reviewers"
  add_foreign_key "review_responses", "template_questions"
  add_foreign_key "reviewee_competencies", "competencies"
  add_foreign_key "reviewee_competencies", "competency_themes"
  add_foreign_key "reviewee_competencies", "employees", column: "approver_id"
  add_foreign_key "reviewee_competencies", "employees", column: "rejected_by_id"
  add_foreign_key "reviewee_competencies", "review_cycles"
  add_foreign_key "reviewee_competencies", "reviewees"
  add_foreign_key "reviewee_goal_event_logs", "employees", column: "event_creator_id"
  add_foreign_key "reviewee_goal_event_logs", "reviewees"
  add_foreign_key "reviewee_goals", "goals"
  add_foreign_key "reviewee_goals", "review_cycles"
  add_foreign_key "reviewee_goals", "reviewees"
  add_foreign_key "reviewee_snapshots", "employees"
  add_foreign_key "reviewee_snapshots", "reviewees"
  add_foreign_key "reviewee_snapshots", "reviewers"
  add_foreign_key "reviewees", "employees"
  add_foreign_key "reviewees", "one_on_ones"
  add_foreign_key "reviewees", "review_cycles"
  add_foreign_key "reviewer_types", "review_cycles"
  add_foreign_key "reviewers", "employees"
  add_foreign_key "reviewers", "employees", column: "approver_id"
  add_foreign_key "reviewers", "employees", column: "nominator_id"
  add_foreign_key "reviewers", "employees", column: "rejected_by_id"
  add_foreign_key "reviewers", "review_cycle_user_roles", column: "approver_role_id"
  add_foreign_key "reviewers", "review_cycle_user_roles", column: "nominator_role_id"
  add_foreign_key "reviewers", "review_cycles"
  add_foreign_key "reviewers", "reviewees"
  add_foreign_key "reviewers", "reviewer_types"
  add_foreign_key "schedule_questions", "questions"
  add_foreign_key "schedule_questions", "schedules"
  add_foreign_key "schedule_respondents", "employees", column: "respondent_id"
  add_foreign_key "schedule_respondents", "schedules"
  add_foreign_key "schedule_shared_admins", "employees"
  add_foreign_key "schedule_shared_admins", "schedules"
  add_foreign_key "schedule_templates", "schedules"
  add_foreign_key "schedule_templates", "templates"
  add_foreign_key "schedules", "accounts"
  add_foreign_key "schedules", "employees", column: "schedule_creator_id"
  add_foreign_key "slack_teams", "accounts"
  add_foreign_key "slack_teams", "employees"
  add_foreign_key "subscriptions", "accounts"
  add_foreign_key "subscriptions", "plans"
  add_foreign_key "super_list_items", "one_on_one_notes"
  add_foreign_key "switch_user_logs", "employees", column: "super_admin_id"
  add_foreign_key "switch_user_logs", "employees", column: "switched_to_id"
  add_foreign_key "tasks", "employees", column: "added_by_id"
  add_foreign_key "tasks", "employees", column: "owner_id"
  add_foreign_key "tasks", "goal_cycles"
  add_foreign_key "tasks", "goals", column: "project_id"
  add_foreign_key "template_categories", "accounts"
  add_foreign_key "template_categories", "employees", column: "creator_id"
  add_foreign_key "template_questions", "questions"
  add_foreign_key "template_questions", "templates"

  create_view "engagement_analysis", sql_definition: <<-SQL
      select `r`.`id` AS `responseId`,`r`.`response_text` AS `response_text`,`r`.`nps_score` AS `nps_score`,`r`.`option_id` AS `option_id`,`r`.`employee_chat_id` AS `response_employee_chat_id`,`ec`.`id` AS `employeeChatId`,`op`.`is_others_option` AS `is_others_option`,`e`.`id` AS `employeeId`,`e`.`full_name` AS `full_name`,`e`.`first_name` AS `first_name`,`e`.`date_of_exit` AS `date_of_exit`,`e`.`department_id` AS `department_id`,`e`.`date_of_joining` AS `date_of_joining`,`e`.`gender` AS `gender`,`e`.`location` AS `location`,`e`.`manager_id` AS `manager_id`,`m`.`full_name` AS `manager_full_name`,`m`.`first_name` AS `manager_first_name`,`e`.`user_id` AS `user_id`,`e`.`account_id` AS `account_id`,`e`.`age` AS `age`,`e`.`title` AS `title`,`e`.`level` AS `level`,`e`.`org_role` AS `org_role`,`e`.`division` AS `division`,`e`.`business_unit` AS `business_unit`,`e`.`reportees_count` AS `reportees_count`,`e`.`invited` AS `invited`,`e`.`relationship_updated` AS `relationship_updated`,`e`.`send_manager_checkin` AS `send_manager_checkin`,`dp`.`name` AS `department_name`,if((((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) < 1),(case when (round(((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 30),0) <= 6) then '0 - 6 Months' else '6 - 12 Months' end),(case when (((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) < 2) then '1 - 2 Years' when ((((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) >= 2) and (((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) < 3)) then '2 - 3 Years' when ((((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) >= 3) and (((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) < 4)) then '3 - 4 Years' when ((((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) >= 4) and (((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) < 5)) then '4 - 5 Years' when ((((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) >= 5) and (((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) < 9)) then '5 - 9 Years' when (((to_days(`s`.`when_to_run`) - to_days(`e`.`date_of_joining`)) DIV 365) >= 10) then '10+ Years' end)) AS `tenure`,`s`.`id` AS `scheduleId`,`s`.`when_to_run` AS `when_to_run`,`s`.`task_type` AS `task_type`,`s`.`task_executed` AS `task_executed`,`s`.`identified_survey` AS `identified_survey`,`q`.`id` AS `questionId`,`q`.`question_text` AS `question_text`,`q`.`question_type` AS `question_type`,`q`.`key` AS `questionKey`,`q`.`status` AS `status`,`q`.`driver_id` AS `driver_id`,`d`.`id` AS `driverId`,`d`.`key` AS `driverKey`,`d`.`name` AS `driverName`,`pr`.`rating` AS `rating`,(case when (`pr`.`rating` = 1) then 'Unsatisfactory' when (`pr`.`rating` = 2) then 'Improvement Needed' when (`pr`.`rating` = 3) then 'Met Expectations' when (`pr`.`rating` = 4) then 'Exceeded Expectations' when (`pr`.`rating` = 5) then 'Exceptional' else NULL end) AS `ratingText` from (((((((((`employee_chats` `ec` left join `responses` `r` on((`r`.`employee_chat_id` = `ec`.`id`))) left join `options` `op` on((`r`.`option_id` = `op`.`id`))) left join `schedules` `s` on((`ec`.`schedule_id` = `s`.`id`))) left join `employees` `e` on((`e`.`id` = `ec`.`employee_id`))) left join `departments` `dp` on((`dp`.`id` = `e`.`department_id`))) left join `employees` `m` on((`m`.`id` = `e`.`manager_id`))) left join `questions` `q` on((`q`.`id` = `r`.`question_id`))) left join `drivers` `d` on((`d`.`id` = `q`.`driver_id`))) left join (select `p1`.`id` AS `id`,`p1`.`rating` AS `rating`,`p1`.`employee_id` AS `employee_id`,`p1`.`created_at` AS `created_at`,`p1`.`updated_at` AS `updated_at`,`p1`.`raw_rating` AS `raw_rating` from `performance_ratings` `p1` where `p1`.`id` in (select max(`p2`.`id`) from `performance_ratings` `p2` where (`p1`.`employee_id` = `p2`.`employee_id`) group by `p2`.`employee_id`)) `pr` on((`pr`.`employee_id` = `e`.`id`))) where ((`s`.`discarded_at` is null) and (`ec`.`discarded_at` is null) and (`r`.`discarded_at` is null))
  SQL
  create_view "monthly_cohorts", sql_definition: <<-SQL
      select `u`.`email` AS `email`,`e`.`account_id` AS `account_id`,cast((`ft`.`first_event_time` - interval (dayofmonth(`ft`.`first_event_time`) - 1) day) as date) AS `cohort_month`,cast((`ae`.`time` - interval (dayofmonth(`ae`.`time`) - 1) day) as date) AS `event_month`,timestampdiff(MONTH,cast((`ft`.`first_event_time` - interval (dayofmonth(`ft`.`first_event_time`) - 1) day) as date),cast((`ae`.`time` - interval (dayofmonth(`ae`.`time`) - 1) day) as date)) AS `month_diff`,count(`ae`.`id`) AS `event_count` from (((`ahoy_events` `ae` left join `users` `u` on((`u`.`id` = `ae`.`user_id`))) left join `employees` `e` on((`e`.`user_id` = `u`.`id`))) left join (select min(`ae`.`time`) AS `first_event_time`,`ae`.`user_id` AS `user_id` from `ahoy_events` `ae` where ((`ae`.`user_id` is not null) and (`ae`.`visit_id` is not null)) group by `ae`.`user_id`) `ft` on((`ft`.`user_id` = `ae`.`user_id`))) group by `u`.`email`,`event_month`
  SQL
  create_view "view_annually_goal_trends", sql_definition: <<-SQL
      select 1 AS `id`,1 AS `goal_id`,1 AS `checkin_time`,1 AS `row_no`,1 AS `completed_value`,1 AS `previous_progress_value`,1 AS `status`,1 AS `previous_progress_status`,1 AS `description`
  SQL
  create_view "view_calendar_tables", sql_definition: <<-SQL
      select 1 AS `dt`,1 AS `y`,1 AS `q`,1 AS `m`,1 AS `d`,1 AS `dw`,1 AS `monthName`,1 AS `dayName`,1 AS `w`,1 AS `isWeekday`,1 AS `isHoliday`
  SQL
  create_view "view_employee_level_filter_acls", sql_definition: <<-SQL
      select `acls`.`id` AS `acl_id`,`acls`.`resource_type` AS `acl_resource_type`,`acls`.`resource_id` AS `acl_resource_id`,`acls`.`permission_type` AS `acl_permission_type`,`employees`.`id` AS `employee_id`,`employees`.`level` AS `employee_level` from (((`acls` join `employee_filters` on(((`acls`.`actorable_type` = 'EmployeeFilter') and (`acls`.`actorable_id` = `employee_filters`.`id`)))) join `employee_filter_conditions` on((`employee_filter_conditions`.`employee_filter_id` = `employee_filters`.`id`))) join `employees` on((0 <> (case when (((`employee_filter_conditions`.`table_attribute` = 'employees.level') and (`employee_filter_conditions`.`operator` = 'IN')) or (`employee_filter_conditions`.`operator` = '=')) then find_in_set(`employees`.`level`,regexp_replace(`employee_filter_conditions`.`value`,'[()\\'"]','')) end))))
  SQL
  create_view "view_fact_reviewee_goals", sql_definition: <<-SQL
      select `rg`.`id` AS `reviewee_goal_id`,`rg`.`goal_id` AS `goal_id`,`gcte`.`goal_title` AS `goal_title`,`gcte`.`parent_goal_title` AS `parent_goal_title`,`rg`.`reviewee_id` AS `reviewee_id`,`rg`.`review_cycle_id` AS `review_cycle_id`,`rg`.`weightage` AS `weightage`,`rg`.`approver_id` AS `approver_id`,`rc`.`account_id` AS `account_id` from ((`reviewee_goals` `rg` join `review_cycles` `rc` on(((`rg`.`review_cycle_id` = `rc`.`id`) and (`rc`.`discarded_at` is null)))) left join (select `g`.`id` AS `goal_id`,`g`.`title` AS `goal_title`,`gh`.`parent_id` AS `parent_goal_id`,`pg`.`title` AS `parent_goal_title`,`e`.`id` AS `owner_id` from ((((`goals` `g` left join `employee_goals` `eg` on(((`g`.`id` = `eg`.`goal_id`) and (`eg`.`discarded_at` is null)))) left join `employees` `e` on((`eg`.`employee_id` = `e`.`id`))) left join `goal_hierarchies` `gh` on(((`g`.`id` = `gh`.`child_id`) and (`gh`.`depth` = 1) and (`gh`.`objective_type` in (0,1,2))))) left join `goals` `pg` on((`gh`.`parent_id` = `pg`.`id`))) where (`g`.`discarded_at` is null)) `gcte` on((`rg`.`goal_id` = `gcte`.`goal_id`))) where ((`rg`.`discarded_at` is null) and (`rg`.`exclude` = false))
  SQL
  create_view "view_fact_reviewees", sql_definition: <<-SQL
      select `re`.`id` AS `reviewee_id`,`re`.`review_cycle_id` AS `review_cycle_id`,`re`.`employee_id` AS `employee_id`,`e`.`account_id` AS `account_id`,(case when `re`.`self_review_done` then 'Yes' else 'No' end) AS `self_review_done`,(case when `re`.`peer_selection_done` then 'Yes' else 'No' end) AS `peer_selection_done`,(case when `re`.`peer_approval_done` then 'Yes' else 'No' end) AS `peer_approval_done`,`re`.`review_writing_percent` AS `review_writing_percent`,(case when `re`.`manager_summary_done` then 'Yes' else 'No' end) AS `manager_summary_done`,(case when `re`.`confidential` then 'Yes' else 'No' end) AS `confidential`,(case when `re`.`release_review_done` then 'Yes' else 'No' end) AS `release_review_done`,(case when `re`.`goal_approval_done` then 'Yes' else 'No' end) AS `goal_approval_done`,(case when `re`.`goal_selection_done` then 'Yes' else 'No' end) AS `goal_selection_done`,`re`.`one_on_one_id` AS `one_on_one_id`,(case when `re`.`competency_selection_done` then 'Yes' else 'No' end) AS `competency_selection_done`,(case when `re`.`competency_approval_done` then 'Yes' else 'No' end) AS `competency_approval_done`,count(distinct `rg`.`id`) AS `reviewee_goals_count`,count(distinct (case when (`rg`.`approver_id` is not null) then `rg`.`goal_id` end)) AS `reviewee_goals_approved_count`,count(distinct (case when (`rr`.`id` is not null) then `rr`.`goal_id` end)) AS `reviewee_goals_review_written_count`,(select count(0) from (`reviewers` `rv` join `reviewer_types` `rt` on((`rv`.`reviewer_type_id` = `rt`.`id`))) where ((`rv`.`reviewee_id` = `re`.`id`) and (`rt`.`reviewer_type` = 'peer'))) AS `peer_reviewer_count`,(select count(0) from (`reviewers` `rv` join `reviewer_types` `rt` on((`rv`.`reviewer_type_id` = `rt`.`id`))) where ((`rv`.`reviewee_id` = `re`.`id`) and (`rv`.`review_submitted` = true) and (`rt`.`reviewer_type` = 'peer'))) AS `peer_review_complete_count`,(select count(0) from (`reviewers` `rv` join `reviewer_types` `rt` on((`rv`.`reviewer_type_id` = `rt`.`id`))) where ((`rv`.`reviewee_id` = `re`.`id`) and (`rt`.`reviewer_type` = 'direct_report'))) AS `upward_reviewer_count`,(select count(0) from (`reviewers` `rv` join `reviewer_types` `rt` on((`rv`.`reviewer_type_id` = `rt`.`id`))) where ((`rv`.`reviewee_id` = `re`.`id`) and (`rv`.`review_submitted` = true) and (`rt`.`reviewer_type` = 'direct_report'))) AS `upward_review_complete_count`,(select count(0) from (`reviewers` `rv` join `reviewer_types` `rt` on((`rv`.`reviewer_type_id` = `rt`.`id`))) where ((`rv`.`reviewee_id` = `re`.`id`) and (`rt`.`reviewer_type` not in ('self','manager','direct_report','peer')))) AS `custom_reviewer_count`,(select count(0) from (`reviewers` `rv` join `reviewer_types` `rt` on((`rv`.`reviewer_type_id` = `rt`.`id`))) where ((`rv`.`reviewee_id` = `re`.`id`) and (`rv`.`review_submitted` = true) and (`rt`.`reviewer_type` not in ('self','manager','direct_report','peer')))) AS `custom_review_complete_count` from (((`reviewees` `re` join `employees` `e` on((`re`.`employee_id` = `e`.`id`))) left join `reviewee_goals` `rg` on(((`re`.`id` = `rg`.`reviewee_id`) and (`rg`.`exclude` = false) and (`rg`.`discarded_at` is null)))) left join `review_responses` `rr` on(((`rg`.`goal_id` = `rr`.`goal_id`) and (`rr`.`discarded_at` is null)))) where (`re`.`discarded_at` is null) group by `re`.`id`,`re`.`employee_id`
  SQL
  create_view "view_fact_reviewers", sql_definition: <<-SQL
      select `rv`.`id` AS `reviewer_id`,`rv`.`review_cycle_id` AS `review_cycle_id`,`rv`.`reviewee_id` AS `reviewee_id`,`re`.`employee_id` AS `reviewee_employee_id`,`rv`.`reviewer_type_id` AS `reviewer_type_id`,`rt`.`first_person` AS `reviewer_type`,`rv`.`employee_id` AS `reviewer_employee_id`,`rv`.`nominator_id` AS `nominator_id`,`rv`.`approver_id` AS `approver_id`,`rv`.`rejected_by_id` AS `rejected_by_id`,`rv`.`review_submitted` AS `review_submitted`,`reviewee_e`.`account_id` AS `account_id`,`reviewee_e`.`full_name` AS `reviewee_name`,`reviewee_e_user`.`email` AS `reviewee_email`,`reviewer_e`.`full_name` AS `reviewer_name`,`reviewer_e_user`.`email` AS `reviewer_email`,if(`rt`.`approval_required`,(case when (`rv`.`approver_id` is not null) then 'Yes' when (`rv`.`rejected_by_id` is not null) then 'No' else 'No' end),'Yes') AS `approval_status`,(case when ((`rv`.`review_submitted` = false) and exists(select 1 from `review_responses` `rr` where ((`rr`.`reviewer_id` = `rv`.`id`) and (`rr`.`discarded_at` is null)))) then 'Yes' else 'No' end) AS `review_saved`,(case when `rv`.`review_submitted` then 'Yes' else 'No' end) AS `review_submitted_status` from ((((((`reviewers` `rv` join `reviewer_types` `rt` on(((`rv`.`reviewer_type_id` = `rt`.`id`) and (`rt`.`discarded_at` is null)))) join `reviewees` `re` on(((`rv`.`reviewee_id` = `re`.`id`) and (`re`.`discarded_at` is null)))) join `employees` `reviewee_e` on((`re`.`employee_id` = `reviewee_e`.`id`))) join `employees` `reviewer_e` on((`rv`.`employee_id` = `reviewer_e`.`id`))) join `users` `reviewee_e_user` on((`reviewee_e`.`user_id` = `reviewee_e_user`.`id`))) join `users` `reviewer_e_user` on((`reviewer_e`.`user_id` = `reviewer_e_user`.`id`))) where (`rv`.`discarded_at` is null)
  SQL
  create_view "view_goal_monthly_progress_breakdowns", sql_definition: <<-SQL
      select `partitioned_activities`.`goal_id` AS `goal_id`,`partitioned_activities`.`employee_id` AS `employee_id`,`partitioned_activities`.`checkin_time` AS `checkin_time`,`partitioned_activities`.`created_at` AS `created_at`,month(`partitioned_activities`.`checkin_time`) AS `checkin_month`,`partitioned_activities`.`completed_value` AS `completed_value`,`partitioned_activities`.`status` AS `status`,`partitioned_activities`.`description` AS `description`,`partitioned_activities`.`row_no` AS `row_no`,`partitioned_activities`.`system_comment` AS `system_comment` from (select `goal_activities`.`id` AS `id`,`goal_activities`.`description` AS `description`,`goal_activities`.`completed_value` AS `completed_value`,`goal_activities`.`discarded_at` AS `discarded_at`,`goal_activities`.`goal_id` AS `goal_id`,`goal_activities`.`employee_id` AS `employee_id`,`goal_activities`.`created_at` AS `created_at`,`goal_activities`.`updated_at` AS `updated_at`,`goal_activities`.`status` AS `status`,`goal_activities`.`source` AS `source`,`goal_activities`.`source_key_result_id` AS `source_key_result_id`,`goal_activities`.`checkin_time` AS `checkin_time`,`goal_activities`.`activity_type` AS `activity_type`,`goal_activities`.`system_comment` AS `system_comment`,`goal_activities`.`previous_progress_value` AS `previous_progress_value`,`goal_activities`.`edited_at` AS `edited_at`,`goal_activities`.`edited_by_id` AS `edited_by_id`,`goal_activities`.`previous_progress_status` AS `previous_progress_status`,`goal_activities`.`checkin_type` AS `checkin_type`,`goal_activities`.`old_description` AS `old_description`,`goal_activities`.`description_json` AS `description_json`,`goal_activities`.`previous_progress_type` AS `previous_progress_type`,`goal_activities`.`progress_type` AS `progress_type`,`goal_activities`.`activity_log_id` AS `activity_log_id`,`goal_activities`.`milestone_summary_method` AS `milestone_summary_method`,row_number() OVER (PARTITION BY `goal_activities`.`goal_id`,month(`goal_activities`.`checkin_time`) ORDER BY `goal_activities`.`checkin_time` desc,`goal_activities`.`created_at` desc,month(`goal_activities`.`checkin_time`) desc )  AS `row_no` from `goal_activities` where ((`goal_activities`.`checkin_time` is not null) and (`goal_activities`.`source` not in (11,12,25)) and (`goal_activities`.`discarded_at` is null))) `partitioned_activities`
  SQL
  create_view "view_goal_quarterly_progress_breakdowns", sql_definition: <<-SQL
      select `partitioned_activities`.`goal_id` AS `goal_id`,`partitioned_activities`.`employee_id` AS `employee_id`,`partitioned_activities`.`checkin_time` AS `checkin_time`,`partitioned_activities`.`created_at` AS `created_at`,quarter(`partitioned_activities`.`checkin_time`) AS `checkin_quarter`,year(`partitioned_activities`.`checkin_time`) AS `checkin_year`,concat('Q',quarter(`partitioned_activities`.`checkin_time`),'-',year(`partitioned_activities`.`checkin_time`)) AS `quarter_year`,`partitioned_activities`.`completed_value` AS `completed_value`,`partitioned_activities`.`status` AS `status`,`partitioned_activities`.`description` AS `description`,`partitioned_activities`.`row_no` AS `row_no`,`partitioned_activities`.`system_comment` AS `system_comment` from (select `goal_activities`.`id` AS `id`,`goal_activities`.`description` AS `description`,`goal_activities`.`completed_value` AS `completed_value`,`goal_activities`.`discarded_at` AS `discarded_at`,`goal_activities`.`goal_id` AS `goal_id`,`goal_activities`.`employee_id` AS `employee_id`,`goal_activities`.`created_at` AS `created_at`,`goal_activities`.`updated_at` AS `updated_at`,`goal_activities`.`status` AS `status`,`goal_activities`.`source` AS `source`,`goal_activities`.`source_key_result_id` AS `source_key_result_id`,`goal_activities`.`checkin_time` AS `checkin_time`,`goal_activities`.`activity_type` AS `activity_type`,`goal_activities`.`system_comment` AS `system_comment`,`goal_activities`.`previous_progress_value` AS `previous_progress_value`,`goal_activities`.`edited_at` AS `edited_at`,`goal_activities`.`edited_by_id` AS `edited_by_id`,`goal_activities`.`previous_progress_status` AS `previous_progress_status`,`goal_activities`.`checkin_type` AS `checkin_type`,`goal_activities`.`old_description` AS `old_description`,`goal_activities`.`description_json` AS `description_json`,`goal_activities`.`previous_progress_type` AS `previous_progress_type`,`goal_activities`.`progress_type` AS `progress_type`,`goal_activities`.`activity_log_id` AS `activity_log_id`,`goal_activities`.`milestone_summary_method` AS `milestone_summary_method`,row_number() OVER (PARTITION BY `goal_activities`.`goal_id`,quarter(`goal_activities`.`checkin_time`),year(`goal_activities`.`checkin_time`) ORDER BY `goal_activities`.`checkin_time` desc,`goal_activities`.`created_at` desc,quarter(`goal_activities`.`checkin_time`) desc )  AS `row_no` from `goal_activities` where ((`goal_activities`.`checkin_time` is not null) and (`goal_activities`.`source` not in (11,12,25)) and (`goal_activities`.`discarded_at` is null))) `partitioned_activities`
  SQL
  create_view "view_goal_weekly_progress_breakdowns", sql_definition: <<-SQL
      select `partitioned_activities`.`goal_id` AS `goal_id`,`partitioned_activities`.`employee_id` AS `employee_id`,`partitioned_activities`.`checkin_time` AS `checkin_time`,week(`partitioned_activities`.`checkin_time`,0) AS `checkin_week_of_year`,`partitioned_activities`.`completed_value` AS `completed_value`,`partitioned_activities`.`status` AS `status`,`partitioned_activities`.`description` AS `description`,`partitioned_activities`.`row_no` AS `row_no`,`partitioned_activities`.`system_comment` AS `system_comment` from (select `goal_activities`.`id` AS `id`,`goal_activities`.`description` AS `description`,`goal_activities`.`completed_value` AS `completed_value`,`goal_activities`.`discarded_at` AS `discarded_at`,`goal_activities`.`goal_id` AS `goal_id`,`goal_activities`.`employee_id` AS `employee_id`,`goal_activities`.`created_at` AS `created_at`,`goal_activities`.`updated_at` AS `updated_at`,`goal_activities`.`status` AS `status`,`goal_activities`.`source` AS `source`,`goal_activities`.`source_key_result_id` AS `source_key_result_id`,`goal_activities`.`checkin_time` AS `checkin_time`,`goal_activities`.`activity_type` AS `activity_type`,`goal_activities`.`system_comment` AS `system_comment`,`goal_activities`.`previous_progress_value` AS `previous_progress_value`,`goal_activities`.`edited_at` AS `edited_at`,`goal_activities`.`edited_by_id` AS `edited_by_id`,`goal_activities`.`previous_progress_status` AS `previous_progress_status`,`goal_activities`.`checkin_type` AS `checkin_type`,`goal_activities`.`old_description` AS `old_description`,`goal_activities`.`description_json` AS `description_json`,`goal_activities`.`previous_progress_type` AS `previous_progress_type`,`goal_activities`.`progress_type` AS `progress_type`,`goal_activities`.`activity_log_id` AS `activity_log_id`,`goal_activities`.`milestone_summary_method` AS `milestone_summary_method`,row_number() OVER (PARTITION BY `goal_activities`.`goal_id`,week(`goal_activities`.`checkin_time`,0) ORDER BY `goal_activities`.`checkin_time` desc,week(`goal_activities`.`checkin_time`,0) desc )  AS `row_no` from `goal_activities` where ((`goal_activities`.`checkin_time` is not null) and (`goal_activities`.`source` not in (11,12,25)) and (`goal_activities`.`discarded_at` is null))) `partitioned_activities`
  SQL
  create_view "view_kpi_checkins", sql_definition: <<-SQL
      select 1 AS `id`,1 AS `kpi_id`,1 AS `checkin_time`,1 AS `checkin_date`,1 AS `checkin_week`,1 AS `checkin_month`,1 AS `checkin_quarter`,1 AS `checkin_year`,1 AS `current_value`,1 AS `current_target`,1 AS `previous_value`,1 AS `previous_target`,1 AS `row_no`,1 AS `summarize_with`,1 AS `kpi_unit_name`,1 AS `kpi_unit_symbol`,1 AS `created_at`,1 AS `relation_to_target`,1 AS `activity_type`
  SQL
  create_view "view_kpi_comments", sql_definition: <<-SQL
      select 1 AS `id`,1 AS `kpi_id`,1 AS `employee_id`,1 AS `description`,1 AS `checkin_time`,1 AS `checkin_date`,1 AS `checkin_week`,1 AS `checkin_month`,1 AS `checkin_quarter`,1 AS `checkin_year`,1 AS `full_name`,1 AS `comment_present`,1 AS `created_at`
  SQL
  create_view "view_kpi_targets", sql_definition: <<-SQL
      select 1 AS `id`,1 AS `kpi_id`,1 AS `checkin_time`,1 AS `checkin_date`,1 AS `checkin_week`,1 AS `checkin_month`,1 AS `checkin_quarter`,1 AS `checkin_year`,1 AS `current_value`,1 AS `current_target`,1 AS `previous_value`,1 AS `previous_target`,1 AS `row_no`,1 AS `summarize_with`,1 AS `kpi_unit_name`,1 AS `kpi_unit_symbol`,1 AS `created_at`,1 AS `relation_to_target`,1 AS `activity_type`
  SQL
  create_view "view_monthly_goal_trends", sql_definition: <<-SQL
      select 1 AS `id`,1 AS `goal_id`,1 AS `checkin_time`,1 AS `row_no`,1 AS `completed_value`,1 AS `previous_progress_value`,1 AS `status`,1 AS `previous_progress_status`,1 AS `description`
  SQL
  create_view "view_permissions", sql_definition: <<-SQL
      select distinct `acls`.`resource_id` AS `resource_id`,`acls`.`resource_type` AS `resource_type`,`acls`.`permission_type` AS `permission_type`,`acls`.`actorable_id` AS `employee_id`,`acls`.`actorable_type` AS `actorable_type` from `acls` where ((`acls`.`discarded_at` is null) and (`acls`.`actorable_type` = 'Employee')) union select distinct `a`.`resource_id` AS `resource_id`,`a`.`resource_type` AS `resource_type`,`a`.`permission_type` AS `permission_type`,`de`.`id` AS `employee_id`,`a`.`actorable_type` AS `actorable_type` from (`acls` `a` join `employees` `de` on((`a`.`actorable_id` = `de`.`department_id`))) where ((`a`.`discarded_at` is null) and (`a`.`actorable_type` = 'Department') and (`de`.`department_id` is not null)) union select distinct `a`.`resource_id` AS `resource_id`,`a`.`resource_type` AS `resource_type`,`a`.`permission_type` AS `permission_type`,`ae`.`id` AS `employee_id`,`a`.`actorable_type` AS `actorable_type` from (`acls` `a` join `employees` `ae` on((`a`.`actorable_id` = `ae`.`account_id`))) where ((`a`.`discarded_at` is null) and (`a`.`actorable_type` = 'Account') and (`ae`.`account_id` is not null))
  SQL
  create_view "view_quarterly_goal_trends", sql_definition: <<-SQL
      select 1 AS `id`,1 AS `goal_id`,1 AS `checkin_time`,1 AS `row_no`,1 AS `completed_value`,1 AS `previous_progress_value`,1 AS `status`,1 AS `previous_progress_status`,1 AS `description`
  SQL
end
