SELECT 
  acls.id AS acl_id, 
  acls.resource_type AS acl_resource_type, 
  acls.resource_id AS acl_resource_id,
  acls.permission_type AS acl_permission_type,
  employees.id AS employee_id, 
  employees.level AS employee_level 
FROM 
  acls 
  INNER JOIN employee_filters ON acls.actorable_type = 'EmployeeFilter' 
  AND acls.actorable_id = employee_filters.id 
  INNER JOIN employee_filter_conditions ON employee_filter_conditions.employee_filter_id = employee_filters.id 
  INNER JOIN employees ON (
    CASE WHEN employee_filter_conditions.table_attribute = 'employees.level'
    AND employee_filter_conditions.operator = 'IN' 
    OR employee_filter_conditions.operator = '=' THEN FIND_IN_SET(
      employees.level COLLATE utf8mb4_general_ci, 
      REGEXP_REPLACE(
        employee_filter_conditions.value COLLATE utf8mb4_general_ci, 
        '[()\'"]', ''
      )
    ) END
  );
