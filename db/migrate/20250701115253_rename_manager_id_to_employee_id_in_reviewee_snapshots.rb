class RenameManagerIdToEmployeeIdInRevieweeSnapshots < ActiveRecord::Migration[5.2]
    def change
    # Remove the foreign key constraint first
    remove_foreign_key :reviewee_snapshots, column: :manager_id
    
    # Rename the column
    rename_column :reviewee_snapshots, :manager_id, :employee_id
    
    # Add new foreign key constraint
    add_foreign_key :reviewee_snapshots, :employees, column: :employee_id
  end
end
