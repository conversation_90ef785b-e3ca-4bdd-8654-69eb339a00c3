class CreateRevieweeSnapshots < ActiveRecord::Migration[5.2]
  def change
    create_table :reviewee_snapshots do |t|
      t.references :reviewee, null: false, foreign_key: true
      t.references :manager, foreign_key: { to_table: :employees }
      t.references :reviewer, foreign_key: { to_table: :reviewers }
      t.string :custom_attribute
      t.string :reason
    end
    add_index :reviewee_snapshots, :reason
  end
end
