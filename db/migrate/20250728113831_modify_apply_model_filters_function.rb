class ModifyApplyModelFiltersFunction < ActiveRecord::Migration[5.2]
   def up
    file_path = Rails.root.join('db', 'user_defined_functions', 'fn_apply_model_filter_v03.sql')

    return unless File.exist?(file_path)

    execute 'DROP FUNCTION IF EXISTS fn_apply_model_filter;'
    execute File.read(file_path)
  end

  def down
    file_path = Rails.root.join('db', 'user_defined_functions', 'fn_apply_model_filter_v02.sql')
    
    return unless File.exist?(file_path)

    execute 'DROP FUNCTION IF EXISTS fn_apply_model_filter;'
    execute File.read(file_path)
  end  
end
