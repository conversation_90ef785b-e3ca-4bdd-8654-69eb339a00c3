CREATE FUNCTION fn_apply_model_filter(
  filter_type VARCHAR(255),
  filter_id INT,
  variable_values JSON
) RETURNS TEXT
READS SQL DATA
BEGIN
  DECLARE dynamic_query_count INT DEFAULT 0;
  DECLARE base_query TEXT;
  DECLARE filter_conditions_json JSON;
  DECLARE condition_dynamic_rule TEXT;
  DECLARE condition_table_attribute VARCHAR(255);
  DECLARE condition_operator VARCHAR(255);
  DECLARE condition_value TEXT;
  DECLARE condition_query TEXT;
  DECLARE dynamic_query TEXT;
  DECLARE subquery_table_name TEXT;
  DECLARE condition_count INT DEFAULT 0;
  DECLARE counter INT DEFAULT 0;
  DECLARE model_table_name VARCHAR(255);

  -- Determine model table and condition count
  CASE filter_type
    WHEN 'employee_filter' THEN
      SET model_table_name = 'employees';
      SET condition_count = (SELECT COUNT(id) FROM employee_filter_conditions WHERE employee_filter_id = filter_id);
    WHEN 'goal_table_filter' THEN
      SET model_table_name = 'goals';
      SET condition_count = (SELECT COUNT(id) FROM goal_table_filter_conditions WHERE goal_table_filter_id = filter_id);
  END CASE;

  SET base_query = CONCAT('SELECT ', model_table_name, '.id FROM ', model_table_name, ' WHERE 1=1');

  -- Load filter conditions as JSON
  CASE filter_type
    WHEN 'employee_filter' THEN
      SET filter_conditions_json = (SELECT JSON_ARRAYAGG(
        JSON_OBJECT(
          'dynamic_rule', dynamic_rule,
          'table_attribute', table_attribute,
          'operator', operator,
          'value', `value`
        )
      ) FROM employee_filter_conditions WHERE employee_filter_id = filter_id);
    WHEN 'goal_table_filter' THEN
      SET filter_conditions_json = (SELECT JSON_ARRAYAGG(
        JSON_OBJECT(
          'dynamic_rule', dynamic_rule,
          'table_attribute', table_attribute,
          'operator', operator,
          'value', `value`
        )
      ) FROM goal_table_filter_conditions WHERE goal_table_filter_id = filter_id);
  END CASE;

  -- Loop through each condition
  WHILE counter < condition_count DO
    SET condition_dynamic_rule = NULLIF(JSON_UNQUOTE(JSON_EXTRACT(filter_conditions_json, CONCAT('$[', counter, '].dynamic_rule'))), 'null');
    SET condition_table_attribute = NULLIF(JSON_UNQUOTE(JSON_EXTRACT(filter_conditions_json, CONCAT('$[', counter, '].table_attribute'))), 'null');
    SET condition_operator = NULLIF(JSON_UNQUOTE(JSON_EXTRACT(filter_conditions_json, CONCAT('$[', counter, '].operator'))), 'null');
    SET condition_value = NULLIF(JSON_UNQUOTE(JSON_EXTRACT(filter_conditions_json, CONCAT('$[', counter, '].value'))), 'null');

    SET counter = counter + 1;

    IF condition_dynamic_rule IS NOT NULL THEN
      SET dynamic_query_count = dynamic_query_count + 1;
      SET subquery_table_name = CONCAT('r', dynamic_query_count);
      SET dynamic_query = condition_dynamic_rule;

      -- Replace variables in the dynamic rule
      IF LOCATE('{{', dynamic_query) > 0 AND LOCATE('}}', dynamic_query) > 0 AND LENGTH(variable_values) > 0 THEN
        SET dynamic_query = REGEXP_REPLACE(dynamic_query, '\\{\\{[^\\}]*\\}\\}',
          JSON_UNQUOTE(JSON_EXTRACT(variable_values,
            CONCAT('$."',
              SUBSTRING_INDEX(SUBSTRING_INDEX(dynamic_query, '{{', -1), '}}', 1),
            '"')
          ))
        );
      END IF;

      SET base_query = CONCAT(base_query, ' AND ', model_table_name, '.id IN (SELECT ', subquery_table_name, '.id FROM (', dynamic_query, ') AS ', subquery_table_name, ')');
    ELSE
      -- Replace variables in the value
      IF LOCATE('{{', condition_value) > 0 AND LOCATE('}}', condition_value) > 0 AND LENGTH(variable_values) > 0 THEN
        SET condition_value = REGEXP_REPLACE(condition_value, '\\{\\{[^\\}]*\\}\\}',
          JSON_UNQUOTE(JSON_EXTRACT(variable_values,
            CONCAT('$."',
              SUBSTRING_INDEX(SUBSTRING_INDEX(condition_value, '{{', -1), '}}', 1),
            '"')
          ))
        );
      END IF;

      IF condition_operator != 'IN' THEN
        -- The only update in this version is the use of QUOTE function here 
        SET condition_value = QUOTE(condition_value);
      END IF;

      SET condition_query = CONCAT(condition_table_attribute, ' ', condition_operator, ' ', condition_value);
      SET base_query = CONCAT(base_query, ' AND ', condition_query);
    END IF;
  END WHILE;

  RETURN base_query;
END;