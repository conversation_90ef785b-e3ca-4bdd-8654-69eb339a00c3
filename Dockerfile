FROM ruby:2.5.1

RUN echo "deb http://archive.debian.org/debian stretch main contrib non-free" > /etc/apt/sources.list

# Install the AWS CLI
RUN apt-get update && \
    apt-get -y install python curl unzip awscli --allow-unauthenticated



# Install apt based dependencies required to run Rails as
# well as RubyGems. As the Ruby image itself is based on a
# Debian image, we use apt-get to install those
RUN apt-get update \
    && apt-get install -y build-essential git nodejs imagemagick wkhtmltopdf xvfb libfontconfig xfonts-75dpi fontconfig unixodbc unixodbc-dev --allow-unauthenticated\
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update && \
    apt-get install -y poppler-utils --allow-unauthenticated

# Set default encoding as UTF-8 to avoid using US-ASCII encoding
ENV LANG=en_US.UTF-8

# Configure the main working directory. This is the base
# directory used in any further RUN, COPY, and ENTRYPOINT
# commands.
RUN mkdir -p /culturegrade-api
WORKDIR /culturegrade-api


# Copy the Gemfile as well as the Gemfile.lock and install
# the RubyGems. This is a separate step so the dependencies
# will be cached unless changes to one of those two files
# are made.
ADD simple_chat /culturegrade-api/simple_chat
ADD Gemfile.lock Gemfile /culturegrade-api/
# First completely clean up existing bundler installations
# First, clean up any existing bundler installations
RUN gem uninstall bundler -a -x --force || true

# Install specific RubyGems and Bundler versions compatible with Ruby 2.5.0
RUN gem install rubygems-update -v 2.7.7 && \
    update_rubygems --no-document && \
    gem install bundler -v 1.17.3 && \
    gem cleanup bundler

# Set up bundle configuration
RUN bundle _1.17.3_ config git.allow_insecure true && \
    bundle _1.17.3_ config --global jobs 20 && \
    bundle _1.17.3_ config --global retry 5

# Install gems
RUN bundle _1.17.3_ install --without development test

# Get environment variable and install the new entry-point script
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]

# Add the version file
ADD VERSION .

# Copy the main application.
COPY . ./

EXPOSE 3000

CMD ["bundle", "exec", "rails", "server", "-b", "0.0.0.0"]