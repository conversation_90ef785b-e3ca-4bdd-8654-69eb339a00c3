# frozen_string_literal: true

require 'rails_helper'

describe ManagerChanges::CheckReviewerTypePhaseLaunched, type: :service do
  let!(:account) { create(:account, :with_employees) }
  let!(:review_cycle) { create(:review_cycle, account: account) }
  let!(:reviewer_employee) { account.employees.first }

  # Shared examples for common test scenarios
  shared_examples 'phase not launched' do
    it 'returns false when phase is not launched' do
      service = described_class.call(
        review_cycle_id: review_cycle.id,
        reviewer_employee_id: reviewer_employee.id,
        target_reviewer_type: reviewer_type_name,
      )

      expect(service).to be_success
      expect(service.result).to be false
    end
  end

  shared_examples 'phase launched without notification' do
    before { phase.update!(start_time: 1.day.ago) }

    it 'returns false when phase is launched but no notification received' do
      service = described_class.call(
        review_cycle_id: review_cycle.id,
        reviewer_employee_id: reviewer_employee.id,
        target_reviewer_type: reviewer_type_name,
      )

      expect(service).to be_success
      expect(service.result).to be false
    end
  end

  shared_examples 'phase launched with notification' do |phase_start_time = 1.day.ago|
    before { phase.update!(start_time: phase_start_time) }

    let!(:reviewee) { create(:reviewee, review_cycle: review_cycle, employee: create(:employee, account: account)) }
    let!(:reviewer) do
      create(:reviewer, review_cycle: review_cycle, reviewee: reviewee,
                        reviewer_type: reviewer_type_record, employee: reviewer_employee)
    end
    let!(:notification) do
      create(:review_cycle_notification,
             review_cycle: review_cycle,
             review_cycle_phase: phase,
             task_type: 'launch',
             channel: 'email',
             task_executed_at: 1.hour.ago)
    end
    let!(:notification_recipient) do
      create(:review_cycle_notification_recipient,
             review_cycle: review_cycle,
             review_cycle_phase: phase,
             review_cycle_notification: notification,
             employee: reviewer_employee)
    end

    it 'returns true when phase is launched and notification received' do
      service = described_class.call(
        review_cycle_id: review_cycle.id,
        reviewer_employee_id: reviewer_employee.id,
        target_reviewer_type: reviewer_type_name,
      )

      expect(service).to be_success
      expect(service.result).to be true
    end
  end

  shared_examples 'notification edge cases' do
    before { phase.update!(start_time: 1.day.ago) }

    let!(:reviewee) { create(:reviewee, review_cycle: review_cycle, employee: create(:employee, account: account)) }
    let!(:reviewer) do
      create(:reviewer, review_cycle: review_cycle, reviewee: reviewee,
                        reviewer_type: reviewer_type_record, employee: reviewer_employee)
    end

    context 'when notification exists but was not executed' do
      let!(:notification) do
        create(:review_cycle_notification,
               review_cycle: review_cycle,
               review_cycle_phase: phase,
               task_type: 'launch',
               channel: 'email',
               task_executed_at: nil) # not executed
      end
      let!(:notification_recipient) do
        create(:review_cycle_notification_recipient,
               review_cycle: review_cycle,
               review_cycle_phase: phase,
               review_cycle_notification: notification,
               employee: reviewer_employee)
      end

      it 'returns false because unexecuted notifications do not count' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: reviewer_type_name,
        )

        expect(service).to be_success
        expect(service.result).to be false
      end
    end

    context 'when employee is not a reviewer in the review cycle' do
      let!(:other_employee) { create(:employee, account: account) }

      it 'returns false because employee is not a reviewer' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: other_employee.id,
          target_reviewer_type: reviewer_type_name,
        )

        expect(service).to be_success
        expect(service.result).to be false
      end
    end
  end

  describe '#call' do
    context 'with invalid inputs' do
      it 'returns false when review_cycle_id is nil' do
        service = described_class.call(
          review_cycle_id: nil,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: 'manager',
        )

        expect(service).not_to be_success
        expect(service.result).to be false
        expect(service.errors[:review_cycle_id]).to include('Review cycle ID is required')
      end

      it 'returns false when reviewer_employee_id is nil' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: nil,
          target_reviewer_type: 'manager',
        )

        expect(service).not_to be_success
        expect(service.result).to be false
        expect(service.errors[:reviewer_employee_id]).to include('Reviewer employee ID is required')
      end

      it 'returns false when target_reviewer_type is nil' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: nil,
        )

        expect(service).not_to be_success
        expect(service.result).to be false
        expect(service.errors[:target_reviewer_type]).to include('Target reviewer type is required')
      end
    end

    context 'when review cycle does not exist' do
      it 'returns false' do
        service = described_class.call(
          review_cycle_id: 999_999,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: 'manager',
        )

        expect(service).to be_success
        expect(service.result).to be false
      end
    end

    context 'when reviewer employee does not exist' do
      it 'returns false' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: 999_999,
          target_reviewer_type: 'manager',
        )

        expect(service).to be_success
        expect(service.result).to be false
      end
    end

    context 'when reviewer type does not exist in review cycle' do
      it 'returns false' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: 'nonexistent_type',
        )

        expect(service).to be_success
        expect(service.result).to be false
      end
    end

    context 'for manager reviewer type' do
      let(:reviewer_type_name) { 'manager' }
      let!(:reviewer_type_record) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'manager') }

      context 'when manager_summary phase does not exist' do
        it 'returns false' do
          service = described_class.call(
            review_cycle_id: review_cycle.id,
            reviewer_employee_id: reviewer_employee.id,
            target_reviewer_type: 'manager',
          )

          expect(service).to be_success
          expect(service.result).to be false
        end
      end

      context 'when manager_summary phase exists' do
        let!(:phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'manager_summary')
        end

        context 'when phase is not launched (start_time is nil)' do
          before { phase.update!(start_time: nil) }
          include_examples 'phase not launched'
        end

        context 'when phase is not launched (start_time is in future)' do
          before { phase.update!(start_time: 1.day.from_now) }
          include_examples 'phase not launched'
        end

        context 'when phase is launched (start_time is in past)' do
          context 'when employee has not received notification' do
            include_examples 'phase launched without notification'
          end

          context 'when employee has received notification' do
            include_examples 'phase launched with notification'
          end
        end

        context 'when phase is launched (start_time is now)' do
          include_examples 'phase launched with notification', Time.current
        end

        include_examples 'notification edge cases'
      end
    end

    context 'for direct_report reviewer type' do
      let(:reviewer_type_name) { 'direct_report' }
      let!(:reviewer_type_record) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'direct_report') }

      context 'when specific write_reviews phase exists for direct_report' do
        let!(:phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'write_reviews',
                 reviewer_type: reviewer_type_record,
                 start_time: nil)
        end

        context 'when phase is not launched' do
          include_examples 'phase not launched'
        end

        context 'when phase is launched' do
          context 'when employee has not received notification' do
            include_examples 'phase launched without notification'
          end

          context 'when employee has received notification' do
            include_examples 'phase launched with notification'
          end
        end

        include_examples 'notification edge cases'
      end

      context 'when only general write_reviews phase exists (reviewer_type is nil)' do
        let!(:phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'write_reviews',
                 reviewer_type: nil,
                 start_time: nil)
        end

        context 'when phase is not launched' do
          include_examples 'phase not launched'
        end

        context 'when phase is launched' do
          context 'when employee has not received notification' do
            include_examples 'phase launched without notification'
          end

          context 'when employee has received notification' do
            include_examples 'phase launched with notification'
          end
        end

        include_examples 'notification edge cases'
      end

      context 'when both specific and general write_reviews phases exist' do
        let!(:specific_phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'write_reviews',
                 reviewer_type: reviewer_type_record,
                 start_time: 1.day.ago)
        end
        let!(:general_phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'write_reviews',
                 reviewer_type: nil,
                 start_time: 2.days.ago)
        end
        let(:phase) { specific_phase } # Should prioritize specific phase

        context 'when specific phase is launched and notification received' do
          include_examples 'phase launched with notification'
        end
      end
    end
  end
end
