# frozen_string_literal: true

require 'rails_helper'

describe ManagerChanges::CreateNormalPerfReviewRct, type: :service do
  let!(:account) { create(:account, :with_employees) }
  let!(:admin_employee) { account.admins.first }
  let!(:employee) { account.employees.employee.first }
  let!(:old_manager) { create(:employee, account: account) }
  let!(:new_manager) { create(:employee, account: account) }

  let!(:review_cycle) do
    create(:review_cycle, 
           account: account, 
           creator: admin_employee,
           review_type: 'performance_review')
  end
  
  let!(:reviewee) { create(:reviewee, review_cycle: review_cycle, employee: employee) }
  let!(:old_reviewer_type) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'manager') }
  let!(:new_reviewer_type) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'former_manager') }
  let!(:old_reviewer) { create(:reviewer, review_cycle: review_cycle, reviewee: reviewee, reviewer_type: old_reviewer_type, employee: old_manager) }

  describe '#call' do
    context 'when old_reviewer has a reviewee snapshot and matching old template' do
      let!(:reviewee_snapshot) do
        create(:reviewee_snapshot,
               reviewee: reviewee,
               manager: old_manager,
               reviewer: old_reviewer,
               custom_attribute: 'senior_engineer',
               reason: 'manager_change')
      end

      let!(:old_review_cycle_template) do
        create(:review_cycle_template,
               reviewer_type: old_reviewer_type,
               employee_attribute: 'senior_engineer')
      end

      let!(:template_question) do
        create(:review_cycle_template_question,
               review_cycle_template: old_review_cycle_template)
      end

      before do
        # Set the reviewee's custom_attribute to match the template
        reviewee.update!(custom_attribute: 'senior_engineer')
      end

      it 'extracts custom_attribute from snapshot, finds old template, and creates new template with questions using ReviewCycleTemplateQuestions::Create' do
        # Mock the ReviewCycleTemplateQuestions::Create service
        mock_service = double('ReviewCycleTemplateQuestions::Create')
        mock_result = double('Result', id: 123)
        allow(mock_service).to receive(:success?).and_return(true)
        allow(mock_service).to receive(:result).and_return(mock_result)

        expect(ReviewCycleTemplateQuestions::Create).to receive(:call).and_return(mock_service)

        service = described_class.call(
          review_cycle,
          old_reviewer_type.id,
          new_reviewer_type,
          old_reviewer
        )

        expect(service).to be_success
        expect(service.result[:review_cycle_template_created]).to be true
        expect(service.result[:review_cycle_template_id]).to be_present
        expect(service.result[:old_template_found]).to be true
        expect(service.result[:old_template_id]).to eq(old_review_cycle_template.id)
        expect(service.result[:created_count]).to eq(1)
        expect(service.result[:updated_responses_count]).to eq(0)

        # Verify the review cycle template was created with the correct employee_attribute
        created_template = ReviewCycleTemplate.find(service.result[:review_cycle_template_id])
        expect(created_template.reviewer_type).to eq(new_reviewer_type)
        expect(created_template.employee_attribute).to eq('senior_engineer')
      end

      context 'with existing review responses' do
        let!(:question) { create(:question) }
        let!(:option) { create(:option, question: question, value: 'Excellent') }
        let!(:review_response) do
          create(:review_response,
                 reviewer: old_reviewer,
                 review_cycle_template_question: template_question,
                 question: question,
                 option: option,
                 response_text: 'Great work!')
        end

        before do
          template_question.update!(question: question, block_type: 'question')
        end

        it 'updates existing review responses to point to new template question' do
          # Mock the ReviewCycleTemplateQuestions::Create service
          new_question = create(:question)
          new_option = create(:option, question: new_question, value: 'Excellent')
          mock_service = double('ReviewCycleTemplateQuestions::Create')
          mock_result = double('Result', id: 999, question: new_question)
          allow(mock_service).to receive(:success?).and_return(true)
          allow(mock_service).to receive(:result).and_return(mock_result)

          expect(ReviewCycleTemplateQuestions::Create).to receive(:call).and_return(mock_service)

          service = described_class.call(
            review_cycle,
            old_reviewer_type.id,
            new_reviewer_type,
            old_reviewer
          )

          expect(service).to be_success
          expect(service.result[:updated_responses_count]).to eq(1)

          # Verify the review response was updated
          review_response.reload
          expect(review_response.review_cycle_template_question_id).to eq(999)
          expect(review_response.question_id).to eq(new_question.id)
          expect(review_response.option_id).to eq(new_option.id)
        end
      end

      context 'with question block type' do
        before do
          template_question.update!(block_type: 'question')
        end

        it 'prepares correct params with question_attributes for question block' do
          expected_params = {
            review_cycle_template_question: {
              block_type: 'question',
              question_id: template_question.question_id,
              content: template_question.content,
              show_goal_progress: template_question.show_goal_progress,
              question_access: template_question.question_access,
              position: template_question.position,
              formula: template_question.formula,
              default_weights: template_question.default_weights || {},
              goal_fields: template_question.goal_fields || {},
              review_cycle_template_id: kind_of(Integer),
              question_attributes: kind_of(Hash)
            }
          }

          expect(ReviewCycleTemplateQuestions::Create).to receive(:call) do |params|
            expect(params).to match(expected_params)
            expect(params[:review_cycle_template_question][:question_attributes]).to be_present
            double('Result', success?: true, result: double(id: 123))
          end

          described_class.call(
            review_cycle,
            old_reviewer_type.id,
            new_reviewer_type,
            old_reviewer
          )
        end
      end

      context 'with goals block type' do
        let!(:goals_template_question) do
          create(:review_cycle_template_question,
                 review_cycle_template: old_review_cycle_template,
                 block_type: 'goals',
                 question: nil)
        end

        it 'prepares correct params without question_attributes for goals block' do
          expected_params = {
            review_cycle_template_question: {
              block_type: 'goals',
              question_id: nil,
              content: goals_template_question.content,
              show_goal_progress: goals_template_question.show_goal_progress,
              question_access: goals_template_question.question_access,
              position: goals_template_question.position,
              formula: goals_template_question.formula,
              default_weights: goals_template_question.default_weights || {},
              goal_fields: goals_template_question.goal_fields || {},
              review_cycle_template_id: kind_of(Integer)
            }
          }

          expect(ReviewCycleTemplateQuestions::Create).to receive(:call) do |params|
            expect(params).to match(expected_params)
            expect(params[:review_cycle_template_question]).not_to have_key(:question_attributes)
            double('Result', success?: true, result: double(id: 124))
          end

          described_class.call(
            review_cycle,
            old_reviewer_type.id,
            new_reviewer_type,
            old_reviewer
          )
        end

        context 'with existing goals review responses' do
          let!(:goals_review_response) do
            create(:review_response,
                   reviewer: old_reviewer,
                   review_cycle_template_question: goals_template_question,
                   question_id: 456,  # Some existing question_id
                   option_id: 789,    # Some existing option_id
                   response_text: 'Goal progress update')
          end

          it 'only updates review_cycle_template_question_id for goals responses' do
            # Mock the ReviewCycleTemplateQuestions::Create service
            mock_service = double('ReviewCycleTemplateQuestions::Create')
            mock_result = double('Result', id: 999, question: nil)
            allow(mock_service).to receive(:success?).and_return(true)
            allow(mock_service).to receive(:result).and_return(mock_result)

            expect(ReviewCycleTemplateQuestions::Create).to receive(:call).and_return(mock_service)

            service = described_class.call(
              review_cycle,
              old_reviewer_type.id,
              new_reviewer_type,
              old_reviewer
            )

            expect(service).to be_success
            expect(service.result[:updated_responses_count]).to eq(1)

            # Verify only review_cycle_template_question_id was updated
            goals_review_response.reload
            expect(goals_review_response.review_cycle_template_question_id).to eq(999)
            expect(goals_review_response.question_id).to eq(456)  # Should remain unchanged
            expect(goals_review_response.option_id).to eq(789)    # Should remain unchanged
          end
        end
      end

      context 'with competency block type' do
        before do
          template_question.update!(block_type: 'competency')
        end

        it 'prepares correct params with question_attributes for competency block' do
          expected_params = {
            review_cycle_template_question: {
              block_type: 'competency',
              question_id: template_question.question_id,
              content: template_question.content,
              show_goal_progress: template_question.show_goal_progress,
              question_access: template_question.question_access,
              position: template_question.position,
              formula: template_question.formula,
              default_weights: template_question.default_weights || {},
              goal_fields: template_question.goal_fields || {},
              review_cycle_template_id: kind_of(Integer),
              question_attributes: kind_of(Hash)
            }
          }

          expect(ReviewCycleTemplateQuestions::Create).to receive(:call) do |params|
            expect(params).to match(expected_params)
            expect(params[:review_cycle_template_question][:question_attributes]).to be_present
            double('Result', success?: true, result: double(id: 125))
          end

          described_class.call(
            review_cycle,
            old_reviewer_type.id,
            new_reviewer_type,
            old_reviewer
          )
        end
      end
    end

    context 'when old_reviewer has no reviewee snapshot' do
      it 'creates review cycle template with nil employee_attribute' do
        service = described_class.call(
          review_cycle,
          old_reviewer_type.id,
          new_reviewer_type,
          old_reviewer
        )

        expect(service).to be_success
        expect(service.result[:review_cycle_template_created]).to be true
        expect(service.result[:review_cycle_template_id]).to be_present
        expect(service.result[:old_template_found]).to be false

        # Verify the review cycle template was created with nil employee_attribute
        created_template = ReviewCycleTemplate.find(service.result[:review_cycle_template_id])
        expect(created_template.reviewer_type).to eq(new_reviewer_type)
        expect(created_template.employee_attribute).to be_nil
      end
    end

    context 'when old_reviewer is not provided' do
      it 'creates review cycle template with nil employee_attribute' do
        service = described_class.call(
          review_cycle,
          old_reviewer_type.id,
          new_reviewer_type,
          nil
        )

        expect(service).to be_success
        expect(service.result[:review_cycle_template_created]).to be true
        expect(service.result[:review_cycle_template_id]).to be_present
        expect(service.result[:old_template_found]).to be false

        # Verify the review cycle template was created with nil employee_attribute
        created_template = ReviewCycleTemplate.find(service.result[:review_cycle_template_id])
        expect(created_template.reviewer_type).to eq(new_reviewer_type)
        expect(created_template.employee_attribute).to be_nil
      end
    end

    context 'when review cycle template questions exist' do
      let!(:old_review_cycle_template) { create(:review_cycle_template, reviewer_type: old_reviewer_type) }
      let!(:template_question) do
        create(:review_cycle_template_question,
               review_cycle_template: old_review_cycle_template)
      end

      it 'duplicates template questions for the new reviewer type' do
        initial_question_count = review_cycle.review_cycle_template_questions.count

        service = described_class.call(
          review_cycle,
          old_reviewer_type.id,
          new_reviewer_type,
          old_reviewer
        )

        expect(service).to be_success
        expect(service.result[:created_count]).to eq(1)
        expect(review_cycle.review_cycle_template_questions.count).to eq(initial_question_count + 1)

        # Verify the new question was created for the new reviewer type
        new_template = new_reviewer_type.review_cycle_templates.first
        expect(new_template).to be_present
        new_question = new_template.review_cycle_template_questions.first
        expect(new_question).to be_present
        expect(new_question.review_cycle_template.reviewer_type).to eq(new_reviewer_type)
      end
    end
  end
end
