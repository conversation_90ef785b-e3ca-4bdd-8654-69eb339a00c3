# frozen_string_literal: true

require 'rails_helper'

describe ManagerChanges::SendReviewerPhaseSilentNotification, type: :service do
  let!(:account) { create(:account, :with_employees) }
  let!(:review_cycle) { create(:review_cycle, account: account) }
  let!(:reviewer_employee) { account.employees.first }

  # Shared examples for common test scenarios
  shared_examples 'creates silent notification successfully' do
    it 'creates a silent notification and recipient' do
      expect {
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: reviewer_type_name,
        )
        expect(service).to be_success
        expect(service.result).to be true
      }.to change { ReviewCycleNotification.count }.by(1)
        .and change { ReviewCycleNotificationRecipient.count }.by(1)

      notification = ReviewCycleNotification.last
      expect(notification.channel).to eq('silent_launch')
      expect(notification.task_type).to eq('launch')
      expect(notification.review_cycle).to eq(review_cycle)
      expect(notification.review_cycle_phase).to eq(phase)
      expect(notification.recipients_type).to be_nil
      expect(notification.creator_id).to be_nil

      recipient = ReviewCycleNotificationRecipient.last
      expect(recipient.employee).to eq(reviewer_employee)
      expect(recipient.review_cycle_notification).to eq(notification)
      expect(recipient.review_cycle_phase).to eq(phase)
    end

    it 'calls the CreateReviewCyclePhaseNotifications service' do
      expect(PerformanceReview::CreateReviewCyclePhaseNotifications).to receive(:call)
        .with(
          review_cycle.id,
          phase.id,
          {
            participants: [reviewer_employee.id],
            channel: 'silent_launch',
            schedule: 'now',
            task_type: 'launch',
            participants_type: nil,
            current_user: nil,
          },
        )
        .and_return(true)

      service = described_class.call(
        review_cycle_id: review_cycle.id,
        reviewer_employee_id: reviewer_employee.id,
        target_reviewer_type: reviewer_type_name,
      )

      expect(service).to be_success
      expect(service.result).to be true
    end
  end

  shared_examples 'returns false when phase not found' do
    it 'returns false when phase does not exist' do
      service = described_class.call(
        review_cycle_id: review_cycle.id,
        reviewer_employee_id: reviewer_employee.id,
        target_reviewer_type: reviewer_type_name,
      )

      expect(service).to be_success
      expect(service.result).to be false
    end
  end

  describe '#call' do
    context 'with invalid inputs' do
      it 'returns false when review_cycle_id is nil' do
        service = described_class.call(
          review_cycle_id: nil,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: 'manager',
        )

        expect(service).not_to be_success
        expect(service.result).to be false
        expect(service.errors[:review_cycle_id]).to include('Review cycle ID is required')
      end

      it 'returns false when reviewer_employee_id is nil' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: nil,
          target_reviewer_type: 'manager',
        )

        expect(service).not_to be_success
        expect(service.result).to be false
        expect(service.errors[:reviewer_employee_id]).to include('Reviewer employee ID is required')
      end

      it 'returns false when target_reviewer_type is nil' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: nil,
        )

        expect(service).not_to be_success
        expect(service.result).to be false
        expect(service.errors[:target_reviewer_type]).to include('Target reviewer type is required')
      end
    end

    context 'when review cycle does not exist' do
      it 'returns false' do
        service = described_class.call(
          review_cycle_id: 999_999,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: 'manager',
        )

        expect(service).to be_success
        expect(service.result).to be false
      end
    end

    context 'when reviewer employee does not exist' do
      it 'returns false' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: 999_999,
          target_reviewer_type: 'manager',
        )

        expect(service).to be_success
        expect(service.result).to be false
      end
    end

    context 'when reviewer type does not exist in review cycle' do
      it 'returns false' do
        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: 'nonexistent_type',
        )

        expect(service).to be_success
        expect(service.result).to be false
      end
    end

    context 'for manager reviewer type' do
      let(:reviewer_type_name) { 'manager' }
      let!(:reviewer_type_record) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'manager') }

      context 'when manager_summary phase does not exist' do
        include_examples 'returns false when phase not found'
      end

      context 'when manager_summary phase exists' do
        let!(:phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'manager_summary')
        end

        include_examples 'creates silent notification successfully'
      end
    end

    context 'for direct_report reviewer type' do
      let(:reviewer_type_name) { 'direct_report' }
      let!(:reviewer_type_record) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'direct_report') }

      context 'when no write_reviews phase exists' do
        include_examples 'returns false when phase not found'
      end

      context 'when specific write_reviews phase exists for direct_report' do
        let!(:phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'write_reviews',
                 reviewer_type: reviewer_type_record)
        end

        include_examples 'creates silent notification successfully'
      end

      context 'when only general write_reviews phase exists (reviewer_type is nil)' do
        let!(:phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'write_reviews',
                 reviewer_type: nil)
        end

        include_examples 'creates silent notification successfully'
      end

      context 'when both specific and general write_reviews phases exist' do
        let!(:specific_phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'write_reviews',
                 reviewer_type: reviewer_type_record)
        end
        let!(:general_phase) do
          create(:review_cycle_phase,
                 review_cycle: review_cycle,
                 phase_type: 'write_reviews',
                 reviewer_type: nil)
        end
        let(:phase) { specific_phase } # Should prioritize specific phase

        include_examples 'creates silent notification successfully'
      end
    end

    context 'error handling' do
      let!(:reviewer_type_record) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'manager') }
      let!(:phase) do
        create(:review_cycle_phase,
               review_cycle: review_cycle,
               phase_type: 'manager_summary')
      end

      it 'handles service errors gracefully' do
        allow(PerformanceReview::CreateReviewCyclePhaseNotifications).to receive(:call)
          .and_raise(StandardError.new('Service error'))

        service = described_class.call(
          review_cycle_id: review_cycle.id,
          reviewer_employee_id: reviewer_employee.id,
          target_reviewer_type: 'manager',
        )

        expect(service).not_to be_success
        expect(service.result).to be false
        expect(service.errors[:base]).to include('Failed to send silent notification: Service error')
      end
    end
  end
end
