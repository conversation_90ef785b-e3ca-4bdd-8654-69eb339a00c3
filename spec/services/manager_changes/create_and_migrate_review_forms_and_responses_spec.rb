# frozen_string_literal: true

require 'rails_helper'

describe ManagerChanges::CreateAndMigrateReviewFormsAndResponses, type: :service do
  let!(:account) { create(:account, :with_employees) }
  let!(:admin_employee) { account.admins.first }
  let!(:employee) { account.employees.employee.first }
  let!(:old_manager) { create(:employee, account: account) }
  let!(:new_manager) { create(:employee, account: account) }

  describe '#call' do
    context 'with performance_review type' do
      let!(:review_cycle) do
        create(:review_cycle, 
               account: account, 
               creator: admin_employee,
               review_type: 'performance_review')
      end
      
      let!(:reviewee) { create(:reviewee, review_cycle: review_cycle, employee: employee) }
      let!(:old_reviewer_type) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'manager') }
      let!(:new_reviewer_type) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'former_manager') }
      let!(:old_reviewer) { create(:reviewer, review_cycle: review_cycle, reviewee: reviewee, reviewer_type: old_reviewer_type, employee: old_manager) }

      it 'uses CreateNormalPerfReviewRct service' do
        expect(ManagerChanges::CreateNormalPerfReviewRct).to receive(:call)
          .with(review_cycle, old_reviewer_type.id, new_reviewer_type, old_reviewer)
          .and_return(double(success?: true, result: { created_count: 1, errors: [], review_cycle_template_created: true, old_template_found: true, updated_responses_count: 0 }))

        service = described_class.call(
          reviewee_id: reviewee.id,
          old_reviewer_id: old_reviewer.id,
          new_reviewer_type_id: new_reviewer_type.id,
          old_reviewer_type_id: old_reviewer_type.id
        )

        expect(service).to be_success
      end
    end

    context 'with three_sixty_review type' do
      let!(:review_cycle) do
        create(:review_cycle, 
               account: account, 
               creator: admin_employee,
               review_type: 'three_sixty_review')
      end
      
      let!(:reviewee) { create(:reviewee, review_cycle: review_cycle, employee: employee) }
      let!(:old_reviewer_type) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'manager') }
      let!(:new_reviewer_type) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'former_manager') }
      let!(:old_reviewer) { create(:reviewer, review_cycle: review_cycle, reviewee: reviewee, reviewer_type: old_reviewer_type, employee: old_manager) }

      it 'uses CreateThreeSixtyPerfReviewTemplate service' do
        expect(ManagerChanges::CreateThreeSixtyPerfReviewTemplate).to receive(:call)
          .with(review_cycle, old_reviewer_type.id, new_reviewer_type, old_reviewer)
          .and_return(double(success?: true, result: { created_count: 1, errors: [] }))

        service = described_class.call(
          reviewee_id: reviewee.id,
          old_reviewer_id: old_reviewer.id,
          new_reviewer_type_id: new_reviewer_type.id,
          old_reviewer_type_id: old_reviewer_type.id
        )

        expect(service).to be_success
      end
    end

    context 'with unsupported review type' do
      let!(:review_cycle) do
        create(:review_cycle, 
               account: account, 
               creator: admin_employee)
      end
      
      let!(:reviewee) { create(:reviewee, review_cycle: review_cycle, employee: employee) }
      let!(:old_reviewer_type) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'manager') }
      let!(:new_reviewer_type) { create(:reviewer_type, review_cycle: review_cycle, reviewer_type: 'former_manager') }
      let!(:old_reviewer) { create(:reviewer, review_cycle: review_cycle, reviewee: reviewee, reviewer_type: old_reviewer_type, employee: old_manager) }

      before do
        # Mock an unsupported review type
        allow(review_cycle).to receive(:performance_review?).and_return(false)
        allow(review_cycle).to receive(:three_sixty_review?).and_return(false)
        allow(review_cycle).to receive(:review_type).and_return('unsupported_type')
      end

      it 'returns error for unsupported review type' do
        service = described_class.call(
          reviewee_id: reviewee.id,
          old_reviewer_id: old_reviewer.id,
          new_reviewer_type_id: new_reviewer_type.id,
          old_reviewer_type_id: old_reviewer_type.id
        )

        expect(service).to be_success
        expect(service.result[:errors]).to include('Unsupported review cycle type: unsupported_type')
        expect(service.result[:forms_created]).to eq(0)
      end
    end
  end
end
