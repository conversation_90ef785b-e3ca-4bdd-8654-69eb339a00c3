system_employee_attributes:
  - email_address
  - phone_number
  - profile_link
  - department
  - joining_date
  - location
  - division
  - business_unit
  - level
elements:
  email_address:
    attr_type: 'system'
    title: 'Email Address'
    value_type: 'string'
    position: 1
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self']
      viewer_type: 'flexible'
    enabled: true
    copyable: true
  phone_number:
    attr_type: 'system'
    title: 'Phone Number'
    value_type: 'number'
    position: 2
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self']
      viewer_type: 'flexible'
    enabled: true
    copyable: true
  profile_link:
    attr_type: 'system'
    title: 'Profile Link'
    value_type: 'link'
    position: 3
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['all_employees']
      viewer_type: 'flexible'
    enabled: true
    copyable: false
  department:
    attr_type: 'system'
    title: 'Department'
    value_type: 'string'
    position: 4
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['all_employees']
      viewer_type: 'flexible'
    enabled: true
    copyable: false
  joining_date:
    attr_type: 'system'
    title: 'Joining Date'
    value_type: 'date'
    position: 5
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['all_employees']
      viewer_type: 'flexible'
    enabled: true
    copyable: false
  location:
    attr_type: 'system'
    title: 'Location'
    value_type: 'string'
    position: 6
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['all_employees']
      viewer_type: 'flexible'
    enabled: true
    copyable: false
  division:
    attr_type: 'system'
    title: 'Division'
    value_type: 'string'
    position: 7
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['all_employees']
      viewer_type: 'flexible'
    enabled: true
    copyable: false
  business_unit:
    attr_type: 'system'
    title: 'Business Unit'
    value_type: 'string'
    position: 8
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['all_employees']
      viewer_type: 'flexible'
    enabled: true
    copyable: false
  level:
    attr_type: 'system'
    title: 'Level'
    value_type: 'string'
    position: 9
    fixed_position: false
    role_access_config:
      possible_viewer_roles: ['admins', 'managers', 'indirect_managers', 'self', 'all_employees']
      allowed_viewer_roles: ['all_employees']
      viewer_type: 'flexible'
    enabled: true
    copyable: false
