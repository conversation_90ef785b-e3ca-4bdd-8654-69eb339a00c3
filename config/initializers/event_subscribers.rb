# frozen_string_literal: true
# Immediate synchronous execution via your event_store.subscribe 
require Rails.root.join('app/events/handlers/base_handler')
require Rails.root.join('app/events/definitions/base_event.rb')
Dir[Rails.root.join('app/events/**/*.rb')].each { |f| require f }
Dir[Rails.root.join('app/events/definitions/**/*.rb')].each { |f| require f }
Dir[Rails.root.join('app/events/handlers/**/*.rb')].each { |f| require f }
Dir[Rails.root.join('app/event_store/**/*.rb')].each { |f| require f }
