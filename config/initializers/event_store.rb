# config/initializers/event_store.rb
Rails.application.config.to_prepare do
  require 'ruby_event_store'
  require 'rails_event_store'
  require Rails.root.join('app/event_store/mappers/symbolized_custom_mapper.rb')

  Rails.configuration.event_store = RailsEventStore::Client.new(
    mapper: EventStore::Mappers::SymbolizedCustomMapper.new,
    repository: RailsEventStoreActiveRecord::EventRepository.new(
      serializer: JSON,
    ),
  )
end