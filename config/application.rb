require_relative 'boot'

require "rails"
# Pick the frameworks you want:
require "active_model/railtie"
require "active_job/railtie"
require "active_record/railtie"
require "active_storage/engine"
require "action_controller/railtie"
require "action_mailer/railtie"
require "action_view/railtie"
require "action_cable/engine"
require "sprockets/railtie"
require "rails/test_unit/railtie"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module CulturegradeApi
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.railties_order = [AhoyEmail::Engine, :all]
    config.load_defaults 5.2
    config.cache_store = :memory_store, { size: 32.megabytes }
    config.autoload_paths << Rails.root.join('lib')
    config.autoload_paths.push(*Dir[ Rails.root.join('app', 'services', '**/') ])
    config.eager_load_paths << Rails.root.join('lib')
    config.autoload_paths += %W(#{config.root}/lib/jobs)
    config.autoload_paths += %W(#{config.root}/app/models/simple_chat/employee_checkin)
    config.autoload_paths += %W(#{config.root}/app/models/simple_chat/)
    config.autoload_paths += %W(#{config.root}/app/models/simple_chat/employee_one_on_one)
    config.autoload_paths += %W(#{config.root}/app/models/simple_chat/manager_feedback)
    config.autoload_paths.push(*Dir[ Rails.root.join('app', 'events', '**/') ])
    config.eager_load_paths.push(*Dir[ Rails.root.join('app', 'events', '**/') ])
    config.autoload_paths.push(*Dir[ Rails.root.join('app', 'event_store', '**/') ])
    config.eager_load_paths.push(*Dir[ Rails.root.join('app', 'event_store', '**/') ])

    # config.time_zone = 'Mumbai'
    # config.active_record.default_timezone = :utc

    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration can go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded after loading
    # the framework and any gems in your application.

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    config.api_only = true

    # Middleware for active admin
    config.middleware.use Rack::MethodOverride
    config.middleware.use ActionDispatch::Flash
    config.middleware.use ActionDispatch::Cookies
    config.middleware.use ActionDispatch::Session::CookieStore
  end
end
