Rails.application.routes.draw do
  # ActiveAdmin.routes(self)

  get '/verification', to: redirect('/googled6d26053080c73a1.html')

  resources :attachments, only: [:create, :destroy]

  resources :surveys do
    resources :questions
  end

  resources :messages, only: [:incoming] do
    collection do
      post :incoming
    end
  end

  resources :employee_chats do
    get :redirect_to_survey_page
    resources :bot, only: [:create, :index] do
      collection do
        get :create_thread_from_email
        get :employee_insight_share
        get :anonymity_video_redirect
      end
    end
    resources :questions do
      resources :responses, only: [:create, :update, :show] do
        collection do
          get :create
        end
      end
    end
    resources :employee_satisfaction, only: [:create]
  end

  resources :manager_chats do
    resources :manager_simple_chat, only: [:create, :index]
  end

  resources :manager_simple_chat do
    collection do
      get :start_chat
    end
  end

  resources :account_products, only: [:index, :create, :destroy]

  resources :accounts do
    resources :connected_workspace_user, only: [:show]
    resources :connected_workspace_user_map, only: [:show]

    resources :account_settings, only: [:update]
    resource :profile_configuration, only: [:update, :show]
    resources :employee_attributes, only: [:index] do
      collection do
        put :bulk_update
      end
    end

    resources :account_products, param: :product_key do
      member do
        put :toggle_product_access, to: 'appsmith#toggle_product_access'
      end
    end

    resources :goals, only: [] do
      member do
        post :add_employee_to_goal, to: 'appsmith#add_employee_to_goal'
      end
    end

    resources :feedback_templates, only: [] do
      collection do
        get :select_list
      end
    end
    resources :feedbacks, only: %i[create update show] do 
      member do
        put :submit
      end
      resources :feedback_receivers, only: [:create, :update]
      resources :feedback_providers, only: [:create, :destroy]
      resources :feedback_responses, only: [:create]
      resources :feedback_requests, only: [:destroy]
    end

    resources :feedback_providers, only: [] do
      resources :feedback_responses, only: [] do
        member do
          post :write_with_ai
        end
      end
    end

    member do
      get :manager_list
      get :filters
      get :department_list
      get :active_subscription
      get :hris_importer
      get :employees_data
      get :org_chart
      delete :remove_messaging_client
      post :invite_users
      get :export_all_data
      put :change_planning_year
      post :sync_analytics_data
      get :products
    end

    namespace :analytics do
      resources :org_chart, only: [] do
        collection do
          get :employees
          get :missing_employees
          get :details
        end
      end
    end

    collection do
      get :onboarding_details
    end
    resources :homepage_analytics, only: [] do
      collection do
        get :get_user_homepage_config
        get :get_homepage_insight
      end
    end
    resources :analytics_dashboard, only: [] do
      collection do
        get :get_analytics_config
        get :get_analytics_data
      end
    end
    resources :coffee_connect, only: [] do
      collection do
        get :stats
        get :activities
        get :channels
        get :initiate_coffee_connect
      end
    end
    resources :questions do
      collection do
        post :create_custom_question
      end
      member do
        get :get_question_tree
        get :get_question_tree_view
        post  :question_update
        post  :option_followup_update
        post  :delete_options
        put :edit_custom_question
      end
    end
    resources :coffee_connect_settings, only: [:create, :update, :show]
    resources :template_categories, only: [:index]
    resources :goal_cycles, only: [:index] do
      collection do
        get :groups
      end
    end
    resources :departments do
      member do
        put :archive_department
      end
    end
    resources :biz_reviews, only: [:index, :create, :destroy] do
      get :my_biz_reviews, on: :collection
      get :biz_review_listing, on: :collection
      get :my_upcoming_reviews, on: :collection
      get :all_upcoming_reviews, on: :collection
      resources :biz_review_schedules, only: [:show, :update, :create, :destroy] do
        member do
          get :generate_pdf
          get :configs
          get :past_reviews
        end
        resources :biz_review_cards, only: [:index, :show, :create, :destroy, :update] do
          collection do
            put :update_layout
            get :presentation_mode
          end
          member do
            get :menu_configs
          end
          resources :biz_review_notes, only: [:update, :show] do
            member do
              get :versions
            end
          end
          resources :biz_review_action_items, only: [:create, :update, :destroy]
          resources :biz_review_comments, only: [:index, :create, :destroy]
        end
      end
    end
    resources :metadata_fields
    resources :metadata_values, only: [:destroy] do
      collection do
        post :create_or_update_metadata_values
      end
    end

    namespace :employees do
      resources :super_admins, only: [:index, :create]
    end

    namespace :performance_reviews do
      resources :employees, only: [:index] do
        collection do
          resources :filters, only: [:index]
        end
      end
    end

    resources :employees do
      member do
        # post :invite
        get :glassdoor_profile
        get :reportee_list
        put :update_employees
        put :update_billing_status
        post :create_bulk_employees
        put :upload_profile_picture
        post :create_custom_homepage_actions
      end
      collection do
        get :product_feedback_form
        get :get_nps_keys
        get :nps_product_feedback_form
        get :slack_mapping
        put :resync_slack_mapping
        get :ms_teams_mapping
        put :update_employee_question_status
        put :update_product_feedback
        post :submit_product_feedback
        get :list_employees
        get :select_list
      end
      resources :feedbacks, only: [:index]
      resources :feedback_requests, only: [:index]
      resources :integration_configs, only: [:index, :create, :update]
      resources :one_on_ones do
        member do
          put :update_completion_status
          get :show
          get :show_index
        end
        collection do
          # get :reasons_for_one_on_one
        end
        resources :one_on_one_notes do
          collection do
            get :create_items_from_email
          end
          member do
            put :update_agendas_and_action_items
            get :markdown
            get :hypertext
          end
        end
      end
      # resources :onboarding
      # resources :one_on_one_agendas do
      #   collection do
      #     get :suggested_items
      #     get :create_talking_point_from_email
      #   end
      # end
      resources :super_list_items do
        collection do
          get :followup_items
        end
      end
      resources :one_on_one_agenda_templates, only: [:index, :create, :update, :destroy]
      resources :suggested_talking_point_categories, only: [:index, :create, :update, :destroy]
      # resources :upcoming_activities, only: [:index]

      namespace :analytics do
        resources :goals, only: [:index]
      end

      namespace :profile do
        resource :profile_configuration, only: [:show]
        resources :employee_attributes, only: [:index]
        resources :breadcrumbs, only: [:index]
        resources :reviews, only: [:index] do
          get :goals, on: :member
          collection do
            get :auto_calculated_scores
            get :review_cycles_with_goals
            post :reviewer_type_tabs
          end
        end
        resources :one_on_ones, only: [:index] do
          get :all_participants, on: :collection
        end
        resources :goals, only: [:index]
        resources :competencies, only: [:index] do
          collection do
            get :review_cycles_with_competencies
            get :review_cycle_competencies
          end
        end
      end

      namespace :core_okr do
        resources :charts, only: [:show]
        resources :goals, only: [:show, :index] do
          resources :goal_activities, only: [:index, :update, :destroy]
          resources :tasks
          resources :goal_children, only: :index
          resources :aligned_goals, only: :index
          resources :key_results, only: :index
          resources :projects, only: :index
          resources :trends, only: :index
          collection do
            get :list_of_goals_to_align, to: 'goal_alignments#index'
          end
          resources :goal_alignments, only: [] do
            collection do
              post :create, to: 'goal_alignments#create'
              put :update, to: 'goal_alignments#update'
              delete :reset_goal_alignment, to: 'goal_alignments#destroy'
            end
          end
        end
        resources :my_goals, only: [:index]
        resources :my_projects, only: [:index]
        resources :employee_goals, only: [:index]
        resources :employee_projects, only: [:index]
        resources :company_goals, only: [:index]
        resources :department_goals, only: [:index]
        resources :department_projects, only: [:index]
        resources :company_projects, only: [:index]
        resources :milestone_cycles, only: :index
        namespace :okr_summary do
          resources :check_in_status_counts, only: [:index]
          resources :goal_average_progresses, only: [:index]
          resources :goal_by_statuses, only: [:index]
        end

        resources :project_integration, only: [] do
          collection do
            put '/', to: 'project_integrations#update'
          end
        end

        resources :external_project_syncs, only: [:show]

        resources :goal_filters, only: [:index, :create, :destroy] do
          collection do
            get :create_initial_filters
            put :update_filters_priority
          end
        end

        resources :filter_options, only: :index
        resources :goal_children_progress_validation, only: :create
      end

      # namespace one_on_one conflicts with one_on_one resources
      # so need to use oneonone
      namespace :oneonone do
        resources :employee_goals, only: [:index]
      end

      namespace :biz_rev do
        resources :goals, only: [:show] do
          resources :goal_activities, only: [:index, :create, :update]
        end
        resources :kpi_progresses, only: [:index]
        # biz review filter
        resources :biz_review_filters, only: [:index, :create, :destroy] do
          collection do
            get :create_initial_filters
            put :update_filters_priority
          end
        end
        resources :latest_qualitative_goal_activities, only: [:show]
        resources :metadata_values, only: [:create]
        resources :fetch_metadata_values, only: [] do
          post :index, on: :collection
        end
        resources :biz_review_card_properties, only: [:show]
        resources :metadata_fields, only: [:create, :update, :destroy]
      end

      namespace :kpi_tracking do
        resources :kpi, path: :kpis, only: [:create, :update, :index, :destroy] do
          resources :kpi_activities, only: [:index]
          resources :kpi_checkins, only: [:create, :update]
          resources :kpi_targets, only: [:create, :update]
          resources :kpi_comments, only: [:create, :update]
        end
        resources :kpi_progresses, only: [:index] do
          collection do
            get :progress_headers
          end
        end
        resource :kpi_progress, only: [:show]
        resources :kpi_views, only: [:index]
        resources :kpi_filters, only: [:index, :create, :destroy] do
          collection do
            put :update_filters_priority
          end
        end
        resources :kpi_units, only: [:index]
      end

      # For external sync when goal is not created yet
      resources :goal_integration_fields, only: [] do
        collection do
          post :verify
          get :metric_data
        end
      end

      resources :goals do
        collection do
          get :filters_list
          get :review_index
          get :goal_progress_timeline
        end
        member do
          get :integrate_progress
          put :assign_weights
          put :align
          get :aligned_goals
          get :review_show
          get :integrate_project
          get :validate_edit_access
        end
        resources :goal_integration_fields, only: [:create, :update] do
          collection do
            post :verify
            get :metric_data
          end
        end

        resources :project_integration_fields, only: [:create, :update, :destroy] do
          collection do
            post :verify
            get :metric_data
          end
        end
        resources :goal_activities, only: [:create, :index, :show]
        resources :key_results, only: [:destroy] do
          resources :key_result_checkins, only: [:create, :update]
        end
        # resources :tasks

        resources :custom_checkin_fields, only: [:index]
        resources :custom_checkin_values, only: [] do
          collection do
            post :create_or_update_checkin_values
          end
        end
      end

      resources :my_dashboard, only: [] do
        collection do
          resources :employee_tasks, only: [:index], module: :my_dashboard
          resources :my_team, only: [:index], module: :my_dashboard
          resources :upcoming_one_on_ones, only: [:index], module: :my_dashboard
          resources :goal_by_statuses, only: [:index], module: :my_dashboard
          resources :check_in_status_counts, only: [:index], module: :my_dashboard
          resources :goal_list_by_statuses, only: [:index], module: :my_dashboard
        end
      end

      resources :goal_priorities, only: [:create]
      resources :goals_onboarding_checklists, only: [:index, :update]
      resources :pulses do
        collection do
          get :my_feedbacks
          get :team_feedbacks
        end
        member do
          get :pulse_questions_list
          get :performance_checkin_pulse
          post :change_pulse_status
        end
        resources :pulse_responses, only: [:index]
        resources :pulse_questions do
          resources :pulse_responses, only: [:create] do
            # resources :pulse_comments, only: [:create]
            # resources :pulse_likes, only: [:create, :destroy]
          end
        end
      end
      resources :employee_data_import, only: [:show, :create, :update]
      resources :employee_notification, only: [:index, :create] do
        collection do
          get :unsubscribe
        end
      end

      resources :custom_tags, only: [:index, :create, :update, :destroy]
      resources :goal_templates, only: [:index]
    end
    resource :dashboard, only: [] do
      collection do
        get :key_metrics
        get :trends
        get :key_drivers
        get :manager_overview
        get :heatmap
        get :manager_profile
        get :insights
        get :latest_insight
        get :upvote
        get :participants
        get :get_department_participation
        get :get_managers_team_participation
        get :get_engagement_scores
        get :get_engagement_trends
        get :get_department_engagement_scores
        get :get_manager_support_scores
        get :get_manager_support_trends
        get :get_managers_scores
        get :get_survey_responses
        get :get_excel_report
        get :get_text_responses
        get :get_ppt_report
        get :department_overview
        get :surveys
      end
    end
    resources :managers do
      member do
        get :redirect_to_nudge_thanks
        get :key_metrics
        get :trends
        get :key_drivers
        get :manager_reportees
        post :add_reportees
        post :invite_reportees
        get :fetch_calendar_data
        post :refresh_calendar_data
        # post :remove_reportee
        post :add_reportees_events
        put :update_relationship
        get :one_on_one_page_polling
        get :manager_trends
        get :update_relationship_from_email
      end
    end

    resources :reports do
      member do
        get :report_trends
      end
    end

    resources :responses, path: :threads do
      resources :messages

      collection do
        get :important
        get :bulk
        post :send_thanks
        get :get_response_thread
      end
    end
    resources :messages, only: [:bulk_messages] do
      collection do
        post :bulk_messages
      end
    end
    resources :drivers do
      collection do
        get :get_driver_details
      end
      member do
        get :trends
        get :responses
      end
    end

    resources :surveys do
      collection do
        post  :send_demo_survey
        get   :survey_info
      end
    end

    resources :discussions do
      collection do
        get :closed_responses
      end
      member do
        post :post_message
        get :post_message
        get :single_response_content
      end
    end

    resources :heatmap, only: [:index]

    resources :filters, only: [:message] do
      collection do
        get :message
        get :heatmap
      end
    end

    resources :schedule_surveys, only: [:index, :update, :create, :show, :destroy] do
      collection do
        get :get_questions_list
        get :pulse_survey
        get :all_pulse_surveys
        get :score_card_status
      end
      member do
        get :get_schedule_questions
        post :duplicate_schedule
        post :update_schedule
        post :send_emails_now
        get :preview_survey
        get :score_card_filters
        get :survey_score_cards
        get :fetch_notification_messages
        get :survey_report
        get :take_survey
        post :add_participant
        post :convert_to_lifecycle_survey
      end
      namespace :survey_participants do
        resources :participants, only: [:create, :index, :destroy] do
          collection do
            get :filters
          end
          member do
            delete :discard_participant_responses
          end
        end
        namespace :graphs do
          resources :bar, only: [:index]
          resources :donut, only: [:index]
        end
      end
      resources :survey_notifications, only: [:create, :index, :destroy]
    end
    resources :question_settings do
      collection do
        get :index
        post :create
        put :update
        post :reorder_question
        get :list_library
        post :add_library_question
        get :list_surveys
        get :list_drivers
        post :delete_question
      end
    end

    resources :billing do
      collection do
        post :redeem_appsumo_code
        get :plans
      end
    end

    permissions [:reviews_grant_all_permissions] do
      resources :review_cycle do
        collection do
          post :sync_google_sheets
        end

        member do
          get :reviewee_filters
          get :reviewee_filters_calibration
          get :reviewee_report_pdf
          get :release_review_pdf
          get :release_review
          get :get_reviewer_type_tabs
          get :calibration_table
          get :calibration_table_csv
          get :get_addable_reviewees_list
          get :get_calibratable_questions
          get :creation_progress
          get :export_reviewees_competency_errors
          get :competency_scores
          get :validate_goal_weightage_range
          post :add_calibration_question
          post :add_calibration_comment
          post :add_reviewee
          post :add_reviewers
          post :send_peer_selection_notification
          post :send_peer_selection_reminder
          post :send_peer_approval_notification
          post :send_manager_approval_notification
          post :send_manager_summary_notification
          post :send_peer_selection_email
          post :send_peer_selection_email_reminder
          post :send_goal_selection_email
          post :send_goal_selection_email_reminder
          post :create_review_cycle_home_page_actions
          post :send_peer_approval_email
          post :send_peer_approval_email_reminder
          post :send_goal_approval_email
          post :send_goal_approval_email_reminder
          post :send_write_review_email
          post :send_write_review_email_reminder
          post :send_write_review_manager_email
          post :send_write_review_manager_email_reminder
          post :send_manager_goals_approval_notification
          post :convert_to_one_on_one
          post :duplicate
          post :send_review_cycle_zip_to_admin
          post :recalculate_scores, to: 'appsmith#recalculate_scores'
          put  :toggle_review_cycle_feedback_visibility,  to: 'appsmith#toggle_review_cycle_feedback_visibility'
          post :add_reviewers_appsmith, to: 'appsmith#add_reviewers_appsmith'
        end

        resources :visibility_matrix, controller: 'review_cycle_visibility_matrix', only: [:index] do
          collection do
            put :update_config
            put :reset_config # For resetting all visibility matrices of a review cycle
          end
        end

        resources :review_cycle_automations, only: [:index, :update]
        resource :hierarchical_calibration, only: [:show] do
          collection do
            get :quota_ranges
            put :submit_form
            put :unsubmit_form, to: 'appsmith#unsubmit_form'
            get :table_filters
          end
        end
        resources :peers, only: [:index, :show, :update] do
          member do
            get :search_peers
            post :save_selected_peers
            get :manager_search_peers
            delete :unsubmit_peer_selection
            delete :discard_peer
          end
        end
        resources :review_cycle_goals, only: [:index, :show, :update] do
          collection do
            get :reviewees_goal_status
          end
          member do
            put :approve_goals
            put :submit_reviewee_goals
            put :edit_reviewee_goals
            put :set_equal_weights
            put :clear_reviewee_goals_weightage
            delete :discard_reviewee_goal
            post :request_to_edit
            post :approve_request_to_edit
            post :edit_reviewee_goals_approval
            get :my_goals
            post :create_reviewee_goal
            get :my_reviewee_goals
            get :validate_goals_weightage
            get :validate_reviewee_goal_weightage
            get :validate_goal_submission
            get :goals_weightage_sum
            post :request_goal_changes
            get :reviewee_goals
          end
        end
        resources :review_cycle_competencies, only: [:show, :index]

        resources :review_cycle_goal_cycle, path: :goal_cycles do
          collection do
            post :import_goal_cycles
          end
        end

        resources :reviewer_types, only: [:create] do
          collection do
            get :reviewer_type_options
          end
        end

        resources :review_cycle_phases, only: [:index, :show, :update] do
          get :participants, on: :member
          get :get_review_cycle_phases_stats, on: :collection
          put :end_hierarchical_calibration, on: :member, to: 'appsmith#end_hierarchical_calibration'
          resources :review_copy_messages, only: [:create, :update] do
            collection do
              get 'message'
            end
          end
          resources :review_cycle_notifications, only: [:create, :index, :show] do
            resources :review_cycle_notification_recipients, only: [:index]
          end
          collection do
            post :update_phases
          end
        end
        resources :reviewers, only: [:index, :create, :destroy] do
          member do
            get :review_form
            post :single_review_response
            post :goal_review_response
            post :submit_form
            get :get_reviewer_type_tabs
            get :get_review_response
            post :unsubmit_response
            post :submit_feedback_form
            post :competency_review_response
            post :write_with_ai
          end
          collection do
            get :reviewees_for_reviewer_employee
            get :list_reviewers
            get :addable_reviewees
          end
        end
        resources :skip_reviewers, only: [:update] do
          collection do
            put :approve_goals
          end
        end
        resources :reviewees do
          resources :goal_event_logs, only: [:index, :create], module: 'performance_reviews'
          collection do
            post :update_all
          end

          member do
            put :reset_peer_reviewers
            post :nominate_peer_reviewers
            post :change_reviewee_manager
            put :update_reviewee_status
            delete :remove_reviewee
            post :update_calibrated_response
            get :get_assignable_managers_list
            get :calibrate_reviewer_type_tabs
            get :calibrate_review_response
            get :addable_reviewers

            resource :hierarchical_calibration, only: [] do
              put :update_calibrated_response
              get :calibration_logs
            end
          end
          resources :review_cycle_calibration_comments
        end

        resources :review_cycle_templates do
          resources :review_cycle_form_templates, only: [:index, :create, :show, :destroy, :update] do
            collection do
              get :list_library
              post :save_as_template
            end
            member do
              post :add_template_block
              delete :delete_template_block
              put :update_template_block
              put :update_positions
              post :duplicate
            end
          end
          resources :review_cycle_template_questions do
            collection do
              put :update_positions
            end
            member do
              put :update_goal_block_legends, to: 'appsmith#update_goal_block_legends'
              put :update_question_access, to: 'appsmith#update_question_access'
            end
          end

          member do
            post :create_from_template
          end
        end

        resources :review_cycle_calibration_questions do
          collection do
            post :add_or_remove
            put :update_positions
          end
        end

        resources :calibration_charts, only: [] do
          collection do
            get '/' => 'calibration_charts#chart_data'
            get :question_list
            get :nine_box
          end
        end

        resources :manager_calibration, only: [], module: :performance_reviews do
          collection do
            get '/:manager_id', to: 'manager_calibration#index'
            get '/:manager_id/nine_box', to: 'manager_calibration#nine_box'
            get '/:manager_id/reviewee_filters', to: 'manager_calibration#reviewee_filters'
            post '/:manager_id/calibrate/:reviewer_id', to: 'manager_calibration#calibrate'
          end
        end
      end

      resources :competency_responses, only: :index do
      end
    end

    resources :home_page_actions, only: [:index, :destroy]

    resources :hris_configs do
      collection do
        get :create_link_token
      end
    end

    resource :goals_setting, only: [:update, :show]
    resources :goals_settings_preview, only: [:show]
    resource :one_on_one_setting, only: [:update, :show]
    resources :one_on_one_activities, only: [:index]
    resources :activity_logs, only: [:index]

    resources :metabase_reports

    resources :permissions, only: [] do
      collection do
        post :update_acls
        post :access
        post :list
      end
    end

    resources :acl_roles, only: [:index, :show, :create, :destroy] do
      resources :permissions, only: [:index], controller: 'acl_roles/permissions' do
        member do
          get :targetables
        end
      end
    end

    resources :okr_updates, only: [:create, :update, :show] do
      collection do
        post :notify_reportee
      end
      resources :goals, only: :index, module: :okr_updates
    end

    resources :microsoft_teams do
      collection do
        get :integration_details
      end
      member do
        delete :remove_integration
      end
    end

    resources :csv_import_chunks, only: [:index]

    namespace :reporting do
      resources :survey_reports, only: [:index] do
        member do
          post :details
          post :download
        end
      end
      resources :goal_reports, only: [:index] do
        member do
          post :details
          post :download
        end
      end
      resources :one_on_one_reports, only: [:index] do
        member do
          post :details
          post :download
        end
      end
      resources :review_reports, only: [:index] do
        member do
          post :details
          post :download
          post :sql
        end
      end
      resources :filters, only: [] do
        collection do
          post :filter_options
        end
      end
      resources :custom_reports, except: [:update] do
        collection do
          get :report_topics
          get :cube_members
          post :report_preview
          post :report_sql
        end
        member do
          post :duplicate
          get :validate
        end
      end
    end

    resources :competencies, only: %i[index destroy show] do
      collection do
        get :competency_matrix
        get :export_matrix
        get :export_competency
        get :validate_matrix_import
      end
    end

    resources :idp_teams, only: %i[index create update] do
      get :select_list, on: :collection

      resources :idp_track_positions, only: [:index]

      resources :idp_tracks, only: [] do
        resources :idp_track_positions, only: [:show]
      end
    end

    resources :idp_tracks, only: [] do
      get :select_list, on: :collection
    end

    resources :idp_track_positions, only: [] do
      collection do
        get :export_position_competency
        get :select_list
      end
    end

    resources :competency_settings, only: %i[index create]
    resources :pbx_credits, only: [] do
      get :available_balance, on: :collection
    end

    resources :account_ai_model_configs, param: :identifier, except: %i[create destory]

    resources :nova_conversations, only: [:create, :update, :index, :show] do
      post :chat, on: :member
      post :regenerate_response, on: :member

      resources :nova_chat_messages, only: [:index] do
        post :nova_feedback, on: :member
      end
    end

    resource :company_data, only: [:create, :show] do
      get :status, on: :collection
    end
  end

  resource :sso_settings, only: [:show, :update]
  resources :sso, controller: 'sso' do
    member do
      post :assert
      get :portal_login # used by Nupco
    end
  end

  resources :goal_progress_types, only: [:index]

  resources :users do
    collection do
      post :authenticate
      post :check_token_validity
      post :change_password
      post :reset_password
      get  :get_all_account_admins
      get  :get_user_details
      post :switch_user
      post :set_password
      post '/auth/oauth2/callback' => 'users#oauth2'
      post :slack_auth
      post :sign_up_with_email, to: 'appsmith#sign_up_with_email'
      post :update_user_password
      post '/impersonate/:id' => 'users#impersonate'
    end
    member do
      post :setup_calendar
      get :competencies
    end
  end

  resources :demo do
    collection do
      get :engagement
      get :engagement_dashboard
      get :manager
      get :manager_init
      get :manager_chat
      get :old_manager
      get :test
      get :try_interactive_demo
      get :generate_sandbox_link
      get :latest
      get :sandbox_generate
      get :sandbox_details
      get :switch_manager_report
      get :switch
      get :revert_slack_email
      post :slack_notifications, to: 'demo/slack_notifications#create'
      post :slack_marketplace_notifications, to: 'demo/slack_marketplace_notifications#create'
      patch :update_subscription, to: 'demo#update_subscription'
    end
  end

  namespace :v1 do
    resources :questions, only: [:index, :show] do
      collection do
        get :categories
      end
    end
  end

  resources :integrations, only: [:index, :show] do
    member do
      get :metrics
    end
  end
  resources :marketing_leads, only: [:create]
  resources :agenda_growth_hacks, only: [:create]

  resources :csv_import_configurations
  resources :csv_imports do
    collection do
      match '/configurations/:identifier' => 'csv_imports#configurations', via: [:get, :post]
      post '/process/:identifier' => 'csv_imports#process_import'
      get :employee_import_status
    end
  end

  resources :slack_init, only: [:index]
  resources :hris_oauth, only: [:index]

  get '/r/:id' => 'shortener/shortened_urls#show', as: 'culturegrade_short_url'
  get '/sentiment' => 'api#sentiment_analysis'
  get '/start-chat' => 'api#start_chat'
  get '/next-week-chat' => 'api#next_week_chat'
  post '/email-fail' => 'api#email_fail'

  # Webhooks and Integrations
  post '/google_notifications' => 'api#google_notifications'
  get '/gdrive_access' => 'api#gdrive_access'
  post '/microsoft_teams_notifications', to: 'api#microsoft_teams_notifications'
  post '/slack_interactive_notifications', to: 'api#slack_interactive_notifications'
  post '/slack_event_notifications', to: 'api#slack_event_notifications'
  get '/agenda-list' => 'one_on_one_agenda_templates#list'
  post 'one_on_one_agenda_templates/:one_on_one_agenda_template_id/like' => 'one_on_one_agenda_templates#like'
  post 'one_on_one_agenda_templates/:one_on_one_agenda_template_id/unlike' => 'one_on_one_agenda_templates#unlike'
  get :version, to: 'api#version'
  post 'create_appsumo_account' => 'billing#create_appsumo_account'
  get 'coffee_connect_pairings/:id', to: 'api#coffee_connect_pairings'
  get :destroy_coffee_connect_pairings, to: 'api#destroy_coffee_connect_pairings'
  get :seed_intrest_tags, to: 'api#seed_intrest_tags'
  get :coffee_connect_checkins, to: 'api#coffee_connect_checkins'
  get :slack_message_email_reminder, to: 'api#slack_message_email_reminder'
  get :slack_message_manager_reminder, to: 'api#slack_message_manager_reminder'
  get :slack_message_ceo_reminder, to: 'api#slack_message_ceo_reminder'
  get :send_participation_feedback_survey_reminder, to: 'api#send_participation_feedback_survey_reminder'
  get :send_participation_ceo_reminder, to: 'api#send_participation_ceo_reminder'
  get :send_talking_points_reminder, to: 'api#send_talking_points_reminder'
  get 'coffee_connect_summary/:id', to: 'api#coffee_connect_summary'
  post '/slack_options_load_url', to: 'api#slack_options_load_url'
  get 'create_review_cycle/:id', to: 'api#create_review_cycle'
  get 'send_initial_pr_notification/:id', to: 'api#send_initial_pr_notification'
  get 'send_manager_pr_notification/:id', to: 'api#send_manager_pr_notification'
  get 'send_write_pr_notification/:id', to: 'api#send_write_pr_notification'
  post 'google_sheet_hris_sync', to: 'api#google_sheet_hris_sync'
  post :sync_to_hubspot, to: 'api#sync_to_hubspot'
  get :reset_calvin_account, to: 'api#reset_calvin_account'
  get :google_sheet_update_apna_peoplebox, to: 'api#google_sheet_update_apna_peoplebox'
  resources :chargebee do
    collection do
      post :webhook, as: :chargebee_webhook
      post 'generate_checkout_new_url' => 'chargebee#checkout_new'
      post 'generate_checkout_existing_url' => 'chargebee#checkout_existing'
      post :cancel_subscription
      post :switch_subscription_checkout_status
    end
  end
  resources :google_sheet_projects_sync, only: :create

  post :wootric_intercom, to: 'api#wootric_intercom'

  # slack channel update
  post 'send_slack_notification', to: 'api#send_slack_notification'

  resources :ai_model_configs, only: :index

  resources :nova_conversations, only: [] do
    get :default_prompts, on: :collection
  end

  resources :company_data, only: [] do
    get :contextual_questions, on: :collection
  end

  root to: 'api#root'
  if Rails.env.development?
    mount LetterOpenerWeb::Engine, at: '/letter_opener'
  end
end
