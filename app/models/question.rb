# frozen_string_literal: true

require 'redcarpet'
require 'redcarpet/render_strip'

class Question < ApplicationRecord
  include Discard::Model
  include ReviewCycleQuestionVisibilityConcern

  default_scope -> { kept }
  has_and_belongs_to_many :surveys, -> { distinct }
  has_many :responses
  belongs_to :driver, optional: true
  has_many :pulse_reasons, dependent: :destroy
  has_one :omnichat_block, as: :source, dependent: :destroy
  belongs_to :serviceable, polymorphic: true, optional: true

  enum question_type: {
    nps: 'nps', text: 'text', three_text: 'three_text', multiple_text: 'multiple_text',
    single_option: 'single_option', multiple_option: 'multiple_option', rating_reason: 'rating_reason',
    rating_text: 'rating_text', enps: 'enps'
  }

  enum input_text_type: {
    rich_text: 'rich_text', text_input: 'text_input'
  }

  enum nps_view_type: {
    multiple_choice: 'multiple_choice', dropdown: 'dropdown', like_dislike: 'like_dislike',
    numeric: 'numeric'
  }

  belongs_to :account, optional: true
  enum status: { live: 'live', archived: 'archived' }
  # default_scope { where(status: :live) }
  scope :shared, -> { where(account: nil) }

  validates :question_type, inclusion: { in: Question.question_types.keys }
  validates :input_text_type, inclusion: { in: Question.input_text_types.keys }, allow_blank: true
  validates :input_text_type, presence: true, if: -> { question_type == 'text' }
  validates :options, presence: true, if: :nps?

  has_many  :options, dependent: :destroy
  has_many :rating_ranges, dependent: :destroy

  has_many :schedule_questions, -> { order(:position) }
  has_many :schedules, through: :schedule_questions, dependent: :destroy

  has_many :benchmarks, class_name: 'GlobalAverage', as: :benchmarkable, dependent: :destroy
  has_many :review_cycle_template_questions, dependent: :destroy
  has_many :review_responses, dependent: :destroy
  has_many :feedback_question_blocks, dependent: :destroy
  has_many :feedback_responses, as: :feedback_questionable
  has_many :review_cycle_calibration_questions, dependent: :destroy
  accepts_nested_attributes_for :options, allow_destroy: true
  accepts_nested_attributes_for :review_cycle_calibration_questions
  after_create :create_question_options, unless: :skip_callbacks
  after_create :create_question_benchmark, unless: :skip_callbacks
  before_update :can_update_or_destroy?, prepend: true, if: -> { (self.changed - %w[updated_at alias_text]).present? }
  before_destroy :can_update_or_destroy?, prepend: true
  before_validation :set_input_text_type, if: -> { question_type == 'text' }

  has_paper_trail
  has_many :question_rounding_limits

  after_update :update_review_cycle_question_visibility_records, if: -> { saved_change_to_visibility_fields? }

  def as_json(options = {})
    # FIXME: should move to decorator
    @campaign = options[:campaign] if options.key? :campaign
    super({ only: %i[id created_at question_text question_type root_question followup_type can_be_skipped],
            methods: [:driver] })
  end

  # def question_text(pulse)
  #   debugger
  # end

  # def question_text_for_campaign(campaign)
  #   template_text(read_attribute(:question_text), campaign)
  # end

  # def positive_quest
  #   template_text(read_attribute(:positive_quest), @campaign)
  # end

  # def negative_quest
  #   template_text(read_attribute(:negative_quest), @campaign)
  # end

  # def question_template_text(campaign)
  #   template_text
  # end

  def set_input_text_type
    self.input_text_type = 'text_input' if input_text_type.blank?
  end

  def parent_driver
    Driver.find(driver).parent_driver
  end

  def template_text(text, employee_chat)
    variables = {}
    if employee_chat.present?
      variables = {
        'company_name' => employee_chat.account.company_name,
        'manager_name' => employee_chat.employee.manager_name.present? ? "(#{employee_chat.employee.manager_name})" : '',
      }
    end

    Liquid::Template.parse(text).render(variables)
  end

  def question_template_text(account, employee = nil)
    variables = {
      'company_name' => account.company_name,
    }
    if employee.present?
      variables['manager_name'] = employee.manager_name.present? ? '(' + employee.manager_name + ')' : ''
      variables['reviewee_name'] = employee.full_name
    end

    Liquid::Template.parse(question_text).render(variables)
  end

  def create_question_options
    # create question options for Question in active admin
    # create question options for nps and text types.
    # question options for other types are handled in the active admin page.
    create_nps_options if nps? && options.blank?
  end

  def create_nps_options
    options = [1, 2, 3, 4, 5]
    options.each do |option|
      option_attributes = {
        value: option,
        question: self,
      }
      case option
      when 1
        option_attributes[:legend] = nps_low || 'Not at all'
      when 5
        option_attributes[:legend] = nps_high || 'Absolutely'
      end
      Option.create(option_attributes)
    end
  end

  def create_question_benchmark
    benchmarks.create!
  end

  def display_name
    question_text
  end

  def question_for_pdf
    question_text.gsub('\n', '<br/>')
  end

  def can_update_or_destroy?
    if self.review_responses.length.positive?
      self.errors.add(:base, "Can't edit this block, it has existing responses")
      throw :abort
    end
  end

  def resolve_review_question(response)
    reviewee_name = response.try(:display_name)
    text = question_text.gsub('{{reviewee_name}}', reviewee_name)
    Redcarpet::Markdown.new(Redcarpet::Render::StripDown).render(text)
  end

  def score_visible
    # checks if all options have score_visible for a question
    options = Option.where(question: self)
    options.each do |option|
      if option.score_visible == true
        return true
      end
    end
    false
  end

  def get_rounded_score(score)
    #checks if there is rounding logic associated with the question and returns score accordingly
    rounding_mapping = self.question_rounding_limits.find_by("min_score <= ? and max_score >= ?", score, score)
    if rounding_mapping.nil?
      return score
    else
      return rounding_mapping.rounded_score.to_f
    end
  end
end
