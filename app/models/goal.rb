class Goal < ApplicationRecord
  include Discard::Model

  attr_accessor :owners

  # Callbacks
  after_commit :add_goal_priority, on: :create
  after_commit :add_goal_to_okr_updates, on: :create

  # Associations
  has_many :employee_goals, dependent: :destroy
  has_many :employees, through: :employee_goals
  has_many :key_results, inverse_of: :goal, dependent: :destroy
  has_many :goal_activities, -> {
    where.not(source: GoalActivity.sources['key_result']).order(created_at: :desc)
  }, dependent: :destroy
  has_many :goals_departments, dependent: :destroy
  has_many :departments, through: :goals_departments
  has_many :reviewee_goals
  has_many :review_responses
  has_many :tasks, foreign_key: 'project_id', inverse_of: :project
  has_many :metadata_values, as: :owner, dependent: :destroy
  has_many :goal_kpis
  has_many :kpis, through: :goal_kpis
  has_many :goal_priorities, dependent: :destroy

  has_many :goal_hierarchies, foreign_key: :child_id, dependent: :destroy do
    def without_self_reference
      where.not(depth: 0)
    end

    def self_reference
      where(depth: 0)&.first
    end
  end
  has_many :goal_hierarchies_as_parent, class_name: 'GoalHierarchy', foreign_key: 'parent_id', dependent: :destroy
  has_many :child_goals, through: :goal_hierarchies_as_parent, source: :child_goal
  has_many :goal_hierarchies_as_child, class_name: 'GoalHierarchy', foreign_key: 'child_id', dependent: :destroy
  has_many :parent_goals, through: :goal_hierarchies_as_child, source: :parent_goal

  has_many :ancestors, -> {
    where.not(goal_hierarchies: { depth: 0 })
  }, through: :goal_hierarchies_as_child, source: :parent_goal

  has_many :children, -> {
    where(goal_hierarchies: { depth: 1 })
  }, through: :goal_hierarchies_as_parent, source: :child_goal

  has_many :parents, -> {
    where(goal_hierarchies: { depth: 1 })
  }, through: :goal_hierarchies_as_child, source: :parent_goal

  has_many :goal_milestones, dependent: :destroy
  has_many :milestone_goals, through: :goal_milestones, class_name: 'Goal', foreign_key: 'milestone_goal_id', dependent: :destroy

  has_many :reverse_milestone_goals, class_name: 'GoalMilestone', foreign_key: 'milestone_goal_id'
  has_many :parent_milestone_goals, through: :reverse_milestone_goals, source: :goal

  # Goal Updates
  has_many :okr_updates_goals
  has_many :okr_updates, through: :okr_updates_goals

  # Goal Audits
  has_many :activity_logs, as: :loggable

  belongs_to :account
  belongs_to :goal_cycle
  belongs_to :added_by, class_name: 'Employee', foreign_key: 'added_by_id'
  belongs_to :duplicated_from, class_name: 'Goal', foreign_key: 'duplicated_from_id', optional: true

  has_one :goal_progress, dependent: :destroy
  has_one :omnichat_conversation, as: :feature, dependent: :destroy
  has_one :goal_integration_field, foreign_key: 'goal_id'
  has_one :project_integration_field, class_name: 'GoalIntegrationField', foreign_key: 'project_id'
  has_one :okr_update_goal_activity_mapping
  has_many :custom_taggings, as: :taggable, dependent: :destroy
  has_many :custom_tags, through: :custom_taggings
  has_many :acls, as: :resource, dependent: :destroy

  # Validations
  validates :milestone_split_type, presence: { if: :has_milestones? }
  validate :goal_milestones_attributes_presence_when_has_milestones, on: :create
  validate :milestone_goals_dependency, on: :create

  # Scopes
  # public is reserved keyword for Active Record
  default_scope -> { kept }
  scope :only_public, -> { where(visibility: 0) }
  scope :except_aligned, -> { where.not(goal_hierarchies: { objective_type: 'alignment' }) }
  scope :accessible, ->(employee_id) {
    joins("LEFT JOIN (
            SELECT DISTINCT
              resource_id,
              resource_type,
              employee_id
            FROM
              view_permissions
            WHERE
              resource_type = 'Goal'
              AND employee_id = #{employee_id}
          ) view_permissions ON goals.id = view_permissions.resource_id")
      .where("view_permissions.employee_id = ?", employee_id)
  }

  accepts_nested_attributes_for :employee_goals, allow_destroy: true
  accepts_nested_attributes_for :goal_progress
  accepts_nested_attributes_for :goal_integration_field
  accepts_nested_attributes_for :tasks, allow_destroy: true
  accepts_nested_attributes_for :goals_departments, allow_destroy: true
  accepts_nested_attributes_for :goal_milestones, allow_destroy: true
  accepts_nested_attributes_for :custom_taggings, allow_destroy: true

  # Enums
  enum goal_type: { individual: 0, department: 1, company: 2 }
  enum objective_type: { objective: 0, key_result: 1, project: 2 }
  enum milestone_split_type: { quarterly: 0, monthly: 1, weekly: 2, half_yearly: 3 }
  enum check_in_type: { manual: 0, auto_roll_up: 1, external_source: 2 }
  enum milestone_summary_method: { last_value: 0, summation: 1, average_value: 2 }
  # Cannot use private because "ActiveRecords already have a method by that name -_-"
  # Not using public because key is already there
  enum visibility: { open: 0, restricted: 1, custom: 2 }

  has_paper_trail on: [:create, :update, :destroy]

  after_update :discard_excluded_reviewee_goals
  after_update :trigger_reviewee_goal_callbacks
  after_update :discard_all_reviewee_goals_after_cycle_change, if: -> { saved_change_to_attribute?(:goal_cycle_id) }

  after_discard do
    if associated_to_perf_review?
      self.undiscard
    else
      self.goal_hierarchies.destroy_all
      self.goal_milestones.destroy_all
      self.goal_activities.discard_all
      self.okr_updates.destroy_all
      self.custom_taggings.discard_all
      self.acls.discard_all

      reviewee_goals.where(goal: self).discard_all
      review_responses.where(goal: self).discard_all
      if employee_goals.present?
        employee_goals.where(goal: self).discard_all
      end
    end
  end

  after_undiscard do
    RevieweeGoal.with_discarded.discarded.where(goal: self).undiscard_all
    ReviewResponse.with_discarded.discarded.where(goal: self).undiscard_all
    EmployeeGoal.with_discarded.discarded.where(goal: self).undiscard_all
    GoalActivity.with_discarded.discarded.where(goal: self).undiscard_all
    CustomTagging.with_discarded.discarded.where(taggable: self).undiscard_all
  end

  def discard_all_reviewee_goals_after_cycle_change
    # if new goal cycle is not part of the review goal cycles
    # then discard it
    reviewee_goals.joins(review_cycle: :goal_cycles)
      .where.not(review_cycles: { goal_cycles: { id: goal_cycle_id } })
      .discard_all
  end

  def associated_to_perf_review?
    discard_reviewee_goals

    (associated_with_reviewee_goals? ||
    associated_with_review_responses? ||
    review_with_key_results_using_self ||
    review_with_objectives_and_key_results_using_self)
  end

  def associated_with_reviewee_goals?
    reviewee_goals.exists?(exclude: false)
  end

  def associated_with_review_responses?
    reviewee_goals
      .joins(:goal)
      .where(exclude: false)
      .exists?(goal: review_responses.pluck(:goal_id))
  end

  def review_with_key_results_using_self
    # if editing a goal and its key results,project or aligned has been used in a review,
    # it become un-editable
    review_cycle_ids = account.review_cycles.joins(:goal_cycles).select do |review_cycle|
      review_cycle.review_cycle_goal_type[:goal_type] == 'goals_with_key_results_only'
    end.pluck(:id)
    account.review_cycles.where(id: review_cycle_ids)
      .joins(:reviewee_goals).exists?(reviewee_goals: {
                                        goal_id: child_key_results_and_projects.ids,
                                        exclude: false,
                                      })
  end

  def review_with_objectives_and_key_results_using_self
    review_cycle_ids = account.review_cycles.joins(:goal_cycles).select do |review_cycle|
      review_cycle.review_cycle_goal_type[:goal_type] == 'goals_with_objectives_and_key_results'
    end.pluck(:id)
    account.review_cycles.where(id: review_cycle_ids)
      .joins(:reviewee_goals)
      .exists?(reviewee_goals: {
                 goal_id: (child_key_results_and_projects.ids + aligned_goals.ids + aligned_parents.ids).flatten,
                 exclude: false,
               })
  end

  def discard_excluded_reviewee_goals
    return if associated_to_perf_review?

    discard_reviewee_goals
  end

  def discard_reviewee_goals
    reviewee_goals.where(exclude: true).discard_all
  end

  # If we want to add more callbacks in future, we can add them here.
  def trigger_reviewee_goal_callbacks
    return unless goal_cycle_id_previously_changed?

    reviewee_goals.each(&:discard_irrelevant_reviewee_goals)
  end

  def aligned_parents
    parent_goals
      .where( goal_hierarchies: { depth: 1, objective_type: 'alignment' })
  end

  def key_result_to_parents
    parent_goals
      .where( goal_hierarchies: { depth: 1, objective_type: 'key_result' })
  end

  def aligned_ancestors
    ancestors
      .where(goal_hierarchies: { objective_type: 'alignment' })
  end

  def key_result_and_project_ancestors
    ancestors
      .where(goal_hierarchies: { objective_type: ['key_result', 'project'] })
  end

  def descendants
    child_goals
      .where.not(goal_hierarchies: { depth: 0 })
  end

  def descendant_projects
    descendants
      .where(goal_hierarchies: { objective_type: 'project' })
  end

  def aligned_key_results
    children.where(goal_hierarchies: { objective_type: 'key_result' })
  end

  def aligned_goals
    children.where(goal_hierarchies: { objective_type: 'alignment' })
  end

  def aligned_projects
    children.where(goal_hierarchies: { objective_type: 'project' })
  end

  def aligned_key_result_goals
    children.where(goal_hierarchies: { objective_type: 'key_result' })
  end

  def child_key_results_and_projects
    children.where(goal_hierarchies: { objective_type: %w[key_result project] })
  end

  # (aligned_key_results.size + aligned_goals.size) > 0
  def children_present?(employee)
    goal_children = children.where(goal_hierarchies: { objective_type: ['key_result', 'alignment'] })
    OkrFilterChildrenByRoles.call(goal_children, employee).count > 0
  end

  def projects_present?(employee)
    OkrFilterChildrenByRoles.call(aligned_projects, employee).count > 0
  end

  def has_milestones_and_milestone_goals?
    has_milestones? && has_milestone_goals?
  end

  def missing_child_targets?
    return false unless has_milestones_and_milestone_goals?

    milestone_goals.joins(:goal_progress).where('goal_progresses.progress_start IS NULL OR goal_progresses.progress_target IS NULL').exists?
  end

  def checkin_disabled?
    goal_progress.progress_start.nil? || goal_progress.progress_target.nil?
  end

  def compute_current_progress
    progress_type = goal_progress.goal_progress_type.title
    progress_type = progress_type.in?(%w[dollar indian_rupee euro GBP BDT SAR AED]) ? 'currency' : progress_type
    "CoreOkr::CheckinRollUps::#{progress_type.camelize}::AggregateChildrenProgresses".constantize.call(self)
  end

  def update_progress_percent
    progress = compute_current_progress
    goal_progress.update_attributes!(
      current_progress: progress[:current_progress_value],
      progress_percent: progress[:current_progress_percentage],
      progress_status: progress[:current_progress_status],
    )

    progress
  end


  def goal_milestones_attributes_presence_when_has_milestones
    if has_milestones? && goal_milestones.blank?
      errors.add(:base, "goal_milestones_attributes must be present when has_milestones is true")
    elsif !has_milestones? && goal_milestones.present?
      errors.add(:base, "goal_milestones_attributes must be empty when has_milestones is false")
    end
  end

  def milestone_goals_dependency
    if has_milestone_goals? && !has_milestones
      errors.add(:has_milestones, 'must be true when has_milestone_goals is true')
    end
  end

  # Only objectives or root goals of given cycle can have goal priority
  def add_goal_priority
    return unless objective?

    case goal_type
    when 'individual', 'company'
      entity_id = goal_type == 'individual' ? added_by_id : account_id
      GoalPriority.where(entity_id: entity_id, entity_type: goal_type).update_all('position = position + 1')
      GoalPriority.create(entity_type: goal_type, entity_id: entity_id, goal_id: id, position: 1, last_updated_by_id: added_by_id)
    when 'department'
      departments.pluck(:id).each do |entity_id|
        GoalPriority.where(entity_id: entity_id, entity_type: goal_type).update_all('position = position + 1')
        GoalPriority.create(entity_type: goal_type, entity_id: entity_id, goal_id: id, position: 1, last_updated_by_id: added_by_id)
      end
    end
  end

  def create_goal_activity_for_changed_progress_type(employee_id)
    completed_value = goal_progress.current_progress
    old_progress = if goal_progress.is_default_progress?
                     goal_progress.attribute_before_last_save('progress_percent').to_f
                   else
                     goal_progress.attribute_before_last_save('current_progress').to_f
                   end
    old_status = goal_progress.attribute_before_last_save('progress_status')
    previous_progress_type = GoalProgressType.find(goal_progress.attribute_before_last_save('goal_progress_type_id'))&.title

    return if previous_progress_type == goal_progress.goal_progress_title

    goal_activities.create(
      description: activity_description(old_progress, completed_value),
      completed_value: completed_value,
      employee_id: employee_id,
      previous_progress_value: old_progress,
      previous_progress_status: old_status,
      status: 'not_started',
      source: 'changed_progress_type',
      checkin_time: DateTime.current,
      checkin_type: 'current_time',
      system_comment: false,
      previous_progress_type: previous_progress_type,
      progress_type: goal_progress.goal_progress_title,
    )
  end

  def activity_description(old_progress, value)
    "Updated the progress from #{old_progress.round(1)} to #{value.round(1)} due to change in progress type"
  end

  def is_visible_to?(emp)
    return false unless emp.account_id == self.account_id
    return true if emp.admin?
    return true if (self.employee_ids&.include?(emp.id) || self.added_by_id == emp.id)

    # Using where and exists because #exists?() only takes 1 arg and we need to pass two for string interpolation.
    Goal.joins("
          LEFT JOIN (
            SELECT DISTINCT
              resource_id,
              resource_type,
              employee_id
            FROM
              view_permissions
            WHERE
              resource_type = 'Goal'
              AND employee_id = #{emp.id}
          ) view_permissions ON goals.id = view_permissions.resource_id"
      ).where("view_permissions.resource_id = ?", self.id).exists?
  end

  def checkins_after(after_datetime)
    checkins = GoalActivity.where('checkin_time > :after_datetime', after_datetime: after_datetime)
                 .where(goal_id: id)
                 .where('completed_value != previous_progress_value OR status != previous_progress_status')
                 .where.not(source: 'changed_progress_type')

    changed_at = changed_progress_type_time
    changed_at.present? ? checkins.where('created_at > ?', changed_at) : checkins
  end

  def checkins_after_exists?(after_datetime)
    checkins_after(after_datetime).exists?
  end

  def self.fetch_associated_goal_ids(selected_goal_ids)
    ancestor_ids = Goal.joins(:goal_hierarchies_as_child)
                        .where(goal_hierarchies: { child_id: selected_goal_ids })
                        .pluck(:parent_id)
                        .uniq

    descendant_ids = Goal.joins(:goal_hierarchies_as_parent)
                          .where(goal_hierarchies: { parent_id: selected_goal_ids })
                          .pluck(:child_id)
                          .uniq

    (ancestor_ids + descendant_ids).uniq
  end

  def self.associated_ancestor_descendant_ids(selected_goal_ids)
    Goal.joins(:goal_hierarchies)
      .where(goal_hierarchies: { child_id: selected_goal_ids })
      .or(Goal.joins(:goal_hierarchies)
        .where(goal_hierarchies: { parent_id: selected_goal_ids }))
      .pluck(:parent_id, :child_id)
      .flatten
      .compact
      .uniq
  end

  def self.fetch_sibling_goal_ids(selected_goal_ids)
    Goal.joins(:goal_hierarchies_as_child)
      .where(goal_hierarchies: { parent_id: parent_ids(selected_goal_ids), depth: 1 })
      .where.not(id: selected_goal_ids)
      .pluck(:id)
      .uniq
  end

  def self.associated_ancestor_descendant_sibling_ids(selected_goal_ids)
    (
      associated_ancestor_descendant_ids(selected_goal_ids) +
      fetch_sibling_goal_ids(selected_goal_ids)
    ).uniq
  end

  def self.parent_ids(selected_goal_ids)
    GoalHierarchy.where(child_id: selected_goal_ids, depth: 1).pluck(:parent_id)
  end

  def siblings
    parent_ids = parents.pluck(:id)

    if parent_ids.any?
      # Find all children of the parents except the selected goal
      siblings = Goal.joins(:goal_hierarchies_as_child)
                     .where(goal_hierarchies: { parent_id: parent_ids, depth: 1 })
                     .where.not(id: id)
                     .distinct
    else
      siblings = Goal.none
    end

    siblings
  end

  def closed?
    goal_progress.status_closed?
  end

  def open_key_results
    aligned_key_results.joins(:goal_progress)
                       .where(goal_progresses: { progress_status: %w[not_started on_track behind at_risk] })
                       .pluck(:id)
  end

  def milestone_derived_from
    return unless self.is_milestone?

    self.parent_milestone_goals.first&.goal_cycle&.cycle
  end

  def add_goal_to_okr_updates
    case goal_type.to_sym
    when :individual
      add_resource_goal_to_okr_updates([added_by])
    when :department
      add_resource_goal_to_okr_updates(departments)
      add_resource_goal_to_okr_updates([added_by])
    when :company
      add_resource_goal_to_okr_updates([account])
      add_resource_goal_to_okr_updates([added_by])
    end
  end

  def add_resource_goal_to_okr_updates(resources)
    resources.each do |resource|
      okr_update = find_associated_okr_update(resource)
      next unless okr_update

      okr_update.okr_updates_goals.find_or_create_by(goal: self)
    end
  end

  def find_associated_okr_update(resource)
    okr_update = OkrUpdate.find_by(resource: resource, status: 'draft')
    return unless okr_update

    okr_update if okr_update.active_goal_cycle_ids.include?(goal_cycle_id)
  end

  def self_hierarchy_objective_type
    goal_hierarchies&.self_reference&.objective_type
  end

  def objective_type_with_goal(parent_goal)
    goal_hierarchies.find_by(parent_id: parent_goal.id)&.objective_type
  end

  def can_edit?(employee)
    employee.admin? || employee.goal_admin? || manager_of_goal_owner?(employee) || employee.can_manage_goal?(self) ||
      (no_approved_reviewee_goals? && (added_by_id == employee.id || employee_ids.include?(employee.id)))
  end

  def can_delete?(employee)
    no_approved_reviewee_goals? &&
      (
        employee.admin? || employee.goal_admin? || manager_of_goal_owner?(employee) || employee.can_manage_goal?(self) ||
          added_by_id == employee.id || employee_ids.include?(employee.id)
      )
  end

  def can_checkin?(employee)
    employee.admin? || employee.goal_admin? || manager_of_goal_owner?(employee) || employee.can_manage_goal?(self) ||
      (added_by_id == employee.id || employee_ids.include?(employee.id))
  end

  def manager_of_goal_owner?(employee)
    employees.pluck(:manager_id).include?(employee.id)
  end

  def no_approved_reviewee_goals?
    !RevieweeGoal.where(exclude: false, goal: [self, *aligned_key_results]).where.not(approver_id: nil).exists?
  end

  def cannot_edit_reason(employee)
    return if can_edit?(employee)

    cannot_edit_or_delete_reason
  end

  def cannot_delete_reason(employee)
    return if can_delete?(employee)

    cannot_edit_or_delete_reason
  end

  def cannot_edit_or_delete_reason
    review_cycle = RevieweeGoal.where(exclude: false, goal: [self, *aligned_key_results]).where.not(approver_id: nil).first&.review_cycle
    "This goal has been approved for \"#{review_cycle&.title}\" #{review_cycle&.title&.split&.last&.downcase == 'review' ? '' : 'review'}"
  end

  def approved_reviewee_goal_attributes
    reviewee_goal = RevieweeGoal.where(exclude: false, goal: [self, *aligned_key_results])
                                .where.not(approver_id: nil).includes(:review_cycle, :reviewee).first
    return unless reviewee_goal

    review_cycle = reviewee_goal.review_cycle
    reviewee = reviewee_goal.reviewee

    { review_cycle_id: reviewee_goal.review_cycle_id,
      reviewee_id: reviewee_goal.reviewee_id,
      reviewee_employee_id: reviewee.employee_id,
      reviewee_manager_name: reviewee.manager&.display_name,
      review_cycle_title: review_cycle.title }
  end

  def update_check_in_type_after_progress_type_change
    return unless auto_roll_up? && !goal_progress.is_default_progress?

    update(check_in_type: 'manual')
  end

  def rollable_children_present?
    (goal_progress.goal_progress_title.in?(%w[default_progress custom_percent binary]) &&
      child_key_results_and_projects.exists?) ||
      child_key_results_and_projects.joins(:goal_progress).exists?(goal_progresses: { goal_progress_type_id: goal_progress.goal_progress_type_id })
  end

  def aggregate_progress(activity, employee_id)
    aggregate_value = Base::Goals::GoalMilestonesAggregate.call(self)
    return if aggregate_value.nil?

    checkin_time = activity&.checkin_time || DateTime.current
    completed_value = completed_value_from_aggregate(aggregate_value)

    aggregate_activity = goal_activities.create(
      description: "Updated the progress from #{previous_aggregate_value.round(1)} to #{aggregate_value.round(1)}",
      completed_value: completed_value,
      employee_id: employee_id,
      previous_progress_value: previous_aggregate_value,
      previous_progress_status: goal_progress.progress_status,
      status: (activity&.backdated? && checkins_after_exists?(checkin_time)) || activity.nil? ? goal_progress.progress_status : activity.status,
      source: 'milestones_aggregate',
      checkin_time: checkin_time,
      checkin_type: activity&.checkin_type || 'current_time',
      system_comment: false,
      milestone_summary_method: milestone_summary_method,
    )

    aggregate_activity
  end

  def previous_aggregate_value
    goal_activities.where(source: 'milestones_aggregate').first&.completed_value || goal_progress.current_progress
  end

  def aggregate_required?(activity)
    has_milestones && milestone_summary_method.present? && !last_value? &&
      (activity&.checkin_time || DateTime.current).in?(goal_cycle.start_date..goal_cycle.end_date)
  end

  def changed_progress_type_time
    goal_activities
      .where(source: 'changed_progress_type')
      .order(created_at: :desc)
      .limit(1)
      .pluck(:created_at)
      .first
  end

  def create_goal_activity_for_changed_milestone_summary_method(employee_id)
    aggregate_value =
      if last_value?
        checkins = goal_activities.where.not(source: [11, 12, 13, 23, 25]).unscope(:order)
        checkins = checkins.where('created_at > ?', changed_progress_type_time) if changed_progress_type_time
        checkins
          .order(checkin_time: :desc, created_at: :desc)
          .limit(1)
          .pluck(:completed_value)
          .first
      else
        Base::Goals::GoalMilestonesAggregate.call(self)
      end

    return if aggregate_value.nil?

    completed_value = completed_value_from_aggregate(aggregate_value)
    old_progress = previous_aggregate_value
    return if completed_value == old_progress

    aggregate_activity = goal_activities.create(
      description: "Updated the progress from #{old_progress.round(1)} to #{aggregate_value.round(1)} due to change in milestone summary method",
      completed_value: completed_value,
      employee_id: employee_id,
      previous_progress_value: old_progress,
      previous_progress_status: goal_progress.progress_status,
      status: goal_progress.progress_status,
      source: 'milestones_aggregate',
      checkin_time: DateTime.current,
      checkin_type: 'current_time',
      system_comment: false,
      milestone_summary_method: milestone_summary_method,
    )

    goal_progress.update_goal_progress(aggregate_activity)
  end

  def completed_value_from_aggregate(aggregate_value)
    if last_value?
      aggregate_value
    elsif summation?
      goal_progress.progress_start + aggregate_value
    else
      contribution_from_milestones = (goal_progress.progress_target - goal_progress.progress_start) * aggregate_value
      goal_progress.progress_start + contribution_from_milestones
    end
  end

  def date_ranges_for_milestones_missing_start_or_target_values
    return unless has_milestones

    goal_milestones.joins(:goal_cycle)
      .where('goal_milestones.progress_start IS NULL OR goal_milestones.progress_target IS NULL')
      .pluck('goal_cycles.start_date', 'goal_cycles.end_date')
      .map { |start_date, end_date| { from: start_date, to: end_date } }
  end
end
