class ReviewerType < ApplicationRecord
  include Discard::Model
  include ReviewCycleUserRoleConcern
  include ReviewCycleVisibilityMatrixConcern

  default_scope -> { kept }
  has_paper_trail

  belongs_to :review_cycle
  has_many :reviewers, dependent: :destroy
  has_many :review_cycle_templates
  has_one :review_cycle_phase
  has_many :review_cycle_notification_recipients, dependent: :destroy
  has_one :review_cycle_user_role, dependent: :destroy
  has_many :review_cycle_visibility_matrices, dependent: :destroy

  validates :min_reviewers, :max_reviewers, numericality: { only_integer: true, greater_than_or_equal_to: 0 },
                                            allow_nil: true
  validate :min_reviewers_less_than_max_reviewers

  DEFAULT_REVIEWER_TYPES = %w[self peer direct_report manager].freeze
  SINGLE_REVIEWER_TYPES = %w[self manager].freeze
  WRITE_REVIEWS_DEFAULT_REVIEWER_TYPES = %w[self peer direct_report].freeze
  REVIEWER_TYPE_META = {
    'self' => { reviewer_type: 'self', first_person: 'Self', second_person: 'Self' },
    'peer' => { reviewer_type: 'peer', first_person: 'Peer', second_person: 'Peer', nomination_required: true },
    'direct_report' => { reviewer_type: 'direct_report', first_person: 'Direct Report', second_person: 'Manager',
                         autofill: true },
    'manager' => { reviewer_type: 'manager', first_person: 'Manager', second_person: 'Direct Report' },
  }.freeze

  validates :reviewer_type, uniqueness: { scope: :review_cycle }
  enum creation_source: { normal: 0, system: 1 }
  enum include_in_release_review: { dont_include: 0,
                                    include_anonymous: 1,
                                    include_identified: 2 }

  after_create :create_review_cycle_user_roles
  # TODO : udpate callback - if limit flag is changed, then discard or create direct report reviewers accordingly
  # add logic to a service and execute the service in delayed job
  after_update :queue_sync_direct_report_reviewers, if: :direct_report_type_changed?

  def has_competencies
    @_has_competencies ||= review_cycle_templates.left_joins(
      :review_cycle_template_questions, template: :template_questions
    ).where(
      "review_cycle_template_questions.block_type = 'competency' OR "\
      "template_questions.block_type = 'competency'",
    ).exists?
  end

  after_create do
    create_review_cycle_user_roles
    create_review_cycle_visibility_matrix_records
  end

  after_discard do
    reviewers.where(discarded_at: nil).where(reviewer_type: self).discard_all
    review_cycle_templates.where(discarded_at: nil).where(reviewer_type: self).discard_all
    discard_review_cycle_user_roles
    discard_review_cycle_visibility_matrix_records
  end

  after_undiscard do
    undiscard_review_cycle_visibility_matrix_records
    undiscard_review_cycle_user_roles
    review_cycle_templates.with_discarded.where(reviewer_type: self).undiscard_all
    reviewers.with_discarded.where(reviewer_type: self).undiscard_all
    create_review_cycle_visibility_matrix_records if review_cycle.review_cycle_user_roles.count * 2 != review_cycle_visibility_matrices.count  
  end

  def self.unique_reviewer_types(account)
    ReviewerType.joins(:review_cycle)
      .where(review_cycles: { account: account })
      .pluck(:reviewer_type)
      .uniq
  end

  def direct_report_type_changed?
    reviewer_type == 'direct_report' && saved_change_to_limit_to_participants?
  end

  def queue_sync_direct_report_reviewers
    PerformanceReview::WebPerformanceReview::SyncDirectReportReviewers.delay(queue: 'immediate').call(review_cycle_id)
  end

  private

  def min_reviewers_less_than_max_reviewers
    return if min_reviewers.blank? || max_reviewers.blank?

    errors.add(:min_reviewers, 'must be less than max reviewers') if min_reviewers > max_reviewers
    throw :abort
  end
end