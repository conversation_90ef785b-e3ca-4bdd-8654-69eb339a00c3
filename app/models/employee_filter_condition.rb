class EmployeeFilterCondition < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }
  has_paper_trail

  belongs_to :employee_filter

  before_save :sanitize_value

  private

  def sanitize_value
    if operator == 'IN'
      sanitized_value = json_value.map { |v| ActiveRecord::Base.connection.quote(v) }
      self.value = "(#{sanitized_value.join(',')})"
    else
      self.value = json_value.first
    end
  end
end
