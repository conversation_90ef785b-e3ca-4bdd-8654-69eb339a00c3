class Employee < ApplicationRecord

  # validates :timezone, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name), allow_blank: true }
  validates :user, presence: { if: -> { account.present? and !account.demo? } }

  has_many :pulses
  has_many :employee_pulses, class_name: 'Pulse', foreign_key: 'reportee_id', dependent: :destroy
  has_many :manager_pulses, class_name: 'Pulse', foreign_key: 'manager_id', dependent: :destroy
  belongs_to :user, optional: true, dependent: :destroy
  belongs_to :account
  belongs_to :department, optional: true
  has_one :slack_team, dependent: :destroy
  has_one :microsoft_team, dependent: :destroy
  has_many :employee_chats, dependent: :destroy
  has_many :direct_reports, class_name: 'Employee', foreign_key: 'manager_id'
  has_many :employee_reasons, class_name: 'EmployeeReasonCard', foreign_key: 'reportee_id'
  has_many :employee_checkin_comments, class_name: 'EmployeeReasonCard', foreign_key: 'reportee_id'
  has_many :employee_checkin_loggers, class_name: 'Employee<PERSON><PERSON>ckinLogger', foreign_key: 'reportee_id'
  has_many :employee_checkin_loggers, class_name: '<PERSON>p<PERSON><PERSON><PERSON><PERSON>ckin<PERSON>ogger', foreign_key: 'reportee_id'
  has_many :employee_one_on_one_loggers, class_name: 'EmployeeOneOnOneLogger', foreign_key: 'reportee_id'
  has_many :employee_checkin_loggers, class_name: 'EmployeeCheckinLogger', foreign_key: 'manager_id'
  has_many :employee_one_on_one_loggers, class_name: 'EmployeeOneOnOneLogger', foreign_key: 'manager_id'
  belongs_to :manager, class_name: 'Employee', optional: true # counter cache of reportees_count will include inactive employees.
  has_many :simple_chat_chatter, class_name: 'SimpleChat::SimpleChatChatter', as: :user
  has_many :completed_by_one_on_ones, class_name: 'OneOnOne', foreign_key: 'completed_by_id'
  has_many :manager_one_on_ones, class_name: 'OneOnOne', foreign_key: 'manager_id'
  has_many :reportee_one_on_ones, class_name: 'OneOnOne', foreign_key: 'reportee_id'
  has_many :creator_one_on_ones, class_name: 'OneOnOne', foreign_key: 'creator_id', dependent: :destroy
  has_many :super_list_items, foreign_key: 'reportee_id'
  has_many :super_list_items, foreign_key: 'manager_id'
  has_many :calendar_events, foreign_key: 'manager_id', dependent: :destroy
  has_many :calendar_settings, dependent: :destroy
  has_many :pulse_responses, dependent: :destroy
  has_many :key_result_checkins
  has_many :employee_goals
  has_many :created_feedbacks, class_name: 'Feedback', foreign_key: 'creator_id', dependent: :destroy
  has_many :feedback_templates, -> { where(template_type: :user) }, foreign_key: 'creator_id', 
    dependent: :destroy
  has_many :feedback_receivers, dependent: :destroy
  has_many :feedback_providers, dependent: :destroy
  has_many :feedback_requests, foreign_key: 'creator_id'
  has_many :goals, through: :employee_goals
  has_many :employee_data_imports, dependent: :destroy
  has_many :goal_filters, as: :filterable, dependent: :destroy
  has_many :added_by_goals, class_name: 'Goal', foreign_key: 'added_by_id'
  has_many :campaigns
  has_many :responses
  has_one :onboarding, dependent: :destroy
  has_many :attendees, class_name: 'MeetingAttendee', dependent: :destroy
  has_many :meetings, through: :attendees
  has_one :omnichat_user, dependent: :destroy
  has_many :coffee_connect_settings, dependent: :destroy
  has_many :schedule_respondents, foreign_key: 'respondent_id', dependent: :destroy
  has_many :schedules, through: :schedule_respondents
  has_many :employee_notification_settings
  has_many :created_schedules, class_name: 'Schedule', foreign_key: 'schedule_creator_id', dependent: :destroy
  has_many :performance_ratings
  has_many :home_page_actions
  has_many :csv_import_chunks
  has_one :reportee_pulse_template, lambda {
                                      where(template_type: 'reportee_template', owner_role: 'reportee')
                                    }, as: :owner, class_name: 'PulseTemplate', dependent: :destroy
  has_one :team_pulse_template, lambda {
                                  where(template_type: 'reportee_template', owner_role: 'manager')
                                }, as: :owner, class_name: 'PulseTemplate', dependent: :destroy
  has_one :manager_pulse_template, lambda {
                                     where(template_type: 'manager_template')
                                   }, as: :owner, class_name: 'PulseTemplate', dependent: :destroy
  belongs_to :calendar_event_organizer, class_name: 'Employee', optional: true
  has_many :calendar_event_organizer_clients, class_name: 'Employee', foreign_key: 'calendar_event_organizer_id'
  has_many :badges, class_name: 'CoffeeConnectGivenBadge', dependent: :destroy
  has_many :given_badges, class_name: 'CoffeeConnectGivenBadge', foreign_key: 'giver_id', dependent: :destroy
  has_many :goal_mentions
  has_many :created_review_cycles, class_name: 'ReviewCycle', foreign_key: 'creator_id', dependent: :destroy
  has_many :reviewees, dependent: :destroy
  has_many :reviewers, dependent: :destroy
  has_many :reviewer_nominations, class_name: 'Reviewer', foreign_key: 'nominator_id', dependent: :destroy
  has_many :reviewer_approvals, class_name: 'Reviewer', foreign_key: 'approver_id', dependent: :destroy
  has_many :reviewer_rejections, class_name: 'Reviewer', foreign_key: 'rejected_by_id', dependent: :destroy
  has_many :review_cycle_templates, dependent: :destroy
  has_many :reviewee_responses, class_name: 'ReviewResponse', foreign_key: 'reviewee_employee', dependent: :destroy
  has_many :reviewer_responses, class_name: 'ReviewResponse', foreign_key: 'reviewer_employee', dependent: :destroy
  has_one :goals_onboarding_checklist, dependent: :destroy
  has_many :added_tasks, class_name: 'Task', foreign_key: 'added_by_id'
  has_many :assigned_tasks, class_name: 'Task', foreign_key: 'owner_id'
  has_many :employee_departments, dependent: :destroy
  has_many :created_biz_reviews, class_name: 'BizReview', foreign_key: 'created_by_id', dependent: :destroy
  has_many :biz_reviews, as: :owner, dependent: :destroy
  has_many :employee_biz_review_action_items
  has_many :biz_review_action_items, through: :employee_biz_review_action_items
  has_many :integration_configs, foreign_key: 'added_by_id'
  has_many :created_kpis, foreign_key: 'added_by_id'
  has_many :employee_kpis
  has_many :kpis, through: :employee_kpis
  has_many :biz_review_comments, foreign_key: 'commented_by_id', dependent: :destroy
  has_many :mentions, dependent: :destroy
  has_many :acls, as: :actorable, dependent: :destroy
  has_many :acl_role_members, as: :member, dependent: :destroy
  has_many :template_categories, foreign_key: 'creator_id', dependent: :destroy
  has_many :metabase_groups, foreign_key: 'employee_id', dependent: :destroy
  has_one :goals_setting, dependent: :destroy
  has_many :employee_attributes, through: :account
  has_many :employee_attribute_values, dependent: :destroy

  # IDP Associations
  has_one :employee_idp_track_position, -> { where(current: true) }
  has_many :previous_employee_idp_track_positions, -> { where(current: false) }, class_name: 'EmployeeIdpTrackPosition'
  has_one :idp_track_position, through: :employee_idp_track_position
  has_many :previous_idp_track_positions, through: :previous_employee_idp_track_positions, source: :idp_track_position
  has_many :idp_track_position_competencies, through: :idp_track_position
  has_many :competencies, through: :idp_track_position

  has_many :nova_conversations, dependent: :destroy
  has_many :nova_chat_messages, through: :nova_conversations

  enum org_role: { employee: 'employee', admin: 'admin' }
  enum gender: { Female: 'Female', Male: 'Male', Unisex: 'Unisex', 'Non-binary': 'Non Binary', 'Other': 'Other'}
  enum pulse_frequency: { before_one_on_one: 'before_one_on_one', weekly: 'weekly', fortnight: 'fortnight',
                          monthly: 'monthly' }
  scope :invited, -> { where(invited: true) }
  enum cc_meeting_pref: { one_on_one: 0, multi_people: 1 }
  enum billing_status: { active: 'active', inactive: 'inactive', to_be_activated: 'to_be_activated' }

  mount_uploader :profile_picture, ProfilePictureUploader
  acts_as_tagger

  accepts_nested_attributes_for :user, update_only: true

  # has_many :omnichat_conversations

  before_create :generate_first_name
  # after_create :sync_team_user_details
  # after_update :sync_team_user_details
  # after_create :update_manager_reportees_count
  after_create :connect_omnichat_user
  after_create :validate_date_of_exit
  before_update :generate_first_name
  after_update :create_goals_onboarding_checklist
  # after_save :update_manager_reportees_count

  after_create :set_role_based_admin
  after_create :create_employee_goals_setting
  before_update :update_role_based_admin
  after_update :update_date_of_exit
  after_update :update_employee_goals_setting, if: -> { saved_change_to_attribute?(:timezone) }
  after_update :trigger_manager_change_event, if: -> { saved_change_to_attribute?(:manager_id) }

  ANONYMOUS_MINIMUM = 5
  ANONYMOUS_MAXIMUM = 30

  attr_reader :emp_id
  has_paper_trail

  # scope :personal_conversations, ->(id) {
  # def as_json(options = {})
  #   {only: [:id, :created_at, :full_name, :first_name, :date_of_joining, :department, :gender, :location], methods: [:user, :account]}
  # end
  

  def profile_picture_url
    profile_picture&.thumb&.url
  end

  def manager_change_history
    versions.where("object_changes LIKE '%manager_id%'")
      .order(created_at: :desc)
      .map do |version|
        changes = version.object_changes['manager_id']
        next unless changes

        {
          changed_at: version.created_at,
          changed_by: version.whodunnit.present? ? User.find_by(id: version.whodunnit)&.email : nil,
          from_manager: changes[0].present? ? Employee.find_by(id: changes[0])&.user&.email : nil,
          to_manager: changes[1].present? ? Employee.find_by(id: changes[1])&.user&.email : nil,
        }
      end.compact
  end

  def acl_role_admin?(product_action_keys: [])
    acl_admin_condition = acl_role_ids.present?
    if product_action_keys.present?
      acl_admin_condition &&= product_action_keys.any? { |perm| can_access_product?(perm) }
    end
    org_role == 'admin' || acl_admin_condition
  end

  def display_name
    (first_name || full_name).capitalize
  end

  def direct_reportees
    direct_reports.active
  end

  def full_name_with_email
    %("#{(full_name || first_name)&.capitalize}" <#{user.email}>)
  end

  def invited_reports
    direct_reports.active.where(invited: true)
  end

  def relationship_updated_reports
    direct_reports.where(relationship_updated: true)
  end

  def sync_team_user_details
    slack_team_id = account.slack_team&.id
    if account.messaging_client == 'slack' && slack_team_id.present? &&
        Delayed::Job.where("handler LIKE ?", "%FetchSlackUsersDetails%")
                    .where("handler LIKE ?", "%#{slack_team_id}%").blank?
      FetchSlackUsersDetails.delay(queue: 'high').call(slack_team_id)
    end
  end

  # If there is a loop in the reporting structure, this would fail
  # to get the employee heirachical based on reportes
  # done recurisve => 1>2>3>4>5..etc, 1.manager.manager.manager, etc
  # add depth to stop loop is => 1>2>3>4>5>1>2>3>4>5>1>2...etc, looping .managers
  # limit to a depth of 10 increased with each call/iteration, 10 is an assumption could be 20
  def team(depth: 0)
    Rails.cache.fetch("employee/#{id}/team") do
      if direct_reports.length == 0 || depth > 10
        return []
      else
        team_list = direct_reports
        direct_reports.each do |reportee|
          team_list += reportee.team(depth: depth + 1)
        end
        return team_list
      end
    end
  end

  def self.team_ids(current_employee_id = nil, depth = 0)
    return [] if current_employee_id.nil? || depth > 10

    direct_report_ids = Employee.where(manager_id: current_employee_id).pluck(:id)
    final_data = direct_report_ids || []

    direct_report_ids.each do |dr_id|
      final_data += team_ids(dr_id, depth + 1)
    end

    final_data
end

  def email_address
    "#{display_name} <#{user.email}>"
  end

  def photo
    index = (id % 4) + 1
    prefix = 'male'
    filename = if gender.present? && %w[male female].include?(gender.downcase)
                 "#{gender.downcase}-0#{index}.svg"
               else
                 "#{prefix}-0#{index}.svg"
               end

    "https://peoplebox-assets.s3.amazonaws.com/assets/default_avatars/#{filename}"
  end

  def generate_first_name
    name_parts = full_name.split(' ')
    first_name = if name_parts.first.length < 3
                   full_name
                 else
                   name_parts.first
                 end

    self.first_name = first_name
  end

  def send_sms(template, data)
    requested_url = 'http://api.textlocal.in/send/?'

    sms_template = File.read(Rails.root.join('app', 'views', 'sms', "#{template}.txt"))
    message = Liquid::Template.parse(sms_template).render(data)

    # if user.present?
    #   SmsLog.create!(user: user, message: message, phone_number: phone_number)
    # end
    uri = URI.parse(requested_url)
    http = Net::HTTP.start(uri.host, uri.port)
    request = Net::HTTP::Get.new(uri.request_uri)

    primary_phone_number = '91' + self.primary_phone_number.split(//).last(10).join unless self.primary_phone_number.nil?
    secondary_phone_number = '91' + self.secondary_phone_number.split(//).last(10).join unless self.secondary_phone_number.nil?
    phone_numbers = "#{primary_phone_number}, #{secondary_phone_number}"

    phone_numbers = '************' if Rails.env.development? # Arpit & Alagu's number

    data = { 'username' => '<EMAIL>',
             'hash' => '2d19a491f8a8a78d68493dced0818e8c6616e69853b2a102c34ca2f18dc9ffa8',
             'message' => message,
             'sender' => 'CULTRE',
             'numbers' => phone_numbers }

    # return if Rails.env.development?
    res = Net::HTTP.post_form(uri, data)
    response = JSON.parse(res.body)
  end

  def update_first_name(full_name)
    name_parts = full_name.split(' ')
    if name_parts.first.length < 3
      full_name
    else
      name_parts.first
    end
  end

  def manager_name
    manager.full_name if manager.present? && manager.full_name.present?
  end

  def last_pulse
    pulse = Pulse.where(employee_id: id).order('end_date desc')
    return pulse.first if pulse
  end

  def pulse_frequency_days
    days = { before_one_on_one: 0,
             weekly: 7,
             fortnight: 14,
             monthly: 30 }

    days[pulse_frequency.to_sym]
  end

  # manager pulse count
  def manager_pulse_count
    pulses.where(pulse_type: 1).count
  end

  # employee pulse count
  def reportee_pulse_count
    pulses.where(pulse_type: 0).count
  end

  # get employee timezone
  # fallback to account timezone if employee timezone is null
  def get_timezone
    timezone || account.timezone || 'Asia/Calcutta'
  end

  def connect_omnichat_user
    chat_user = OmnichatUser.where(email: user.email, userable: (account.slack_team || account.microsoft_team)).last
    return unless chat_user

    chat_user.update_attributes(employee: self)
  end

  def self.personal_conversations(id)
    return if OmnichatUser.where(employee_id: id).none?

    OmnichatConversation.joins("
      INNER JOIN omnichat_users on
        omnichat_conversations.conversationable_id = omnichat_users.id AND
        omnichat_conversations.conversationable_type = 'OmnichatUser'
    ").where(omnichat_users: { employee_id: id }, conversation_category: 'personal')
  end

  # do it in after save to include only active employees
  def update_manager_reportees_count
    return unless saved_change_to_attribute?(:manager_id) || saved_change_to_attribute?(:date_of_exit)

    # New manager
    manager = self.manager
    if manager.present?
      manager.reportees_count = manager.direct_reports.active.length
      manager.save
    end

    return if manager_id_previous_change.blank?

    old_manager = Employee.find(manager_id_previous_change.first)
    return unless old_manager.present?

    old_manager.reportees_count = old_manager.direct_reports.active.length
    old_manager.save
  end

  def update_reportee_count
    self.reportees_count = direct_reports.active.length
    save
  end

  # def all_reports
  #   self.direct_reports.map(&:id) | self.direct_reports.map(&:all_reports).flatten.compact
  # end

  def all_reports
    childs_to_visit = direct_reports.to_a
    childs_to_return = []
    while childs_to_visit.present?
      current_node = childs_to_visit.shift
      next if childs_to_return.include?(current_node) || current_node == self

      childs_to_return << current_node
      childs_to_visit.concat(current_node.direct_reports)
    end
    childs_to_return
  end

  def hierarchical_role
    if org_role == 'admin'
      org_role
    else
      direct_report_managers = direct_reports.active.where('reportees_count > 0')
      if direct_report_managers.length > 0
        'manager_of_manager'
      elsif reportees_count.to_i > 0
        'manager'
      else
        'employee'
      end
    end
  end

  def org_chart
    children = []
    direct_reports.active.each do |report|
      children << report.org_chart
    end

    {
      id: id,
      name: full_name,
      children: children,
    }
  end

  def create_goals_onboarding_checklist
    return unless org_role == 'admin'

    goal_added = added_by_goals.present?
    progress_checkin_done = goal_added == true ? added_by_goals.any? { |goal| goal.goal_activities.where(employee: self).present? } : false
    GoalsOnboardingChecklist.find_or_create_by(employee: self)
      .update(goal_added: goal_added, progress_checkin_done: progress_checkin_done, explored_demo: false, team_invited: false)
  end

  def set_role_based_admin
    if self.org_role == 'admin'
      self.update(account_admin: true, goal_admin: true)
    end
  end

  def update_role_based_admin
    if self.changes.include? "org_role"
      if self.org_role == 'admin'
        self.account_admin = true
        self.goal_admin = true
      elsif self.org_role == 'employee'
        self.account_admin = false
        self.goal_admin = false
      end
    end
  end

  def is_active?
    billing_status == 'active'
  end

  ['edit', 'view', 'delete', 'comment'].each do |permission_type|
    define_method("can_#{permission_type}?") do |resource|
      Views::Permissions.where(
        resource_id: resource.id,
        resource_type: resource.class.to_s,
        permission_type: Acl.permission_types[permission_type],
        employee_id: id,
      ).exists?
    end
  end

  def employee_specific_access?(resource)
    acls.exists?(resource: resource)
  end

  def update_date_of_exit
    if saved_change_to_attribute?(:date_of_exit)
      previous_value, current_value = saved_changes[:date_of_exit]

      if current_value.present? && current_value.to_datetime <= Time.zone.now
        update(billing_status: 'inactive')
      end
    end

    if saved_change_to_attribute?(:billing_status)
      previous_status, current_status = saved_changes[:billing_status]
      return if previous_status == current_status

      if current_status == 'active'
        update(activated_at: Time.current)
      else
        update(deactivated_at: Time.current)
      end
    end
  end

  def validate_date_of_exit
    if self.date_of_exit.present? && self.date_of_exit.to_datetime  <= Time.zone.now
      self.update(billing_status: 'inactive')
    end
  end

  def acl_role_ids
    acc_acl_role_members = AclRoleMember.where(acl_role_id: AclRole.where(account_id: account_id).pluck(:id).uniq)
    emp_acl_role_ids = acc_acl_role_members.where(member_type: 'Employee', member_id: id).pluck(:acl_role_id).uniq

    ef_acl_role_ids = acc_acl_role_members.where(member_type: 'EmployeeFilter').select(
      :member_id, :acl_role_id
    ).select do |arms|
      EmployeeFilter.apply(arms.member_id, base_criteria: Employee.where(id: id)).any?
    end.pluck(:acl_role_id).uniq

    (emp_acl_role_ids + ef_acl_role_ids).uniq
  end

  def acl_role_names
    AclRole.where(id: acl_role_ids).pluck(:name).uniq
  end

  def accessible_product_actions
    Acl.joins(
      "INNER JOIN product_actions pa ON acls.resource_type = 'ProductAction' AND acls.resource_id = pa.id",
    ).where(actorable_type: 'AclRole', actorable_id: acl_role_ids).pluck('pa.key').uniq
  end

  def accessible_sub_product_actions
    AclRoleSubAction.joins(:acl_role, :product_action).where(acl_roles: { id: acl_role_ids }).distinct.pluck(:key).uniq
  end

  def can_access_product?(product_action_key)
    return true if admin? || acl_role_ids.blank?

    product_action = ProductAction.find_by(key: product_action_key)
    AccountProduct.exists?(account_id: account_id, product_id: product_action.product_id) &&
      Acl.exists?(
        actorable_type: 'AclRole',
        actorable_id: acl_role_ids,
        resource: product_action,
      )
  end

  def custom_role_admin_for?(key_snippet)
    # Please use this method carefully. This is to check if the employee is a custom role admin for a
    # specific key_snippet. You don't need to pass the whole product_action_key you can pass a small snippet instead.
    # This method can be used for when you need to check if the employee is a custom role admin for a specific module in
    # the reporting module. For example, we can check if the employee is custom role admin of reporting AND has access to the
    # one_on_ones module inside the reporting module. Make sure the snippet you pass is comprehensive enough so that it only match
    # with whatever you are trying to match.
    Acl.joins(
      "INNER JOIN product_actions pa ON acls.resource_type = 'ProductAction' AND acls.resource_id = pa.id",
    ).where(actorable_type: 'AclRole', actorable_id: acl_role_ids).where('pa.key LIKE ?', "%#{key_snippet}%" ).exists?
  end

  def goals_acls
    @_goals_acls ||= begin
      access_service = Acls::Goals::Access.call(self, product_action_key: 'goals_grant_all_permissions')
      access_service.success? ? access_service.result : {}
    end
  end

  def sub_product_admin_for?(sub_product_action_key)
    AclRoleSubAction.joins(:acl_role, :product_action).where(acl_roles: { id: acl_role_ids }).where(product_actions: { key: sub_product_action_key }).exists?
  end

  def can_manage_goal?(goal)
    return true if goals_acls[:super_admin]

    goals_acls[:access] &&
      (
        goal.employee_goals.where(
          ActiveRecord::Base.sanitize_sql_for_conditions(
            ["employee_goals.employee_id IN (#{goals_acls[:employee_targetable_sql].presence || 'NULL'})"],
          ),
        ).exists? ||
          goals_acls[:goals_targetable].exists?(id: goal.id)
      )
  end

  def okr_updates_url(only_path = false)
    base_url = if only_path
                 "/dashboard/goals/employees/#{self.id}"
               else
                 "https://#{ENV['APP_DOMAIN']}/dashboard/goals/employees/#{self.id}"
               end

    service = OkrUpdates::Create.call(self, self)
    if service.success?
      "#{base_url}?okr-update-id=#{service.result[:id]}"
    else
      base_url
    end
  end

  def create_employee_goals_setting
    return if account.company_goals_setting.blank?

    GoalsSettings::CreateOrUpdateEmployeeGoalsSettings.delay(queue: 'normal').call(account.company_goals_setting, self)
  end

  def update_employee_goals_setting
    setting = goals_setting
    return if setting.blank?

    day = setting.next_reminder_at
    time = setting.time
    tz_offset = ActiveSupport::TimeZone[timezone].formatted_offset
    next_reminder_at = "#{day.year}-#{day.month}-#{day.day} #{time} #{tz_offset}"

    setting.update(next_reminder_at: next_reminder_at)
  end

  def trigger_manager_change_event
    previous_manager_id, new_manager_id = saved_changes[:manager_id]

    # Skip if both values are nil (no actual change)
    return if previous_manager_id.nil? && new_manager_id.nil?

    # Create the event
    event = Events::Definitions::EmployeeManagerIdChangedDefinition.new(
      data: {
        employee_id: id,
        previous_manager_id: previous_manager_id,
        new_manager_id: new_manager_id
      }.with_indifferent_access
    )

    # Publish the event to the employee stream (asynchronously to avoid blocking the transaction)
    Events::Publisher.publish(
      event: event,
      stream_name: EventStore::StreamNames.for_employee(id),
      metadata: {
        triggered_by: 'employee_model_callback',
        account_id: account_id
      },
      async: true  # Asynchronous execution for background processing
    )
  rescue StandardError => e
    Rails.logger.error("Error triggering manager change event for employee #{id}: #{e.message}")
    Sentry.capture_exception(e) if defined?(Sentry)
  end

  def event_flow(range = nil)
    ManagerChanges::EventFlowVisualizer.new(self, range).execution_flow
  end

  def print_event_flow(formatter: :json, range: nil)
  
    ManagerChanges::EventFlowVisualizer.new(self, range).print_flow(formatter: formatter)
  end

  def role_type
    if manager_id && direct_reportees.none?
      'Individual Contractor'
    elsif manager_id.nil? && direct_reportees.any?
      'Manager of Managers'
    elsif manager_id && direct_reportees.any?
      'Manager'
    end
  end

  def self.profile_picture_url(employee_id, file_name)
    emp = Employee.new(id: employee_id)
    emp.write_attribute(:profile_picture, file_name)
    uploader = ProfilePictureUploader.new(emp, :profile_picture)
    uploader.retrieve_from_store!(file_name) if file_name.present?
    uploader.thumb.url
  end

  def employee_attributes
    # This method is overwridden to ensure default attributes are created if none are there already.
    super.presence || account.employee_attributes
  end

  def superiors_with_positions
    # This query returns the superiors of the employee with their full name, id, and position in the hierarchy.
    # This query's last item will always be the employee itself.
    sql = <<~SQL.squish
      WITH RECURSIVE employee_superiors AS (
        SELECT id, manager_id, full_name, CAST(id AS CHAR(200)) AS path, 1 AS level
        FROM employees
        WHERE id = #{self.id}
          AND billing_status = 'active'
          AND account_id = #{self.account_id}

        UNION ALL

        SELECT e.id, e.manager_id, e.full_name, CONCAT(es.path, ',', e.id), es.level + 1
        FROM employees e
        INNER JOIN employee_superiors es ON e.id = es.manager_id
        WHERE e.billing_status = 'active'
          AND FIND_IN_SET(e.id, es.path) = 0
          AND e.account_id = #{self.account_id}
      )
      SELECT id, full_name, level FROM employee_superiors
      ORDER BY level DESC;
    SQL

    ActiveRecord::Base.connection.exec_query(sql).rows
  end

  def superiors
    sql = <<~SQL.squish
      WITH RECURSIVE employee_superiors AS (
        SELECT id, manager_id, CAST(id AS CHAR(200)) AS path
        FROM employees
        WHERE id = #{self.id}
          AND billing_status = 'active'
          AND account_id = #{self.account_id}
        UNION ALL

        SELECT e.id, e.manager_id, CONCAT(es.path, ',', e.id)
        FROM employees e
        INNER JOIN employee_superiors es ON e.id = es.manager_id
        WHERE e.billing_status = 'active'
          AND FIND_IN_SET(e.id, es.path) = 0
          AND e.account_id = #{self.account_id}
      )
      SELECT id FROM employee_superiors
      WHERE id != #{self.id}
    SQL

    ActiveRecord::Base.connection.exec_query(sql).rows.flatten!
  end

  def mapped_superiors_with_positions
    superiors_with_positions.map do |superior|
      {
        id: superior[0],
        full_name: superior[1]
      }
    end
  end

  def relations_to(employee)
    # This is a generic method to get the relation between two employees.
    # It only returns a string and ideally should not be used for dynamic role based access control.
    return 'no_direct_relation' if (employee.inactive? || self.inactive?)

    case
    when employee.manager_id == self.id
      'manager'
    when self.manager_id == employee.id
      'reportee'
    when employee.superiors&.include?(self.id)
      'indirect_manager'
    when self.manager&.active? && self.manager&.direct_reports&.include?(employee)
      'peer'
    else
      'no_direct_relation'
    end
  end

  def all_relations_to(employee)
    # This is a specific method to get all relations between two employees.
    # This method returns an array of all relations between the two employees.
    # This array can be used for dynamic role based access control.
    employee_relations = ['all_employees']
    employee_relations << relations_to(employee)&.pluralize
    employee_relations << 'admins' if admin?
    employee_relations << 'self' if self == employee

    employee_relations
  end

  def can_access_one_on_one_notes?(one_on_one)
    ooo_manager = one_on_one.manager
    ooo_reportee = one_on_one.reportee

    return false if ooo_manager.account_id != self.account_id
    return true if (ooo_reportee.id == self.id || ooo_manager.id == self.id) # always enabled for participants

    relations_to_reportee = all_relations_to(ooo_reportee)
    relations_to_manager = all_relations_to(ooo_manager)
    all_relations = (relations_to_reportee + relations_to_manager).uniq # current relations to the reportee and manager
    settings = self.account.one_on_one_setting

    config_json = settings.shared_notes_visibility_config_json.with_indifferent_access
    all_relations.each do |relation|
      return true if config_json[relation].present? && config_json[relation]['enabled']
    end

    false
  end
end
