class ReviewCycleNotificationRecipient < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }
  has_paper_trail
  belongs_to :review_cycle
  belongs_to :review_cycle_phase
  belongs_to :review_cycle_notification
  belongs_to :employee
  belongs_to :reviewer_type, optional: true
  enum status: { skipped: 'skipped', task_assigned: 'task_assigned' }

  # backfill_type column distinguishes split records which was created by running backward compatibility script
  # therefore delivered_at < created_at for newly created records
  # https://www.notion.so/peoplebox/Backward-Compatibility-script-to-fix-the-delivered_at-column-in-the-split-records-2332beb89af18069b4f7c39a24e47f89
  enum backfill_type: { reviewer_type_split: 'reviewer_type_split', updation_failed: 'updation_failed'}

  after_discard :discard_review_cycle_notification

  private

  # here discarding review cycle notification
  # only if its associated all review cycle notification recipients gets discarded
  # return if review_cycle_notification.discarded? This avoids redundant discard calls during cascading callbacks.
  # Example: when ReviewCycleNotification is discarded first, it discards all recipients.
  # Each recipient then triggers this callback, which would try to discard the notification again.
  # We guard against that using an early return.

  def discard_review_cycle_notification
    return if review_cycle_notification.nil?
    return if review_cycle_notification.discarded?
    return if notification_recipients_exist?

    review_cycle_notification.discard
  end

  def notification_recipients_exist?
    ReviewCycleNotificationRecipient
      .unscoped.kept
      .where(review_cycle_notification_id: review_cycle_notification_id)
      .exists?
  end
end
