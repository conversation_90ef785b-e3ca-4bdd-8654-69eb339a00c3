# frozen_string_literal: true

module ReviewerMethods
  extend ActiveSupport::Concern

  included do
    def review_template
      parsed_attribute_value = case reviewer_type.creation_source
                               when 'normal'
                                 parse_normal_creation_attribute
                               when 'system'
                                 parse_system_creation_attribute
                               end

      template = reviewer_type.review_cycle_templates.find_by(employee_attribute: parsed_attribute_value)
      return template if template.present?

      raise ::PerformanceReview::ReviewTemplateNotFoundError,
            "review_cycle='#{review_cycle_id}', reviewer='#{id}', "\
            "reviewer_type='#{reviewer_type_id}', employee_attribute='#{parsed_attribute_value}'"
    end

    def parse_normal_creation_attribute
      reviewee_employee_attribute = reviewee.custom_attribute

      if reviewee_employee_attribute.to_i.to_s == reviewee_employee_attribute.to_s
        reviewee_employee_attribute
      else
        reviewee_employee_attribute&.strip&.downcase
      end
    end

    def parse_system_creation_attribute
      snapshot = reviewee_snapshots.first
      snapshot&.custom_attribute
    end
  end
end
