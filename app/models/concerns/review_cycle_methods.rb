# frozen_string_literal: true

module ReviewCycleMethods
  extend ActiveSupport::Concern

  included do
    has_many :reviewer_types, dependent: :destroy

    def reviewer_types_summary
      self_reviewer_type = reviewer_types.find_by(reviewer_type: 'self')
      manager_reviewer_type = reviewer_types.find_by(reviewer_type: 'manager')
      peer_reviewer_type = reviewer_types.find_by(reviewer_type: 'peer')
      direct_report_reviewer_type = reviewer_types.find_by(reviewer_type: 'direct_report')
      custom_reviewer_types = reviewer_types.normal.where.not(reviewer_type: ReviewerType::DEFAULT_REVIEWER_TYPES)

      {
        goals_selection_by_reviewee: {
          enabled: self_reviewer_type&.define_goals.present?,
        },
        goals_approval_by_manager: {
          enabled: self_reviewer_type&.goal_approval_required.present?,
        },
        goals_selection_and_approval_by_manager: {
          enabled: manager_reviewer_type&.define_goals.present? &&
            manager_reviewer_type&.goal_approval_required.present?,
        },
        peer_review: {
          enabled: peer_reviewer_type.present?,
          min_reviewers: peer_reviewer_type&.min_reviewers,
          max_reviewers: peer_reviewer_type&.max_reviewers,
          limit_to_participants: peer_reviewer_type&.limit_to_participants.nil? || peer_reviewer_type&.limit_to_participants,
          selection_required: peer_config&.selection_required || false,
          selection_role: peer_config&.peer_selection_role&.role_type || nil,
          approval_required: peer_config&.approval_required || false,
          approval_role: peer_config&.peer_approval_role&.role_type || nil,
        },
        write_self_review: {
          enabled: self_reviewer_type.present?,
        },
        write_peer_review: {
          enabled: peer_reviewer_type.present?,
          can_review_anyone: peer_reviewer_type&.can_review_anyone? || false,
          standalone_launch: peer_reviewer_type&.standalone_launch?,
        },
        write_direct_report_review: {
          enabled: direct_report_reviewer_type.present?,
          limit_to_participants: direct_report_reviewer_type&.limit_to_participants.nil? || direct_report_reviewer_type&.limit_to_participants,
          standalone_launch: direct_report_reviewer_type&.standalone_launch?,
        },
        write_manager_summary_review: { enabled: manager_reviewer_type.present? },
        goal_in_self_review: { enabled: self_reviewer_type&.include_goals.present? },
        goal_in_manager_review: { enabled: manager_reviewer_type&.include_goals.present? },
        goal_in_peer_review: { enabled: peer_reviewer_type&.include_goals.present? },
        goal_in_direct_report_review: {
          enabled: direct_report_reviewer_type&.include_goals.present?,
        },
        goal_in_custom_review: custom_reviewer_types.map do |type|
          {
            reviewer_type_id: type.id,
            reviewer_type: type.reviewer_type,
            reviewer_label: type.first_person,
            reviewee_label: type.second_person,
            enabled: type.include_goals == true,
          }
        end,
        auto_create_one_on_one: {
          options: ReviewCycle.auto_create_one_on_ones,
          enabled: auto_create_one_on_one,
        },
        review_visibility: {
          self_review: {
            enabled: reviewer_type_visibility_on_pdf(self_reviewer_type),
          },
          peer_review: {
            enabled: reviewer_type_visibility_on_pdf(peer_reviewer_type),
          },
          manager_review: {
            enabled: reviewer_type_visibility_on_pdf(manager_reviewer_type),
          },
          direct_report_review: {
            enabled: reviewer_type_visibility_on_pdf(direct_report_reviewer_type),
          },
          custom_review: custom_reviewer_types.map do |type|
            {
              reviewer_type_id: type.id,
              display_name: type.reviewer_type.gsub('_', ' ').capitalize,
              reviewer_type: type.reviewer_type,
              reviewer_label: type.first_person,
              reviewee_label: type.second_person,
              enabled: type.include_anonymous? || type.include_identified?,
              standalone_launch: type.standalone_launch,
            }
          end,
        },
        define_goal_weights: {
          enabled: reviewer_types.exists?(
            reviewer_type: ReviewerType::SINGLE_REVIEWER_TYPES,
            define_goal_weights: true,
          ),
        },
        release_reviews: {
          enabled: review_cycle_phase == 'release_summary',
        },
        custom_reviewer_types: custom_reviewer_types.map do |type|
          {
            reviewer_type_id: type.id,
            display_name: type.reviewer_type.gsub('_', ' ').capitalize,
            reviewer_type: type.reviewer_type,
            reviewer_label: type.first_person,
            reviewee_label: type.second_person,
            can_read_reviews: type.can_read_reviews?,
            can_review_anyone: type.can_review_anyone?,
            standalone_launch: type.standalone_launch,
          }
        end,
        competency_configs: {
          enabled: competency_required?,
          is_weights_enabled: competency_weights_required?,
        },
        calibration_view: {
          manager: {
            enable_calibration_view: manager_reviewer_type&.enable_calibration_view,
            enable_nine_box: manager_reviewer_type&.enable_nine_box,
          },
        },
        goal_configs: {
          goal_visibility: goal_visibility,
          review_cycle_goal_type: goal_config_json.review_cycle_goal_type,
          minimum_goal_weightage: goal_config_json.minimum_goal_weightage,
          maximum_goal_weightage: goal_config_json.maximum_goal_weightage,
          allow_zero_weightage_goals: goal_config_json.allow_zero_weightage_goals,
        },
        enforce_manager_change: {
          enabled: enforce_system_manager_mapping.present?,
          end_date: enforce_system_manager_mapping,

        },
      }
    end

    def user_roles
      review_cycle_user_roles.map do |role|
        role.slice(:role_type, :review_cycle_id, :reviewer_type_id, :id)
      end
    end

    def summary_label_suffix(type)
      if type.include_goals && !type.has_competencies
        'with Goals'
      elsif type.has_competencies && !type.include_goals
        'with Competency'
      elsif type.include_goals && type.has_competencies
        'with Competency and Goals'
      else
        ''
      end
    end

    def review_cycle_summary
      {
        write_review: reviewer_types.map do |type|
          case type.reviewer_type
          when 'self'
            ['Self Review', summary_label_suffix(type)].join(' ')
          when 'peer'
            ['Peer Review', summary_label_suffix(type)].join(' ')
          when 'direct_report'
            ['Upward Review', summary_label_suffix(type)].join(' ')
          else
            ["#{type.first_person} Review", summary_label_suffix(type)].join(' ')
          end
        end,
        peer_evaluation: reviewer_types.each_with_object([]) do |type, store|
          next unless type.reviewer_type == 'peer'

          if peer_config&.selection_required
            selection_label = peer_selection_role == 'self' ? 'Peers Chosen by Reviewees' : 'Peers Assigned by Manager'
            store << selection_label
          end

          if type.approval_required || peer_config&.approval_required
            store << 'Peer Approval by Manager'
          end
        end,
        goal_evaluation: reviewer_types.each_with_object([]) do |type, store|
          case type.reviewer_type
          when 'self', 'manager', ReviewerType::DEFAULT_REVIEWER_TYPES.exclude?(type.reviewer_type)
            store << 'Goal Selection' if type.define_goals
            store << 'Goal Approval' if type.goal_approval_required
          end
          store.uniq!
        end,
        # we dont have phases for now, uncomment later when phases come into play
        # competency_evaluation: reviewer_types.each_with_object([]) do |type, store|
        #   case type.reviewer_type
        #   when 'self', 'manager'
        #     store << 'Competency Selection' if type.define_competency
        #     store << 'Competency Approval' if type.competency_approval_required
        #   else
        #     if ReviewerType::DEFAULT_REVIEWER_TYPES.exclude?(type.reviewer_type)
        #       store << 'Competency Selection' if type.define_competency
        #       store << 'Competency Approval' if type.competency_approval_required
        #     end
        #   end
        #   store
        # end,
        manager_summary: reviewer_types.each_with_object([]) do |type, store|
          case type.reviewer_type
          when 'manager'
            store << ['Downward Review', summary_label_suffix(type)].join(' ')
          end
          store
        end,
      }
    end

    def review_cycle_goal_types
      goal_config_json.review_cycle_goal_types
    end

    def review_cycle_goal_type
      {
        goal_type: goal_config_json.review_cycle_goal_type,
      }
    end

    def reviewer_type_visibility_on_pdf(reviewer_type)
      return false if reviewer_type.blank?

      reviewer_type.include_anonymous? || reviewer_type.include_identified?
    end

    def peer_selection_role
      return 'self' if peer_config.nil? || peer_config&.peer_selection_role.nil?

      peer_config&.peer_selection_role&.role_type
    end

    def peer_selection_required
      return false if peer_config.nil? || peer_config&.peer_selection_role.nil?

      peer_config&.selection_required
    end

    def peer_approval_role
      return 'manager' if peer_config.nil? || peer_config&.peer_approval_role.nil?

      peer_config&.peer_approval_role&.role_type
    end

    def peer_approval_required
      return false if peer_config.nil? || peer_config&.peer_approval_role.nil?

      peer_config&.approval_required
    end

    def admin_role
      review_cycle_user_roles.find_by(role_type: 'admin')
    end
  end
end
