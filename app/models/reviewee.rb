# == Schema Information
#
# Table name: reviewees

#  review_cycle_id             :bigint           Review Cycle that Reviewee Belongs to
#  employee_id                 :bigint           Employee Reference of the Reviewee
#  manager_id                  :bigint           Copy of Manager Id in Reviewee as the manager might change after review cycle.
#  peer_selection_done         :boolean          When peer selection is done by reviewee employee
#  peer_approval_done          :boolean          When peer approval is done by manager
#  goal_selection_done         :boolean          When goal selection is done by reviewee
#  goal_approval_done          :boolean          When goal approval is done by manager
#  self_review_done            :boolean          When self review is done by employee
#  manager_summary_done        :boolean          When manager review is done by manager
#  review_writing_percent      :float            Calculated float value where denominator is # of reviews, numerator is completed ones
#  release_review_done         :boolean          When manager has sent the pdf to the employee
#  confidential                :boolean          To hide the PDF & Data to be hidden as they might be senior leaders
#  custom_attribute            :string           Custom attribute value copy from employee table for field mentioned in template_employee_attribute in review_cycle table.
#  custom_variables            :json             JSON hash of all custom variables to be used in calculated field. Eg: Manager Score Weight, etc
#  one_on_one_id               :bigint           Foreign Key Reference when a 1-on-1 is created for this reviewee.

class Reviewee < ApplicationRecord
  include Discard::Model
  include PerformanceReview::Reviewees::DiscardableRevieweeNotification
  include Reviewees::AutomationTrigger
  default_scope -> { kept }
  has_paper_trail
  belongs_to :review_cycle
  belongs_to :employee
  belongs_to :manager, class_name: 'Employee', optional: true
  has_many :reviewers, dependent: :destroy
  has_many :review_responses, dependent: :destroy
  has_many :reviewee_goals, dependent: :destroy
  belongs_to :one_on_one, optional: true
  has_many :home_page_actions, as: :pageable, dependent: :destroy
  has_many :reviewee_competencies, dependent: :destroy
  has_one :review_summary, dependent: :destroy
  has_one :skip_reviewer, dependent: :destroy
  has_many :review_cycle_calibration_comments
  has_many :calibrator_calibratees, foreign_key: :calibratee_id
  has_many :reviewee_goal_event_logs, dependent: :destroy
  has_many :reviewee_snapshots, dependent: :destroy
  attr_accessor :skip_after_create

  before_validation :clean_custom_attribute
  validates :employee,
            uniqueness: { scope: [:review_cycle, :discarded_at],
                          message: 'performance should be reviewed once in a review cycle' }
  after_create :set_current_manager
  after_create :create_default_reviewers, unless: :skip_callbacks

  after_update :remove_reviewers_responses
  after_update :update_reviewee_goals
  after_update :trigger_manager_change_event, if: :saved_change_to_manager_id?

  # TODO: Handle creation of new calibrator_calibratee when reviewee is added from tool after review cycle is live
  # TODO: Handle discard of calibrator_calibratee when reviewee is removed from review cycle

  def any_goal_form_submitted?
    reviewers.joins(:review_responses).where.not(review_responses: { goal_id: nil }).exists?(review_submitted: true)
  end

  def remove_reviewers_responses
    return unless saved_change_to_attribute?(:custom_attribute)

    previous_value, current_value = saved_changes[:custom_attribute]
    return if previous_value == current_value
    return if previous_value&.strip&.downcase == current_value&.strip&.downcase

    reviewers.joins(:review_responses).find_each do |reviewer|
      reviewer.review_responses.discard_all
      reviewer.update(review_submitted: false)
      update(self_review_done: false, manager_summary_done: false, review_writing_percent: 0.0)
    end
  end

  def clean_custom_attribute
    self.custom_attribute = custom_attribute&.strip&.downcase if custom_attribute.present?
  end

  def display_name
    employee.display_name
  end

  def resolve_question_for_reviewee(question_text)
    return if question_text.nil?

    question_text.gsub('{{reviewee_name}}', employee.display_name).gsub('*', '')
  end

  after_discard do
    discard_direct_report_reviewer
    reviewers.where(discarded_at: nil).where(reviewee: self).where(review_cycle: review_cycle).discard_all
    reviewee_goals.where(discarded_at: nil).where(reviewee: self).where(review_cycle: review_cycle).discard_all
    home_page_actions.where(discarded_at: nil).where(actionable: review_cycle).discard_all
    reviewee_competencies.where(discarded_at: nil).where(review_cycle: review_cycle).discard_all
    calibrator_calibratees.discard_all
    remove_manager_home_page_action
    discard_notification_recipients
    reviewee_goal_event_logs.discard_all
    skip_reviewer&.discard
  end

  after_undiscard do
    # TODO: undiscard direct report reviewer for currently undiscarded reviewee employee
    undiscard_direct_report_reviewer
    reviewers.unscoped.where.not(discarded_at: nil).where(reviewee: self).where(review_cycle: review_cycle).undiscard_all
    reviewee_goals.unscoped.where.not(discarded_at: nil).where(reviewee: self).where(review_cycle: review_cycle).undiscard_all
    home_page_actions.with_discarded.undiscard_all
    reviewee_competencies.where.not(discarded_at: nil).where(reviewee: self).where(review_cycle: review_cycle).undiscard_all
    calibrator_calibratees.with_discarded.undiscard_all
    reviewee_goal_event_logs.with_discarded.undiscard_all
    skip_reviewer&.with_discarded&.undiscard
  end

  def remove_manager_home_page_action
    return if review_cycle.nil?
    return if review_cycle.reviewees.with_discarded.kept.where.not(id: id)
      .exists?(manager: manager, review_cycle: review_cycle)

    if review_cycle.peer_selection_role == 'manager'
      review_cycle.home_page_actions.where(action_type: 'choose_direct_report_peers', employee: manager).discard_all
    end

    if review_cycle.reviewer_types.exists?(approval_required: true)
      review_cycle.home_page_actions.where(action_type: 'approve_peers', employee: manager).discard_all
    end

    if review_cycle.reviewer_types.exists?(goal_approval_required: true)
      review_cycle.home_page_actions.where(action_type: 'approve_goals', employee: manager).discard_all
    end
  end

  def peers
    # returns all peer reviewers
    reviewers.joins(:reviewer_type).where(reviewer_types: { reviewer_type: 'peer' })
  end

  def manager_reviewer
    # returns the manager reviewer
    reviewers.joins(:reviewer_type).find_by(reviewer_types: { reviewer_type: 'manager' })
  end

  def direct_reports
    # returns the manager reviewer
    reviewers.joins(:reviewer_type).where(reviewer_types: { reviewer_type: 'direct_report' })
  end

  def custom_reviewers
    reviewers.joins(:reviewer_type)
      .where.not(reviewer_types: { reviewer_type: ReviewerType::DEFAULT_REVIEWER_TYPES })
  end

  def self_reviewer
    # returns the self reviewer
    reviewers.joins(:reviewer_type).find_by(reviewer_types: { reviewer_type: 'self' })
  end

  def create_default_reviewers
    standard_reviewer_types = review_cycle.reviewer_types.where(reviewer_type: %w[self manager direct_report])
    standard_reviewer_types.each do |reviewer_type|
      case reviewer_type.reviewer_type
      when 'self'
        reviewers.find_or_create_by!(review_cycle: review_cycle, reviewer_type: reviewer_type, employee: employee)
      when 'manager'
        next unless manager

        reviewers.find_or_create_by!(review_cycle: review_cycle, reviewer_type: reviewer_type,
                                     employee: manager)
      when 'direct_report'
        next unless reviewer_type.autofill

        direct_reports = employee.direct_reports.active

        if reviewer_type.limit_to_participants
          direct_reports = direct_reports.where(id: review_cycle.reviewees.where(manager_id: employee.id).pluck(:employee_id))
        end

        direct_reports.each do |report|
          reviewers.find_or_create_by!(review_cycle: review_cycle, reviewer_type: reviewer_type, employee: report)
        end

        # Handle scenario where a reviewee is added after their manager:
        # If the reviewee's manager is already in this review cycle,
        # add the current reviewee as a direct report reviewer for their manager
        if manager && review_cycle.reviewees.exists?(employee_id: manager.id)
          manager_reviewee = review_cycle.reviewees.find_by(employee_id: manager.id)
          manager_reviewee.reviewers.find_or_create_by!(review_cycle: review_cycle, reviewer_type: reviewer_type,
                                                        employee: employee)
        end
      end
    end
  end

  def discard_direct_report_reviewer
    return if review_cycle.nil?
    return unless review_cycle.reviewer_types.exists?(reviewer_type: 'direct_report', limit_to_participants: true)

    manager_reviewee = review_cycle.reviewees.find_by(employee_id: manager_id)
    return unless manager_reviewee

    direct_report_reviewer = manager_reviewee.reviewers.joins(:reviewer_type)
      .where(reviewer_types: { reviewer_type: 'direct_report' }).find_by(employee_id: employee_id)

    direct_report_reviewer&.discard
  end

  def undiscard_direct_report_reviewer
    return if review_cycle.nil?

    direct_report_reviewer_type = review_cycle.reviewer_types.find_by(reviewer_type: 'direct_report')
    return unless direct_report_reviewer_type&.limit_to_participants

    manager_reviewee = review_cycle.reviewees.find_by(employee_id: manager_id)
    return unless manager_reviewee

    direct_report_reviewer = manager_reviewee.reviewers.with_discarded.find_by(
      employee_id: employee_id,
      reviewer_type: direct_report_reviewer_type,
    )
    direct_report_reviewer&.undiscard
  end

  def previous_manager_actions
    ManagerChanges::RevieweePreviousManagerActions.call(self)
  end

  def all_previous_managers
    snapshot_manager_ids = reviewee_snapshots.where(reason: 'manager_change').where.not(employee_id: manager_id)
      .order(created_at: :desc).distinct.pluck(:employee_id)
    return [] if snapshot_manager_ids.blank?

    Employee.left_joins(:department).where(id: snapshot_manager_ids)
  end

  private

  def set_current_manager
    return if manager.present?
    return if employee.manager.nil?

    self.manager = employee.manager
    save!
  end

  def update_reviewee_goals
    handle_goal_selection_change if saved_change_to_attribute?(:goal_selection_done)
    handle_goal_approval_change if saved_change_to_attribute?(:goal_approval_done)
  end

  def handle_goal_selection_change
    update(goal_approval_done: false) if goal_approval_done && !goal_selection_done
  end

  def handle_goal_approval_change
    return if goal_approval_done

    reviewee_goals.update_all(approver_id: nil, rejected_by_id: nil)
  end

  def trigger_manager_change_event
    previous_manager_id, new_manager_id = saved_changes[:manager_id]
    ManagerChanges::RevieweeManagerChange.call(self, previous_manager_id, new_manager_id)
  end
end
