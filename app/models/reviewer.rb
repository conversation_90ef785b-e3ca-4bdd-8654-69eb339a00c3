require 'dentaku'

class Reviewer < ApplicationRecord
  include Discard::Model
  include Slack::SlackShared::SharedModule
  include ReviewerMethods
  has_paper_trail

  belongs_to :review_cycle
  belongs_to :reviewer_type
  belongs_to :reviewee
  belongs_to :employee
  belongs_to :nominator, class_name: 'Employee', optional: true
  belongs_to :approver, class_name: 'Employee', optional: true
  belongs_to :rejected_by, class_name: 'Employee', optional: true
  belongs_to :nominator_role, class_name: 'Review<PERSON>ycle<PERSON><PERSON>Role', optional: true
  belongs_to :approver_role, class_name: 'ReviewCycleUserRole', optional: true

  has_many :review_responses, dependent: :destroy
  has_many :home_page_actions, as: :pageable, dependent: :destroy
  has_many :reviewee_snapshots, dependent: :destroy

  validates :rejected_by, absence: true, if: -> { approver.present? }
  validates :approver, absence: true, if: -> { rejected_by.present? }
  default_scope -> { kept }
  after_save :update_review_writing_percent

  after_discard do
    review_responses.where(discarded_at: nil)
      .where(reviewer: self)
      .where(review_cycle_id: review_cycle_id)
      .where(reviewee_id: reviewee_id)
      .where(reviewer_employee_id: employee_id)
      .discard_all
    discard_reviewer_notification_recipients
    PerformanceReview::ReviewerHomePageNotificationRemovalService.call(self)
  end

  after_undiscard do
    review_responses.unscoped
      .where.not(discarded_at: nil)
      .where(reviewer: self)
      .where(review_cycle: review_cycle)
      .where(reviewer_employee: employee)
      .where(reviewee: reviewee)
      .undiscard_all
    home_page_actions.unscoped
      .where.not(discarded_at: nil)
      .where(actionable: review_cycle)
      .undiscard_all
  end

  def discard_reviewer_notification_recipients
    return if review_cycle.nil?
    
    if %w[self direct_report].include?(reviewer_type.reviewer_type)
      discard_notification_recipients
    else
      # Discard notifications if no active reviewers exist for this employee and reviewer type
      discard_notification_recipients if fetch_active_reviewers_for_employee.empty?
    end
  end
  
  # Fetches active reviewers for the current employee, excluding rejected and current reviewer
  # Applies approval filter if reviewer type requires approval
  def fetch_active_reviewers_for_employee
    review_cycle.reviewers
    .unscoped
    .where(review_cycle: review_cycle, employee_id: employee.id)
    .where(reviewer_type: reviewer_type)
    .where(rejected_by_id: nil)
    .where.not(id: id)
    .kept
    .tap { |reviewers| add_approval_filter(reviewers) }
  end

  def add_approval_filter(reviewers)
    reviewers.where.not(approver_id: nil) if reviewer_type.approval_required
  end

  def discard_notification_recipients
    review_cycle.review_cycle_notification_recipients
      .where(employee_id: employee.id)
      .where(reviewer_type_id: reviewer_type.id)
      .discard_all
  end
  
  def update_review_writing_percent
    reviewee.reload if reviewee.present?
    return if reviewee.nil?
    return if reviewee.reviewers.blank?

    approved_reviewers_count = reviewee.reviewers.joins(:reviewer_type).where('reviewer_types.approval_required = false').or(
        reviewee.reviewers.joins(:reviewer_type).where('reviewer_types.approval_required = true').where.not(approver: nil),
      ).count
    submitted_reviews_count = reviewee.reviewers.where(review_submitted: true).count
    review_writing_percent = if approved_reviewers_count.positive?
                               (submitted_reviews_count.to_f / approved_reviewers_count) * 100
                             else
                               0.0
                             end

    reviewee.review_writing_percent = review_writing_percent
    reviewee.save!
  end

  # Run autocalculate for all the calculated questions
  # for this reviewer. to call this just reviewer.auto_calculate, use argument calibration_phase = true if you want to run autocalculation during calibration phase
  def auto_calculate(calibration_phase = false)
    return [] if review_template.nil?

    calculated_questions = review_template.review_cycle_template_blocks.where(block_type: 'calculated_question')
    @zero_weights ||= review_template.reviewer_type_wise_zero_weights

    return [] if calculated_questions.blank?

    # We store default weights on the review_cycle_template_question and if there are no weights configured at reviewee level, we fallback to default_weights.
    reviewee_weights = reviewee.custom_variables.presence
    auto_calc_questions_updated_scores = []
    calculated_questions.each do |calc_question|
      variables = @zero_weights.merge(calc_question.default_weights.presence || {}).merge(reviewee_weights.presence || {})
      auto_calc_questions_updated_scores.push(
        auto_calculate_for_question(calc_question, variables.dup, calibration_phase),
      )
    end
    auto_calc_questions_updated_scores
  end

  # Calculate scores for each of the review cycle template question
  # by using the formula in the formula field of review cycle template question and
  # extract all the variables that are needed for this specific formula
  def auto_calculate_for_question(calc_question, variables, calibration_phase)
    # Fallback to general formula if user defined formula is not present
    formula = calc_question.formula.presence || review_template.general_formula

    # example formula: manager__driver__impact*custom__impact__weight +  manager__driver__values*custom__values__weight

    calculator = ::Dentaku::Calculator.new

    # example custom variables {"custom__impact__weight"=>"0.7", "custom__values__weight"=>"0.3"}
    dependent_variables = calculator.dependencies(formula, variables)

    # example dependent_variables ["manager__driver__impact", "manager__driver__values"]
    dependent_variables.each do |dependent_variable|
      # Breakdown the variable into entity and reviewer type
      # Note: the delimiter is updated to double underscore from underscore
      dependent_variable_reviewer_type, entity, entity_id = dependent_variable.split(ReviewCycleTemplateQuestion::FORMULA_VARIABLE_DELIMITER)
      single_reviewer = reviewee.reviewers.joins(:reviewer_type).find_by(
        reviewer_types: { reviewer_type: dependent_variable_reviewer_type },
      )
      case entity
      when 'driver'
        variables[dependent_variable] =
          calculate_driver_score(dependent_variable_reviewer_type, entity_id, calibration_phase)
      when 'goals'
        variables[dependent_variable] =
          if ReviewerType::SINGLE_REVIEWER_TYPES.include?(dependent_variable_reviewer_type)
            calculate_goal_scores(single_reviewer)
          else
            calculate_goal_for_multiple_reviewer_type_scores(
              reviewee,
              dependent_variable_reviewer_type,
              reviewer_id: dependent_variable_reviewer_type == reviewer_type.reviewer_type ? id : nil,
            )
          end
      when 'question'
        variables[dependent_variable] = calculate_question_score(dependent_variable_reviewer_type, entity_id)
      when 'competency'
        variables[dependent_variable] =
          if ReviewerType::SINGLE_REVIEWER_TYPES.include?(dependent_variable_reviewer_type)
            calculate_competency_scores(single_reviewer)
          else
            calculate_competency_for_multiple_reviewer_type_scores(
              reviewee,
              dependent_variable_reviewer_type,
              reviewer_id: dependent_variable_reviewer_type == reviewer_type.reviewer_type ? id : nil,
            )
          end
      end
    end
    calculated_score = calculator.evaluate(formula, variables).to_f.round(2)

    # applying rounding logic - Khatabook custom requirement feb 2023
    calculated_score = calc_question.question.get_rounded_score(calculated_score)

    # Calculate legend

    params = {
      review_cycle: review_cycle,
      review_template.template_questions_source => calc_question,
      question: calc_question.question,
      reviewer: self,
      reviewee: reviewee,
      reviewer_employee: employee,
      reviewee_employee: reviewee.employee,
    }

    # Store this resposne in the review_resposne table against the calculated score.
    fetch_responses = review_cycle.review_responses.find_by(params)

    review_response = fetch_responses.presence || review_cycle.review_responses.find_or_create_by!(params)

    option = params[:question].options.find_by(value: calculated_score.to_f.round.to_i)
    if calibration_phase
      review_response.update!(calibrated_score: calculated_score, calibrated_score_option: option)
    else
      review_response.update!(score: calculated_score, option: option)
    end
    { calibration_column_id: ReviewCycleCalibrationQuestion.select(:id).find_by(
        question_id: calc_question.question_id, review_cycle_id: review_cycle_id,
      )&.id,
      question_id: calc_question.question.id,
      calibrated_score: calculated_score,
      calibrated_score_legend: option&.legend }
    # TODO: Set the right legend for the response
  end

  def calculate_driver_score(dependent_variable_reviewer_type, driver_key, calibration_phase)
    # Find all the responses
    # for that specific reviewee, from a reviewer type
    # aggregate it by the driver.
    driver = Driver.find_by(key: driver_key,
                            account_id: reviewee.employee.account_id, driver_type: 'performance_review')
    return 0 if driver.nil?

    driver_id = driver.id

    reviewers_where_clause = { reviewer_types: { reviewer_type: dependent_variable_reviewer_type } }
    if dependent_variable_reviewer_type == reviewer_type.reviewer_type
      reviewers_where_clause.merge!(id: id)
    end

    review_res = review_cycle.review_responses
      .joins(:question, reviewer: [:reviewer_type])
      .where(reviewee: reviewee,
             reviewers: reviewers_where_clause,
             questions: { driver_id: driver_id, question_type: %w[nps rating_text] })
      .where.not(score: nil)

    total_score = 0.0
    avg_score = 0.0
    review_res.each do |rr|
      if calibration_phase && rr.calibrated_score.present? # using calibrated scores if calculating score in calibration phase
        total_score += rr.calibrated_score
      else
        total_score += rr.score
      end
    end
    if total_score.positive?
      avg_score = total_score / review_res.count
    end
    avg_score.to_f.round(2)
  end

  def calculate_question_score(dependent_variable_reviewer_type, question_id)
    # Find all the responses
    # for that specific reviewee, from a reviewer type
    # aggregate it by the question.
    reviewer_responses = review_cycle.review_responses
      .select('avg(score) as avg_score')
      .joins(:question, reviewer: [:reviewer_type])
      .where(reviewee: reviewee,
             reviewers: { reviewer_types: { reviewer_type: dependent_variable_reviewer_type } },
             question_id: question_id)
      .where.not(score: nil)

    reviewer_responses.first&.avg_score.to_f.round(2) || 0.0
  end

  def goals_required?
    rc_template_question = review_template.review_cycle_template_blocks.where(
      block_type: 'goals',
    ).where.not(goal_fields: nil).first
    return false if rc_template_question.blank?

    rc_template_question.goal_fields.try(:[], 'required').nil? ? true : rc_template_question.goal_fields['required']
  end
end
