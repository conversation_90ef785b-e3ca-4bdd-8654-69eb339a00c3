# == Schema Information
#
# Table name: review_cycles
#
#  title                       :string(255)      Title of the Review Cycle
#  account_id                  :bigint           Foreign Key Reference for Account ID
#  start_date                  :datetime         (Deprecated) Start date of review cycle
#  template_employee_attribute :string(255)      Field in employee table that it used for different review templates for different users
#  creator_id                  :bigint           Person who created the review cycle
#  last_synced_at              :datetime         Time when Google Sheet was last synced
#  anonymize_reports           :boolean          Used for sending non-anonymous reports in PDF.
#  view_unsubmitted_reviews    :boolean          (Deprecated) Built for Lightspeed, custom field
#  review_cycle_phase          :string(255)      (Deprecated) Use review cycle phases instead. Only used for release reviews now.
#  gsheet_info                 :json             JSON value of all the Google Sheet Sync information
#  show_key_results            :boolean          Configuration to show child level goals for review or only parent level.
#  auto_progress_manager       :boolean          Automatically notifies manager when employee finishes self review.
#  auto_progress_employee      :string(255)      (Not used) Automatically notifies employee when manager finishes their review
#  status                      :string(255)      Live/Draft/Paused/Completed of the entire review.
#  auto_create_one_on_one      :string(255)      Enable automatic creation of 1:1 from review cycle. See ENUM below.
#
class ReviewCycle < ApplicationRecord
  include Discard::Model
  include ReviewCycleMethods
  include ReviewCycleUserRoleConcern

  default_scope -> { kept }
  has_paper_trail

  DEFAULT_GOAL_CONFIG = {
    minimum_goal_weightage: 1,
    maximum_goal_weightage: 100,
  }.freeze

  DEFAULT_USER_ROLES = %w[self manager admin].freeze

  serialize :competency_config_json, ::ReviewCycleConfig::CompetencyConfig
  serialize :goal_config_json, ::ReviewCycleConfig::GoalConfig

  belongs_to :account
  belongs_to :creator, class_name: 'Employee'
  has_many :reviewees, dependent: :destroy
  has_many :reviewers, dependent: :destroy
  has_many :review_responses, dependent: :destroy
  has_one :omnichat_conversation, as: :feature, dependent: :destroy
  has_many :review_cycle_automations, dependent: :destroy
  has_many :review_copy_messages, dependent: :destroy
  has_many :review_cycle_goal_cycles, dependent: :destroy
  has_many :goal_cycles, through: :review_cycle_goal_cycles
  has_many :reviewee_goals, dependent: :destroy
  has_many :home_page_actions, as: :actionable, dependent: :destroy
  has_many :reports, as: :reportable, class_name: 'MetabaseReport', dependent: :destroy
  has_many :review_cycle_templates, through: :reviewer_types
  has_many :review_cycle_phases, dependent: :destroy
  has_many :review_cycle_notifications, dependent: :destroy
  has_many :review_cycle_notification_recipients, dependent: :destroy
  has_many :questions, as: :serviceable, dependent: :destroy
  has_many :reviewee_competencies, dependent: :destroy
  has_many :pdf_configurations, as: :configurable, dependent: :destroy
  has_many :review_cycle_user_roles, dependent: :destroy
  has_many :skip_reviewers, dependent: :destroy
  has_many :review_cycle_visibility_matrices, dependent: :destroy
  has_one :peer_config, dependent: :destroy

  enum review_cycle_phase: { choose_peers: 'choose_peers', write_reviews: 'write_reviews',
                             manager_summary: 'manager_summary', release_summary: 'release_summary' }

  enum auto_progress_employee: { auto_progress_employee_disabled: 'disabled',
                                 auto_progress_employee_enabled: 'enabled' }

  validates :title, presence: true

  enum auto_create_one_on_one: { one_on_one_disabled: 'disabled',
                                 one_on_one_on_self_review_complete: 'on_self_review_complete',
                                 one_on_one_on_manager_review_complete: 'on_manager_review_complete',
                                 one_on_one_on_self_manager_review_complete: 'on_self_manager_review_complete' }

  enum status: { draft: 'draft', scheduled: 'scheduled', live: 'live', paused: 'paused', completed: 'completed' }
  enum review_type: { performance_review: 'performance_review', three_sixty_review: 'three_sixty_review' }
  enum default_channel: { email: 'email', slack: 'slack', ms_teams: 'ms_teams' }

  scope :archived, -> { where.not(archived_at: nil) }
  scope :unarchived, -> { where(archived_at: nil) }
  scope :ordered, -> { order(created_at: :desc) }

  after_create do
    populate_metabase_report_reportable
    create_review_cycle_user_roles
  end

  after_update :create_review_cycle_automations, if: -> { saved_change_to_attribute?(:automation_enabled) }
  after_save :update_review_cycle_end_date, if: -> { saved_change_to_attribute?(:archived_at) }
  after_save :create_review_cycle_phases, if: -> { saved_change_to_attribute?(:status) }
  after_save :trigger_status_updated_to_live_event, if: -> { saved_change_to_attribute?(:status) }
  after_save :trigger_enforce_system_manager_mapping_updated_to_date_event, if: lambda {
                                                                                  saved_change_to_attribute?(:enforce_system_manager_mapping)
                                                                                }
  after_save :update_reviewee_goals
  after_save :update_home_page_actions, if: -> { saved_change_to_attribute?(:end_date) }

  def create_review_cycle_automations
    return unless automation_enabled

    init_result = ReviewCycles::Automations::Init.call(id)
    return if init_result.success?

    update_column(:automation_enabled, false)
  end

  def update_review_cycle_end_date
    previous_value, current_value = saved_changes[:archived_at]
    return if previous_value == current_value

    update(end_date: current_value) if current_value.present? && previous_value.nil?
  end

  def update_reviewee_goals
    if saved_change_to_attribute?(:show_key_results)
      previous_value, current_value = saved_changes[:show_key_results]
      return if previous_value == current_value

      PerformanceReview::UpdateRevieweeGoals.call(id)
    end
  end

  def update_home_page_actions
    return if end_date.nil?

    home_page_actions.update(end_date: end_date)
    # close the review cycle phases if end date is set
    # get phases where their end time is nil or in the feture
    # update those phases with rc end date
    review_cycle_phases.where.not(start_time: nil)
      .where('end_time is NULL OR end_time >= ?', Time.now.utc)
      .find_each do |phase|
      phase.update(end_time: end_date)
    end
  end

  def create_review_cycle_phases
    PerformanceReview::CreateReviewCyclePhases.call(id) if %w[live scheduled].include?(status)
  end

  def populate_metabase_report_reportable
    metabase_reports = [
      { title: 'Analytics', metabase_dashboard_id: ENV['META_ANALYTICS_ID'], height: 4000 },
      { title: 'Slack Logs', metabase_dashboard_id: ENV['META_SLACK_LOG_ID'], height: 4000 },
      { title: 'Email Logs', metabase_dashboard_id: ENV['META_EMAIL_LOG_ID'], height: 4000 },
    ]
    metabase_reports.each do |metabase_report|
      next if metabase_report[:metabase_dashboard_id].nil?

      report_data = {
        title: metabase_report[:title],
        metabase_dashboard_id: metabase_report[:metabase_dashboard_id].to_i,
        height: metabase_report[:height],
        account: account,
        dashboard_type: 'dashboard',
        params: { review_cycle_id: id },
        reportable: self,
      }
      MetabaseReport.create(report_data)
    end
  end

  def archived?
    archived_at.present?
  end

  def has_same_visibility_config?
    has_same_visibility_config
  end

  def duplicated_from_review_cycle
    return if duplicated_from.nil?

    ReviewCycle.find(duplicated_from)
  end

  def employee_attributes
    review_cycle_templates.distinct(:employee_attribute).pluck(:employee_attribute)
  end

  def employee_attribute_template_ids
    review_cycle_templates.joins(:reviewer_type)
      .where(reviewer_types: { creation_source: :normal })
      .select(
      'JSON_ARRAYAGG(review_cycle_templates.id) AS review_cycle_template_ids, employee_attribute',
    ).group(:employee_attribute).map do |record|
      next if record.employee_attribute.blank?

      {
        employee_attribute: record.employee_attribute,
        review_cycle_template_ids: JSON.parse(record.review_cycle_template_ids),
      }
    end.compact
  end

  def custom_reviewer_types
    reviewer_types.where.not(reviewer_type: ReviewerType::DEFAULT_REVIEWER_TYPES)
  end

  def competency_weights_required?
    competency_config_json.weights[:is_enabled]
  end

  def competency_required?
    competency_config_json.is_enabled
  end

  def goal_visibility
    goal_config_json.goal_visibility
  end

  def minimum_goal_weightage
    goal_config_json.minimum_goal_weightage
  end

  def maximum_goal_weightage
    goal_config_json.maximum_goal_weightage
  end

  def allow_zero_weightage_goals?
    goal_config_json.allow_zero_weightage_goals
  end

  def goal_weights_required?
    reviewer_types.exists?(define_goal_weights: true)
  end

  def goal_with_objectives_and_key_results?
    goal_config_json.review_cycle_goal_type == goal_config_json.review_cycle_goal_types[:goals_with_objectives_and_key_results]
  end

  def goal_with_objectives_only?
    goal_config_json.review_cycle_goal_type == goal_config_json.review_cycle_goal_types[:goals_with_objectives_only]
  end

  def goal_with_key_results_only?
    goal_config_json.review_cycle_goal_type == goal_config_json.review_cycle_goal_types[:goals_with_key_results_only]
  end

  def manager_selects_and_approve_goals?
    review_cycle_phases.where(phase_type: 'goal_selection').blank? &&
      review_cycle_phases.where(phase_type: 'goal_approval').exists?
  end

  after_discard do
    reviewees.where(discarded_at: nil).where(review_cycle: self).discard_all
    reviewer_types.where(discarded_at: nil).where(review_cycle: self).discard_all
    home_page_actions.where(discarded_at: nil).where(actionable: self).discard_all
    review_cycle_phases.where(discarded_at: nil).where(review_cycle: self).discard_all
    review_cycle_goal_cycles.where(discarded_at: nil).where(review_cycle: self).discard_all
    reviewee_competencies.where(discarded_at: nil).where(review_cycle: self).discard_all
    pdf_configurations.where(discarded_at: nil).where(configurable: self).discard_all
    discard_review_cycle_user_roles
    peer_config.discard
  end

  after_undiscard do
    reviewees.with_discarded.where.not(discarded_at: nil).where(review_cycle: self).undiscard_all
    reviewer_types.with_discarded.where.not(discarded_at: nil).where(review_cycle: self).undiscard_all
    home_page_actions.with_discarded.where.not(discarded_at: nil).where(actionable: self).undiscard_all
    review_cycle_phases.with_discarded.where.not(discarded_at: nil).where(review_cycle: self).undiscard_all
    review_cycle_goal_cycles.with_discarded.where.not(discarded_at: nil).where(review_cycle: self).undiscard_all
    reviewee_competencies.with_discarded.where.not(discarded_at: nil).where(review_cycle: self).undiscard_all
    pdf_configurations.with_discarded.where.not(discarded_at: nil).where(configurable: self).undiscard_all
    undiscard_review_cycle_user_roles
    PeerConfig.with_discarded.where.not(discarded_at: nil).find_by(review_cycle: self)&.undiscard
  end

  def self.for_employee(employee)
    review_cycles = unarchived.where(account_id: employee.account_id, status: %w[live completed])

    unless employee.admin? || employee.acl_role_admin?(product_action_keys: [:reviews_grant_all_permissions])
      review_cycles = review_cycles.where(id: completed_review_cycle_ids_for(employee))
    end

    review_cycles.ordered
  end

  def self.completed_review_cycle_ids_for(employee)
    Reviewee.where(employee: employee, release_review_done: true).pluck(:review_cycle_id).uniq
  end

  private

  def trigger_status_updated_to_live_event
    previous_status, current_status = saved_changes[:status]
    return unless current_status == 'live'

    publish_event(previous_status, current_status)

    Rails.logger.info(
      "ReviewCycleStatusUpdatedToLive event triggered for review cycle #{id} " \
      "(#{previous_status} → #{current_status})",
    )
  end

  def trigger_enforce_system_manager_mapping_updated_to_date_event
    previous_status, current_status = saved_changes[:enforce_system_manager_mapping]
    return if current_status.nil?
    return if status != 'live'

    publish_event(nil, 'live')

    Rails.logger.info(
    "ReviewCycleStatusUpdatedToLive event triggered for review cycle #{id} " \
    "(#{previous_status} → #{current_status})",
  )
  end

  def publish_event(previous_status, current_status)
    event = Events::Definitions::ReviewCycleStatusUpdatedToLiveDefinition.new(
  data: {
    review_cycle_id: id,
    previous_status: previous_status,
    new_status: current_status,
  }.with_indifferent_access,
)

    Events::Publisher.publish(
      event: event,
      stream_name: EventStore::StreamNames.for_review_cycle(id),
      metadata: {
        triggered_by: 'review_cycle_model_callback',
        review_cycle_id: id,
        previous_status: previous_status,
      },
      async: true,
    )
  end
end
