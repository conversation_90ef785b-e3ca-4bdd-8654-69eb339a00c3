# frozen_string_literal: true

module EventStore
  class StreamNames
    def self.for_employee(employee_id)
      "Employee$#{employee_id}"
    end

    def self.for_manager(manager_id)
      "Manager$#{manager_id}"
    end

    def self.for_review_cycle(review_cycle_id)
      "ReviewCycle$#{review_cycle_id}"
    end

    def self.for_review_cycle_and_employee(review_cycle_id, employee_id)
      "ReviewCycle$#{review_cycle_id}:Employee$#{employee_id}"
    end

    def self.for_review_cycle_and_manager(review_cycle_id, manager_id)
      "ReviewCycle$#{review_cycle_id}:Manager$#{manager_id}"
    end

    def self.for_entity(entity)
      case entity
      when ::Employee
        for_employee(entity.id)
      when ::Manager
        for_manager(entity.id)
      when ::ReviewCycle
        for_review_cycle(entity.id)
      else
        raise ArgumentError, "No stream name mapping for #{entity.class.name}"
      end
    end
  end
end
