# frozen_string_literal: true

module EventStore
  module Mappers
    class SymbolizedCustomMapper < RubyEventStore::Mappers::Default
      def event_class_resolver
        lambda { |event_type|
          # Resolves "EmployeeManagerChanged" to Events::Definitions::EmployeeManagerChanged
          "Events::Definitions::#{event_type}".safe_constantize || super.call(event_type)
        }
      end

      def event_type_resolver
        lambda { |domain_event|
          # Stores only the short name of the event class (e.g., "EmployeeManagerChanged")
          domain_event.class.name.demodulize
        }
      end

      def event_from_record(record)
        super.tap do |event|
          # Force symbolized keys for data and metadata
          event.instance_variable_set(:@data, deep_symbolize(event.data))
          event.instance_variable_set(:@metadata, deep_symbolize(event.metadata))
        end
      end

      private

      def deep_symbolize(obj)
        case obj
        when Hash
          obj.each_with_object({}) do |(k, v), h|
            h[k.to_sym] = deep_symbolize(v)
          end
        when Array
          obj.map { |e| deep_symbolize(e) }
        else
          obj
        end
      end
    end
  end
end
