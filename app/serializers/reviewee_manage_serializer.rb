class RevieweeManageSerializer < ActiveModel::Serializer
  attributes :id, :self_review_done, :manager_summary_done, :release_review_done,
             :review_writing_percent, :confidential, :employee_id,
             :custom_attribute, :current_weights, :weights_valid, :row_type

  attribute :goal_selection_done, if: -> { phase_type_present?('goal_selection') }
  attribute :goal_approval_done, if: -> { phase_type_present?('goal_approval') }
  attribute :peer_selection_done, if: -> { phase_type_present?('peer_selection') }
  attribute :peer_approval_done, if: -> { phase_type_present?('peer_approval') }
  attribute :competency_selection_done, if: -> { phase_type_present?('competency_selection') }
  attribute :competency_approval_done, if: -> { phase_type_present?('competency_approval') }
  attribute :peer_reviewers
  attribute :direct_report_reviewers
  attribute :custom_reviewers
  attribute :reviewer_progress_status
  attribute :reviewee_previous_managers

  def reviewee_previous_managers
    ActiveModelSerializers::SerializableResource.new(
        @object.all_previous_managers, each_serializer: ManagerSerializer
      )
  end

  def reviewer_progress_status
    return if @object.custom_attribute.nil?

    @object.reviewers.each_with_object({ reviewers_with_submitted_responses: [],
                                         reviewers_with_unsubmitted_responses: [],
                                         reviewers_without_responses: [] }) do |reviewer, store|
      if reviewer.review_responses.present? && reviewer.review_submitted
        store[:reviewers_with_submitted_responses] << { full_name: reviewer.employee.full_name,
                                                        reviewer_type: reviewer.reviewer_type.reviewer_type,
                                                        review_submitted: reviewer.review_submitted }
      elsif reviewer.review_responses.present? && !reviewer.review_submitted
        store[:reviewers_with_unsubmitted_responses] << { full_name: reviewer.employee.full_name,
                                                          reviewer_type: reviewer.reviewer_type.reviewer_type,
                                                          review_submitted: reviewer.review_submitted }
      else
        store[:reviewers_without_responses] << { full_name: reviewer.employee.full_name,
                                                 reviewer_type: reviewer.reviewer_type.reviewer_type,
                                                 review_submitted: reviewer.review_submitted }
      end
    end
  end

  def phase_type_present?(phase_type)
    object.review_cycle.review_cycle_phases.map(&:phase_type).include?(phase_type)
  end

  def row_type
    'reviewee'
  end

  def current_weights
    weights = merged_weights
    return {} if weights.blank?

    { manager: transform_weights_values(weights) }
  end

  def merged_weights
    weight_keys = manager_templates.map(&:formula_weight_keys).flatten.uniq
    return {} if weight_keys.blank?

    reviewer_type_wise_default_weights = weight_keys.map { |k| [k, 0] }.to_h.merge(
      review_cycle_template_blocks.reject do |block|
        block.default_weights.nil?
      end.pluck(:default_weights).reduce({}, :merge).slice(*weight_keys),
    )

    reviewer_type_wise_default_weights.merge!(@object.custom_variables.slice(*weight_keys)) if @object.custom_variables.present?
    reviewer_type_wise_default_weights
  end

  def weights_valid
    manager_templates.map { |manager_template| weight_validity(manager_template) }.all?
  end

  def weight_validity(manager_template)
    overall_block = manager_template&.overall_score_block
    return true if overall_block.blank?
    return true if overall_block.formula.blank?

    weight_keys = manager_template&.formula_weight_keys
    weights = merged_weights
    return true if weight_keys.blank?
    return false if weights.blank?

    (weights.keys & weight_keys).sort.eql?(weight_keys.sort) &&
      weights.slice(*weight_keys).values.map(&:to_f).sum.to_s.eql?('1.0')
  end

  belongs_to :profile_picture
  belongs_to :full_name
  belongs_to :department
  belongs_to :manager_reviewer_id
  belongs_to :self_reviewer_id
  belongs_to :email_id
  belongs_to :title
  belongs_to :manager, serializer: ManagerSerializer

  def peer_reviewers
    reviewer_type = @object.review_cycle.reviewer_types.find_by(reviewer_type: 'peer')
    reviewers = @object.peers.where(rejected_by_id: nil)
    build_reviewer_section(reviewer_type, reviewers, PeerSerializer) || []
  end

  def direct_report_reviewers
    reviewer_type = @object.review_cycle.reviewer_types.find_by(reviewer_type: 'direct_report')
    reviewers = @object.direct_reports.where(rejected_by_id: nil)
    build_reviewer_section(reviewer_type, reviewers, DirectReportSerializer) || []
  end

  def custom_reviewers
    @object.review_cycle.custom_reviewer_types.map do |type|
      reviewers = @object.custom_reviewers.where(reviewer_type: type, rejected_by_id: nil)
      build_reviewer_section(type, reviewers, CustomReviewerSerializer)
    end
  end

  def build_reviewer_section(reviewer_type, reviewers_scope, serializer)
    return nil if reviewer_type.nil?

    eligible_reviewers = reviewers_scope.where(
      'reviewer_types.approval_required = false AND reviewers.rejected_by_id IS NULL OR
      (reviewer_types.approval_required = true AND reviewers.approver_id IS NOT NULL)',
    )

    if %w[entire_team direct_reports indirect_reports].include?(scope)
      reviewers_scope = eligible_reviewers
    end

    eligible_reviewers_count = eligible_reviewers.count
    reviewers_submitted_reviews_count = eligible_reviewers.where(review_submitted: true).count
    progress = eligible_reviewers_count == 0 ? 0 : ((reviewers_submitted_reviews_count.to_f / eligible_reviewers_count) * 100).floor

    {
      reviewer_type_info: {
        reviewer_type_id: reviewer_type.id,
        reviewer_type: reviewer_type.reviewer_type,
        reviewer_label: reviewer_type.first_person,
        reviewee_label: reviewer_type.second_person,
        creation_source: reviewer_type.creation_source,
      },
      reviewers: ActiveModelSerializers::SerializableResource.new(
        reviewers_scope, each_serializer: serializer
      ),
      progress_status: {
        total_count: eligible_reviewers_count,
        completed_count: reviewers_submitted_reviews_count,
        progress: progress,
      },
    }
  end

  def profile_picture
    object.employee.profile_picture.url
  end

  def full_name
    object.employee.full_name
  end

  def email_id
    object.employee.user.email
  end

  def title
    object.employee.title
  end

  def department
    object&.employee.department&.name
  end

  def manager_reviewer_id
    object&.manager_reviewer&.id
  end

  def self_reviewer_id
    object&.self_reviewer&.id
  end

  private

  def transform_weights_values(weights)
    driver_k_n = driver_keys_names
    weights.each_with_object({}) do |(key, value), res|
      r_type_key_parts = key.split(delimiter)
      name = "#{driver_k_n[(r_type_key_parts & driver_k_n.keys).first]} - #{r_type_key_parts.first.titleize} Review"
      res[key] = { name: name, value: value }
    end
  end

  def review_cycle_template_blocks
    review_cycle_templates.where(
      employee_attribute: object.custom_attribute,
    ).map(&:review_cycle_template_blocks).flatten.compact
  end

  def reviewer_type_wise_zero_weights
    review_cycle_templates.first.reviewer_type_wise_zero_weights
  end

  def review_cycle_templates
    ReviewCycleTemplate.joins(:reviewer_type)
      .where(reviewer_types: { review_cycle_id: @object.review_cycle_id })
  end

  def manager_templates
    review_cycle_templates.where(
      reviewer_types: { reviewer_type: 'manager' },
      employee_attribute: @object.custom_attribute,
    )
  end

  def driver_keys_names
    result = Question.joins(:driver).where(
      id: review_cycle_template_blocks.pluck(:question_id),
    ).reorder('drivers.key').select(
      'distinct drivers.key, drivers.name',
    ).each_with_object({}) { |drv, res| res[drv.key] = drv.name }
    result.merge!('goals' => 'Goals', 'competency' => 'Competency')
    result
  end

  def delimiter
    ReviewCycleTemplateQuestion::FORMULA_VARIABLE_DELIMITER
  end
end
