class ReviewCycleSerializer < ActiveModel::Serializer
  include Slack::SlackShared::HomePageActionsProgress
  attributes :id, :title, :review_type, :start_date, :creator_id,
             :review_cycle_phase, :archived_at, :template_employee_attribute,
             :permissions

  attribute :ongoing do
    true
  end

  attribute :enforce_manager_change do
    {
      enabled: object.enforce_system_manager_mapping.present?,
      end_date: object.enforce_system_manager_mapping&.in_time_zone(object.account.timezone)&.strftime('%Y-%m-%d'),
    }
  end

  attribute :reviewer_types_summary do
    object.reviewer_types_summary
  end

  attribute :user_roles do
    @object.user_roles
  end

  attribute :review_goal_type do
    object.review_cycle_goal_type
  end

  attribute :review_cycle_goal_types do
    object.review_cycle_goal_types
  end

  attribute :peer_selection_role do
    @object.peer_selection_role
  end

  attribute :reviewee_status do
    reviewee = object.reviewees.where(employee: current_employee).first
    if reviewee
      {
        id: reviewee.id,
        peer_selection_done: reviewee.peer_selection_done,
        peer_approval_done: reviewee.peer_approval_done,
        goal_approval_done: reviewee.goal_approval_done,
        review_writing_percent: reviewee.review_writing_percent,
        self_review_done: reviewee.self_review_done,
        competency_selection_done: reviewee.competency_selection_done,
        competency_approval_done: reviewee.competency_approval_done,
        release_review_done: reviewee.release_review_done,
      }
    end
  end
  def fetch_home_page_actions(action_type)
    HomePageAction.where(employee: current_employee,
                         action_type: action_type,
                         actionable: object)
      .exists?(['start_date <= ? and end_date >= ?', Time.zone.now, Time.zone.now])
  end

  def phase_type(action_type)
    case action_type
    when 'approve_peers'
      'peer_approval'
    when 'choose_peers', 'choose_direct_report_peers'
      'peer_selection'
    when 'create_goals'
      'goal_selection'
    when 'approve_goals'
      'goal_approval'
    when 'self_review', 'peer_review', 'upward_review', 'custom_review'
      'write_reviews'
    when 'downward_review'
      'manager_summary'
    when 'create_competencies'
      'competency_selection'
    when 'approve_competencies'
      'competency_approval'
    end
  end

  def fetch_review_cycle_phase(action_type, reviewer_type = nil)
    phase_type = phase_type(action_type)
    if phase_type == 'write_reviews'
      action_reviewer_type = fetch_reviewer_types(action_type, reviewer_type)
      reviewer_type_phase = ReviewCyclePhase.where(phase_type: phase_type, review_cycle: object,
                                                   reviewer_type: action_reviewer_type)

      if action_reviewer_type.present? && reviewer_type_phase.present?
        reviewer_type_phase.exists?([
                                      'start_time <= ?', Time.now.utc
                                    ])
      else
        ReviewCyclePhase.where(phase_type: phase_type, review_cycle: object,
                               reviewer_type_id: nil).exists?(['start_time <= ?',
                                                               Time.now.utc])
      end
    else
      ReviewCyclePhase.where(phase_type: phase_type, review_cycle: object,
                             reviewer_type_id: nil).exists?(['start_time <= ?',
                                                             Time.now.utc])
    end
  end

  def fetch_reviewer_types(action_type, reviewer_type = nil)
    case action_type
    when 'self_review'
      ReviewerType.find_by(reviewer_type: 'self', review_cycle: object)
    when 'peer_review'
      ReviewerType.find_by(reviewer_type: 'peer', review_cycle: object)
    when 'upward_review'
      ReviewerType.find_by(reviewer_type: 'direct_report', review_cycle: object)
    when 'custom_review'
      ReviewerType.find_by(reviewer_type: reviewer_type, review_cycle: object)
    end
  end

  def phase_access_type(action_type, reviewer_type = nil)
    # true condition - read and write mode
    # phase has no end date or
    # phase end date is in the feature
    # false condition - read only mode
    # phase has an end date and the end date is in the past
    phase_type = phase_type(action_type)
    phase = if phase_type == 'write_reviews'
              action_reviewer_type = fetch_reviewer_types(action_type, reviewer_type)
              reviewer_type_phase = ReviewCyclePhase.find_by(phase_type: phase_type, review_cycle: object,
                                                             reviewer_type: action_reviewer_type)
              if action_reviewer_type.present? && reviewer_type_phase.present?
                reviewer_type_phase
              else
                ReviewCyclePhase.find_by(phase_type: phase_type, review_cycle: object, reviewer_type_id: nil)
              end
            else
              ReviewCyclePhase.find_by(phase_type: phase_type, review_cycle: object, reviewer_type_id: nil)
            end
    if phase.present? && phase.closed?
      {
        access: false,
        access_mode: 'Read Only Mode',
      }
    else
      {
        access: true,
        access_mode: 'Read and Write Mode',
      }
    end
  end

  def permissions
    return if current_employee.blank?

    {
      progress: progress_view_permissions,
      peer_selection: {
        choose_peers: { enabled: choose_peers_enabled? }
          .merge!(choose_peers_progress(object, current_employee.id))
          .merge!(phase_access_type('choose_peers')),
        approve_peers: { enabled: approve_peers_enabled? }
          .merge!(approve_peers_progress(object, current_employee.id))
          .merge!(phase_access_type('approve_peers')),
        choose_direct_report_peers: { enabled: choose_direct_report_peers_enabled? }
          .merge!(choose_direct_report_peers_progress(object, current_employee.id))
          .merge!(phase_access_type('choose_direct_report_peers')),
      },
      goals_selection: {
        define_goals: {
          # if user is a reviewee,
          # is a reviewer and has define goals for that reviewer type
          # and has a manager reviewer
          enabled: define_goals_enabled?,
        }.merge!(create_goals_progress(object, current_employee.id))
          .merge!(phase_access_type('create_goals')),
        approve_goals: {
          # enabled: false,
          # need to be a manager
          # the manager type need to have goal approval set to true
          enabled: approve_goals_enabled?,
        }.merge!(approve_goals_progress(object, current_employee.id))
          .merge!(phase_access_type('approve_goals')),
        review_goals: {
          enabled: review_goals?,
        }.merge!(review_goals_progress(@object, current_employee)).merge!({ access: true }),
      },
      competency_selection: {
        define_competencies: {
          enabled: define_competencies_enabled?,
        }.merge!(create_competencies_progress(object, current_employee.id))
          .merge!(phase_access_type('create_competencies')),
        approve_competencies: {
          enabled: approve_competencies_enabled?,
        }.merge!(approve_competencies_progress(object, current_employee.id))
          .merge!(phase_access_type('approve_competencies')),

      },
      write_reviews: write_review_permissions,
      manage_team: manage_team_permissions,
      review_feedback: {
        review_responses: {
          name: 'View Feedback',
          enabled: !object.archived? && object.review_cycle_phase == 'release_summary' &&
            object.reviewees.where(employee: current_employee).present? &&
            object.reviewees.find_by(employee_id: current_employee.id)&.release_review_done,
          reviewer_type_id: object.reviewer_types.first&.id,
          reviewee_id: object.reviewees.find_by(employee_id: current_employee.id)&.id,
          page: 'view_review',
        },
      },
    }
  end

  private

  def choose_peers_enabled?
    !object.archived? && object.reviewees.where(employee: current_employee).present? &&
      object.reviewer_types.where(reviewer_type: 'peer').present? &&
      fetch_review_cycle_phase('choose_peers') && fetch_home_page_actions('choose_peers') &&
      object.peer_selection_role == 'self' && object.peer_selection_required
  end

  def choose_direct_report_peers_enabled?
    !object.archived? && object.reviewees.where(manager: current_employee).present? &&
      object.reviewer_types.where(reviewer_type: 'peer').present? &&
      fetch_review_cycle_phase('choose_direct_report_peers') && fetch_home_page_actions('choose_direct_report_peers') &&
      object.peer_selection_role == 'manager' && object.peer_selection_required
  end

  def approve_peers_enabled?
    !object.archived? && object.reviewees.where(manager: current_employee).present? &&
      object.reviewer_types.where(reviewer_type: 'peer', approval_required: true).present? &&
      fetch_review_cycle_phase('approve_peers') && fetch_home_page_actions('approve_peers')
  end

  def define_goals_enabled?
    !object.archived? && object.reviewees.where(employee: current_employee).present? &&
      object.reviewees.where(employee: current_employee).where.not(manager_id: nil).present? &&
      object.reviewers.joins(:reviewer_type).where(employee: current_employee).where(
        reviewer_types: { define_goals: true },
      ).present? && fetch_review_cycle_phase('create_goals') && fetch_home_page_actions('create_goals')
  end

  def approve_goals_enabled?
    !object.archived? && object.reviewees.joins(review_cycle: :reviewer_types).where(manager: current_employee).where(
      reviewer_types: { goal_approval_required: true },
    ).present? && fetch_review_cycle_phase('approve_goals') && fetch_home_page_actions('approve_goals')
  end

  def define_competencies_enabled?
    !object.archived? &&
      object.reviewees.where(employee: current_employee).present? &&
      fetch_review_cycle_phase('create_competencies') && fetch_home_page_actions('create_competencies')
  end

  def approve_competencies_enabled?
    !object.archived? &&
      object.reviewees.joins(review_cycle: :reviewer_types).where(manager: current_employee).present? &&
      fetch_review_cycle_phase('approve_competencies') && fetch_home_page_actions('approve_competencies')
  end

  def review_goals?
    return false unless @object.goal_config_json.review_goals_enabled?

    skip_reviewers = @object.skip_reviewers.where(employee: current_employee)
    return false if skip_reviewers.blank?

    true
  end

  def write_review_permissions
    self_data = object.reviewers.joins(:reviewer_type)
      .where(employee: current_employee)
      .where(reviewer_types: { reviewer_type: 'self' })

    peer_reviewer_type = object.reviewer_types.find_by(reviewer_type: 'peer')
    peers_data = object.reviewers.where(employee: current_employee).where(reviewer_type_id: peer_reviewer_type&.id)
    peers_data = if object.reviewer_types.find_by(reviewer_type: 'peer')&.approval_required
                   peers_data.where.not(approver_id: nil)
                 else
                   peers_data.where.not(nominator_id: nil).where(rejected_by_id: nil)
                 end
    peer_data_or_can_review_anyone = peers_data.present? || peer_reviewer_type&.can_review_anyone?

    direct_report_data = object.reviewers.joins(:reviewer_type)
      .where(employee: current_employee)
      .where(reviewer_types: { reviewer_type: 'direct_report' })

    {
      self: {
        name: 'Self',
        enabled: !object.archived? && self_data.present? &&
          fetch_review_cycle_phase('self_review') && fetch_home_page_actions('self_review'),
        can_review_anyone: false,
        reviewer_type_id: self_data.present? ? self_data.first.reviewer_type.id : nil,
        reviewer_id: self_data.present? ? self_data.first.id : nil,
        page: 'form_page',
      }.merge!(self_review_progress(object, current_employee.id))
        .merge!(phase_access_type('self_review')),
      peer: {
        name: 'Peers',
        enabled: !object.archived? && fetch_review_cycle_phase('peer_review') &&
          peer_data_or_can_review_anyone && fetch_home_page_actions('peer_review'),
        can_review_anyone: peer_reviewer_type&.can_review_anyone?,
        reviewer_type_id: peer_data_or_can_review_anyone ? peer_reviewer_type&.id : nil,
        reviewer_id: nil,
        page: 'list_page',
      }.merge!(peer_review_progress(object, current_employee.id))
        .merge!(phase_access_type('peer_review')),
      manager: {},
      direct_report: {
        name: 'Manager',
        enabled: !object.archived? && direct_report_data.present? &&
          fetch_review_cycle_phase('upward_review') && fetch_home_page_actions('upward_review'),
        can_review_anyone: false,
        reviewer_type_id: direct_report_data.present? ? direct_report_data.first.reviewer_type.id : nil,
        reviewer_id: direct_report_data.present? ? direct_report_data.first.id : nil,
        page: 'form_page',
      }.merge!(upward_review_progress(object, current_employee.id))
        .merge!(phase_access_type('upward_review')),

      pod_owner: {},
    }.merge!(custom_reviewer_write_review_permissions)
  end

  def custom_reviewer_write_review_permissions
    custom_reviewer_types = object.reviewer_types.where.not(reviewer_type: ReviewerType::DEFAULT_REVIEWER_TYPES)
    custom_reviewer_types.each_with_object({}) do |custom_reviewer_type, perms|
      custom_reviewers_data = object.reviewers.joins(:reviewer_type).where(employee: current_employee).where(
        reviewer_types: { reviewer_type: custom_reviewer_type.reviewer_type },
      ).first
      is_enabled = custom_reviewers_data.present? || custom_reviewer_type.can_review_anyone?
      next unless is_enabled

      perms[custom_reviewer_type.reviewer_type.to_sym] = {
        name: custom_reviewer_type.second_person.gsub('_', ' ').titleize,
        enabled: !object.archived? && fetch_review_cycle_phase('custom_review',
                                                               custom_reviewer_type.reviewer_type) && is_enabled && fetch_home_page_actions('custom_review'),
        can_review_anyone: custom_reviewer_type.can_review_anyone?,
        reviewer_type_id: custom_reviewer_type.id,
        reviewer_id: custom_reviewers_data&.id,
        page: 'list_page',
      }.merge!(custom_review_progress(object, current_employee.id, custom_reviewer_type.reviewer_type))
        .merge!(phase_access_type('custom_review', custom_reviewer_type.reviewer_type))
    end
  end

  def progress_view_permissions
    current_user_direct_reports = (
      object.reviewees.where(manager: current_employee).map(&:employee_id) + current_employee.direct_reports
    ).uniq

    {
      team_progress: {
        name: "Team's Progress",
        enabled: !object.archived? && (
          current_employee.team & object.reviewees.includes(:employee).map(&:employee)
        ).present?,
        reviewer_type_id: nil,
        reviewer_id: nil,
        page: 'manager_progress_view',
      },
      self_progress: {
        name: "Your Progress",
        enabled: false,
        reviewer_type_id: nil,
        reviewer_id: nil,
        page: 'self_progress_view',  
      }
    }
  end

  def manage_team_permissions
    manager_data = object.reviewers.joins(:reviewer_type)
      .where(employee: current_employee)
      .where(reviewer_types: { reviewer_type: 'manager' })
    current_user_direct_reports = (
      object.reviewees.where(manager: current_employee).map(&:employee_id) + current_employee.direct_reports
    ).uniq

    {
      manager: {
        name: 'Direct Report',
        enabled: !object.archived? && manager_data.present? &&
          fetch_review_cycle_phase('downward_review') && fetch_home_page_actions('downward_review'),
        reviewer_type_id: manager_data.present? ? manager_data.first.reviewer_type.id : nil,
        reviewer_id: nil,
        page: 'list_page',
      }.merge!(downward_review_progress(object, current_employee.id))
        .merge!(phase_access_type('downward_review')),
      your_org: {
        name: 'Your Org',
        enabled: !object.archived? &&
          (
            current_employee.team & object.reviewees.includes(:employee).where.not(
              employee_id: current_user_direct_reports,
            ).map(&:employee)
          ).present?,
        reviewer_type_id: nil,
        reviewer_id: nil,
        page: 'reviewee_list_page',
      },
      hierarchical_calibration: {
        name: 'Calibration',
        enabled: hierarchical_calibration_enabled?,
        page: 'form_page',
        access: false,
        access_mode: hierarchical_calibration_access_mode,
      },
    }
  end

  def hierarchical_calibration_phase
    @_hierarchical_calibration_phase ||= @object.review_cycle_phases.find_by(phase_type: 'hierarchical_calibration')
  end

  def calibrator
    return if hierarchical_calibration_phase.blank?

    @_calibrator ||= hierarchical_calibration_phase.calibrators.find_by(employee: current_employee)
  end

  def hierarchical_calibration_enabled?
    return false if hierarchical_calibration_phase.blank?
    return false if calibrator.blank?

    !calibrator.not_enabled?
  end

  def hierarchical_calibration_access_mode
    return 'Disabled Mode' if calibrator.blank?

    case calibrator.status
    when 'submitted'
      'Read Only Mode'
    when 'pending'
      'Read and Write Mode'
    when 'not_enabled'
      'Disabled Mode'
    end
  end

  def current_employee
    @_current_employee ||= @instance_options[:current_employee]
  end
end
