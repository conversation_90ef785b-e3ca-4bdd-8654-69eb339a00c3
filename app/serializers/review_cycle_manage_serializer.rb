class ReviewCycleManageSerializer < ActiveModel::Serializer
  attribute :id, if: -> { include_field?(:id) }
  attribute :title, if: -> { include_field?(:title) }
  attribute :review_type, if: -> { include_field?(:review_type) }
  attribute :start_date, if: -> { include_field?(:start_date) }
  attribute :creator_id, if: -> { include_field?(:creator_id) }
  attribute :review_cycle_phase, if: -> { include_field?(:review_cycle_phase) }
  attribute :status, if: -> { include_field?(:status) }
  attribute :metabase_reports, if: -> { include_field?(:metabase_reports) }
  attribute :review_goal_type, if: -> { include_field?(:review_goal_type) }
  attribute :review_cycle_goal_types, if: -> { include_field?(:review_cycle_goal_types) }
  attribute :show_key_results, if: -> { include_field?(:show_key_results) }
  attribute :archived_at, if: -> { include_field?(:archived_at) }
  attribute :charts_metadata, if: -> { include_field?(:charts_metadata) }
  attribute :reviewee_count, if: -> { include_field?(:reviewee_count) }
  attribute :reviewer_types_summary, if: -> { include_field?(:reviewer_types_summary) }
  attribute :review_cycle_summary, if: -> { include_field?(:review_cycle_summary) }
  attribute :review_cycle_phases, if: -> { include_field?(:review_cycle_phases) }
  attribute :using_custom_attribute, if: -> { include_field?(:using_custom_attribute) }
  attribute :employee_attributes, if: -> { include_field?(:employee_attributes) }
  attribute :default_channel, if: -> { include_field?(:default_channel) }
  attribute :automation_enabled, if: -> { include_field?(:automation_enabled) }
  attribute :has_same_visibility_config, if: -> { include_field?(:has_same_visibility_config) }
  has_many :goal_cycles, if: -> { include_field?(:goal_cycles) }
  attribute :user_roles, if: -> { include_field?(:user_roles) }
  attribute :peer_selection_role, if: -> { include_field?(:peer_selection_role) }

  attribute :reviewer_types, if: -> { include_field?(:reviewer_types) } do
    @object.reviewer_types.normal
  end

  attribute :tabs, if: -> { include_field?(:tabs) } do
    reviews_tabs = [
      {
        key: :analytics,
        visible: @object.reports.exists?(title: 'Analytics'),
        name: 'Analytics',
        is_metabase_report: true,
      },
      {
        key: :phases, visible: true, name: 'Phases', is_metabase_report: false
      },
      {
        key: :reviewees, visible: true, name: 'Reviewees', is_metabase_report: false
      },
      {
        key: :calibration, visible: true, name: 'Calibration', is_metabase_report: false
      },
      {
        key: :slack_logs,
        visible: @object.reports.exists?(title: 'Slack Logs'),
        name: 'Slack Logs',
        is_metabase_report: true,
      },
      {
        key: :email_logs,
        visible: @object.reports.exists?(title: 'Email Logs'),
        name: 'Email Logs',
        is_metabase_report: true,
      },
      {
        key: :manager_changes,
        visible: @object.reports.exists?(title: 'Manager Changes'),
        name: 'Manager Changes',
        is_metabase_report: true,
      },
      {
        key: :settings, visible: true, name: 'Settings', is_metabase_report: false
      },
    ]

    other_metabase_reports = @object.reports.where.not(title: ['Analytics', 'Slack Logs', 'Email Logs',
                                                               'Manager Changes'])
    if other_metabase_reports.exists?
      other_metabase_reports.each do |report|
        reviews_tabs.push({
                            key: report.title.parameterize.underscore.to_sym,
                            visible: true,
                            name: report.title,
                            is_metabase_report: true,
                          })
      end
    end
    reviews_tabs
  end

  attribute :review_cycle_phases_status, if: -> { include_field?(:review_cycle_phases_status, false) } do
    # if the rc has a end date and the end date is in the past
    # then the status is closed
    # if the rc has no end date and the rc has a phase that has notifications
    # then the status is started
    # if the rc has no end date and the rc has no phase that has notifications
    # then the status is not started
    if object.end_date.present? && object.end_date.to_i < Time.now.utc.to_i
      'CLOSED'
    elsif object.review_cycle_phases.joins(:review_cycle_notifications).present?
      'STARTED'
    else
      'NOT_STARTED'
    end
  end

  attribute :enforce_manager_change, if: -> { include_field?(:enforce_manager_change) } do
    {
      enabled: object.enforce_system_manager_mapping.present?,
      end_date: object.enforce_system_manager_mapping&.in_time_zone(@account.timezone)&.strftime('%Y-%m-%d'),
    }
  end

  def initialize(*args)
    super
    @account = @instance_options[:current_employee].account
    @employee = @instance_options[:current_employee]
    @fields = @instance_options[:fields].presence&.map(&:to_sym) || []
  end

  def include_field?(field, skip_blank = true)
    return true if @fields.blank? && skip_blank

    @fields.include?(field)
  end

  def using_custom_attribute
    ReviewCycleTemplate.joins(:reviewer_type)
      .where(reviewer_types: { review_cycle: @object, creation_source: :normal })
      .where.not(employee_attribute: nil)
      .exists?
  end

  def employee_attributes
    @object.employee_attribute_template_ids
  end

  def review_goal_type
    object.review_cycle_goal_type
  end

  def review_cycle_goal_types
    object.review_cycle_goal_types
  end

  def review_cycle_phases
    # ActiveModelSerializers::SerializableResource.new(
    #   @object.review_cycle_phases,
    #   { each_serializer: ReviewCyclePhasesSerializer, scope: { current_user: @employee.user } },
    # )
  end

  def metabase_reports
    where_condition = {}

    if include_field?(:analytics, false)
      where_condition[:title] = 'Analytics'
    elsif include_field?(:slack_logs, false)
      where_condition[:title] = 'Slack Logs'
    elsif include_field?(:email_logs, false)
      where_condition[:title] = 'Email Logs'
    elsif include_field?(:metabase_reports, false) && @fields[1..-1].count == 1
      where_condition[:title] = @fields.last.to_s.titleize
    end

    @object.reports.where(where_condition).includes(:employee).map do |attendee|
      ActiveModelSerializers::SerializableResource.new(
        attendee,
        {
          serializer: MetabaseReportsSerializer,
          scope: { account: @account, employee: @employee },
        },
      )
    end
  end

  def reviewee_count
    Pundit.policy_scope!(@employee.user, @object.reviewees).count(:id)
  end

  def reviewer_types_summary
    @object.reviewer_types_summary
  end

  def user_roles
    @object.user_roles
  end

  def peer_selection_role
    @object.peer_selection_role
  end

  def review_cycle_summary
    @object.review_cycle_summary
  end

  def status
    review_cycle_phase_criteria = @object.review_cycle_phases.joins(:review_cycle_notifications)
    if @object.status == 'live' &&
        @object.end_date.present? &&
        @object.end_date.to_i < Time.now.utc.to_i
      'closed'
    elsif @object.status == 'live' &&
        @object.end_date.nil? &&
        review_cycle_phase_criteria.present? &&
        review_cycle_phase_criteria.where(end_time: nil).blank? &&
        review_cycle_phase_criteria.where('end_time >= ?', Time.now.utc).blank?
      'complete'
    elsif @object.status == 'live' &&
        @object.end_date.nil? &&
        (review_cycle_phase_criteria.where(end_time: nil).blank? || review_cycle_phase_criteria.where('end_time >= ?',
                                                                                                      Time.now.utc).present?)
      'live'
    elsif @object.status == 'live' &&
        @object.end_date.nil? &&
        review_cycle_phase_criteria.blank?
      'not started'
    else
      @object.status

    end
  end
end
