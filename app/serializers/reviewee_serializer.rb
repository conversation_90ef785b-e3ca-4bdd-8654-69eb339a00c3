class RevieweeSerializer < ActiveModel::Serializer
  attributes :id
  attributes :self_review_done, :peer_selection_done,
             :peer_approval_done, :manager_summary_done,
             :release_review_done, :goal_approval_done,
             :goal_selection_done, :review_writing_percent,
             :competency_selection_done, :competency_approval_done

  attribute :manager_summary_status do
    if @object.reviewers.joins(:reviewer_type).where(reviewer_types: { reviewer_type: 'manager' }).first&.review_submitted
      'submitted'
    elsif @object.reviewers.joins(:reviewer_type).where(reviewer_types: { reviewer_type: 'manager' }).first&.review_responses&.count&.positive?
      'in_progress'
    else
      'not_started'
    end
  end

  attribute :reviewee_previous_managers do
    ActiveModelSerializers::SerializableResource.new(
       @object.all_previous_managers, each_serializer: ManagerSerializer
     )
  end

  belongs_to :employee, class_name: 'Employee'
  has_many :reviewers
  belongs_to :manager, class_name: 'Employee'

  class EmployeeSerializer < ActiveModel::Serializer
    attributes :id, :full_name
    attribute :profile_picture_url do |_object|
      @object.profile_picture
    end
    attribute :department do |_object|
      @object.department&.name
    end
  end

  class ReviewerSerializer < ActiveModel::Serializer
    attributes :id, :review_submitted
    belongs_to :employee, class_name: 'Employee'
    # belongs_to :nominator, class_name: 'Employee'
    # belongs_to :approver, class_name: 'Employee'
    # belongs_to :rejected_by, class_name: 'Employee'
    belongs_to :reviewer_type, class_name: 'Reviewer_type'
    class EmployeeSerializer < ActiveModel::Serializer
      attributes :id, :full_name

      attribute :profile_picture_url do |_object|
        @object.profile_picture
      end
      attribute :department do |_object|
        @object.department&.name
      end
    end

    class ReviewerTypeSerializer < ActiveModel::Serializer
      attributes :id, :reviewer_type
    end
  end
end
