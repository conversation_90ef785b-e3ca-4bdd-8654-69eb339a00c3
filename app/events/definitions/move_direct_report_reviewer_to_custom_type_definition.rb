# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]

module Events
  module Definitions
    class MoveDirectReportReviewerToCustomTypeDefinition < Events::Definitions::BaseEvent
      # Define typed attributes
      attribute :reviewer_employee_id, :integer
      attribute :reviewee_employee_id, :integer
      attribute :review_cycle_id, :integer

      validates :reviewer_employee_id, :reviewee_employee_id, :review_cycle_id,
                presence: true, numericality: { only_integer: true }
    end
  end
end
