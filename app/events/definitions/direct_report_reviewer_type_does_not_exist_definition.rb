# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
module Events
  module Definitions
    class DirectReportReviewerTypeDoesNotExistDefinition < Events::Definitions::BaseEvent
      # Define typed attributes
      attribute :review_cycle_id, :integer
      attribute :reviewer_employee_id, :integer
      attribute :reviewee_employee_id, :integer

      validates :review_cycle_id, :reviewer_employee_id, :reviewee_employee_id,
                presence: true, numericality: { only_integer: true }
    end
  end
end
