# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
module Events
  module Definitions
    class DiscardReviewerDefinition < Events::Definitions::BaseEvent
      # Define typed attributes with defaults
      attribute :reviewee_id, :integer
      attribute :review_cycle_id, :integer
      attribute :old_reviewer_employee_id, :integer
      attribute :reviewer_type, :string, default: 'manager'

      validates :reviewee_id, :review_cycle_id, :old_reviewer_employee_id,
                presence: true, numericality: { only_integer: true }

      validates :reviewer_type, presence: true
    end
  end
end
