# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]

module Events
  module Definitions
    class MoveOldManagerToCustomReviewerDefinition < Events::Definitions::BaseEvent
      # Define typed attributes
      attribute :employee_id, :integer
      attribute :reviewee_id, :integer
      attribute :review_cycle_id, :integer
      attribute :old_manager_id, :integer
      attribute :new_manager_id, :integer

      validates :employee_id, :reviewee_id, :review_cycle_id, :new_manager_id,
                presence: true, numericality: { only_integer: true }
      validates :old_manager_id, numericality: { only_integer: true }, allow_nil: true
    end
  end
end
