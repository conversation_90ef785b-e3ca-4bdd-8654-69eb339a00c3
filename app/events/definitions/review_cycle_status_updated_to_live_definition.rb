# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]

module Events
  module Definitions
    class ReviewCycleStatusUpdatedToLiveDefinition < Events::Definitions::BaseEvent
      # Define typed attributes
      attribute :review_cycle_id, :integer
      attribute :previous_status, :string
      attribute :new_status, :string

      validates :review_cycle_id,
                presence: true, numericality: { only_integer: true }
      validates :new_status, presence: true
      validates :previous_status, presence: true, allow_nil: true

      validate :status_changed_to_live

      private

      def status_changed_to_live
        return unless new_status != 'live'

        errors.add(:new_status, 'must be "live"')
      end
    end
  end
end
