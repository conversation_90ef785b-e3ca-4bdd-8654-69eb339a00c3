# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
module Events
  module Definitions
    class CreateNewReviewerTypeDefinition < Events::Definitions::BaseEvent
      # Define typed attributes with defaults
      attribute :employee_id, :integer
      attribute :reviewee_id, :integer
      attribute :review_cycle_id, :integer
      attribute :old_reviewer_employee_id, :integer
      attribute :new_manager_id, :integer
      attribute :reviewer_type, :string, default: 'manager'

      validates :employee_id, :reviewee_id, :review_cycle_id,
                presence: true, numericality: { only_integer: true }
      validates :old_reviewer_employee_id, :new_manager_id, numericality: { only_integer: true }, allow_nil: true
      validates :reviewer_type, presence: true
    end
  end
end
