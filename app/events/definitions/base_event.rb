# frozen_string_literal: true

module Events
  module Definitions
    class BaseEvent < RubyEventStore::Event
      include ActiveModel::Validations
      include ActiveModel::Attributes

      # Store default values for attributes
      @attribute_defaults = {}

      # Override the attribute class method to automatically define reader methods
      def self.attribute(name, type = :string, **options)
        super(name, type, **options)

        # Store default value if provided
        @attribute_defaults ||= {}
        @attribute_defaults[name.to_s] = options[:default] if options.key?(:default)

        # Define the reader method that extracts from data hash
        define_method(name) do
          extract_typed_attribute(name)
        end
      end

      # Access to attribute defaults
      def self.attribute_defaults
        @attribute_defaults ||= {}
      end

      # Converts the event to a hash representation for serialization
      #
      # @return [Hash] A hash containing event_id, metadata, and data
      #
      # @example
      #   event = CreateRevieweeSnapshotDefinition.new(data: { employee_id: 123 })
      #   event.to_h
      #   # => {
      #   #      event_id: "550e8400-e29b-41d4-a716-446655440000",
      #   #      metadata: { triggered_by: "system" },
      #   #      data: { employee_id: 123 }
      #   #    }
      def to_h
        {
          event_id: event_id,
          metadata: metadata.to_h,
          data: data,
        }
      end

      # Automatically defines reader methods for declared attributes
      # Each attribute declared with the `attribute` class method will have a reader method
      # that extracts the value from the event's data hash with proper type casting
      #
      # @example
      #   # In a child class:
      #   class CreateRevieweeSnapshotDefinition < BaseEvent
      #     attribute :employee_id, :integer
      #     attribute :reason, :string, default: 'manager_change'
      #   end
      #
      #   event = CreateRevieweeSnapshotDefinition.new(data: { employee_id: "123", reason: "custom" })
      #   event.employee_id  # => 123 (integer, type-cast from string)
      #   event.reason       # => "custom" (string)
      #
      #   event_with_defaults = CreateRevieweeSnapshotDefinition.new(data: { employee_id: "456" })
      #   event_with_defaults.reason  # => "manager_change" (default value)

      private

      # Extracts and type-casts an attribute value from the event's data hash
      # Handles both symbol and string keys, applies default values, and performs type casting
      #
      # @param attribute_name [Symbol] The name of the attribute to extract
      # @return [Object] The type-cast attribute value, default value, or nil
      #
      # @example
      #   # Given an event with:
      #   # - data: { employee_id: "123", name: "John", active: nil }
      #   # - attribute :employee_id, :integer
      #   # - attribute :name, :string, default: "Unknown"
      #   # - attribute :active, :boolean, default: true
      #
      #   extract_typed_attribute(:employee_id)  # => 123 (string "123" cast to integer)
      #   extract_typed_attribute(:name)         # => "John" (string value)
      #   extract_typed_attribute(:active)       # => true (default value, since data value is nil)
      #   extract_typed_attribute(:missing)      # => nil (no data, no default)
      def extract_typed_attribute(attribute_name)
        # Try to get value using both symbol and string keys for flexibility
        value = data[attribute_name] || data[attribute_name.to_s]

        # Return default if value is nil and a default exists
        if value.nil? && self.class.attribute_defaults.key?(attribute_name.to_s)
          return self.class.attribute_defaults[attribute_name.to_s]
        end

        # Return nil if no value and no default
        return nil if value.nil?

        # Type cast the value using ActiveModel::Attributes type system
        attribute_type = self.class.attribute_types[attribute_name.to_s]
        attribute_type.cast(value)
      end
    end
  end
end
