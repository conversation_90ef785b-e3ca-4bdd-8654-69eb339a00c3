# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
module Events
  module Definitions
    class CreateAndMigrateReviewFormsAndResponsesDefinition < Events::Definitions::BaseEvent
      # Define typed attributes
      attribute :reviewee_id, :integer
      attribute :old_reviewer_id, :integer
      attribute :new_reviewer_type_id, :integer
      attribute :old_reviewer_type_id, :integer

      validates :reviewee_id, :old_reviewer_id, :new_reviewer_type_id, :old_reviewer_type_id,
                presence: true, numericality: { only_integer: true }
    end
  end
end
