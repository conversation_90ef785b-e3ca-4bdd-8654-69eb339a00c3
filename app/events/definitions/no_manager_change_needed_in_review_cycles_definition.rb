# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]

module Events
  module Definitions
    class NoManagerChangeNeededInReviewCyclesDefinition < Events::Definitions::BaseEvent
      # Define typed attributes
      attribute :employee_id, :integer
      attribute :previous_manager_id, :integer
      attribute :new_manager_id, :integer
      attribute :reason, :string

      validates :employee_id, :new_manager_id,
                presence: true, numericality: { only_integer: true }
      validates :previous_manager_id, numericality: { only_integer: true }, allow_nil: true
    end
  end
end
