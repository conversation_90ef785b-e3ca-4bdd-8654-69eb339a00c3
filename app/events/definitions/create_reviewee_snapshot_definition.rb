# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
module Events
  module Definitions
    class CreateRevieweeSnapshotDefinition < Events::Definitions::BaseEvent
      # Define typed attributes with defaults
      attribute :employee_id, :integer
      attribute :reviewee_id, :integer
      attribute :review_cycle_id, :integer
      attribute :old_employee_id, :integer
      attribute :new_manager_id, :integer
      attribute :reason, :string, default: 'manager_change'

      validates :employee_id, :reviewee_id, :review_cycle_id, :old_employee_id,
                presence: true, numericality: { only_integer: true }
      validates :new_manager_id, numericality: { only_integer: true }, allow_nil: true
      validates :reason, presence: true
    end
  end
end
