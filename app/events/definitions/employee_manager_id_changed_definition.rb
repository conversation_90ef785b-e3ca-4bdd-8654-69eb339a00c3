Dir[Rails.root.join('app/events/**/*.rb')]

module Events
  module Definitions
    class EmployeeManagerIdChangedDefinition < Events::Definitions::BaseEvent
      # Define typed attributes
      attribute :employee_id, :integer
      attribute :previous_manager_id, :integer
      attribute :new_manager_id, :integer

      validates :employee_id, :previous_manager_id, :new_manager_id,
                presence: true, numericality: { only_integer: true }
      validate :manager_changed

      def manager_changed
        return if new_manager_id != previous_manager_id

        errors.add(:base, 'Manager ID must change')
      end
    end
  end
end
