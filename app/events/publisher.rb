# frozen_string_literal: true

# app/events/publisher.rb

module Events
  class Publisher
    def self.publish(event:, stream_name:, metadata: {}, async: true)
      # Validate event before publishing
      unless event.valid?
        Rails.logger.error("Failed to publish #{event.class.name}: #{event.errors.full_messages}")
        raise ArgumentError, "Invalid event: #{event.errors.full_messages.join(', ')}"
      end

      enriched_event = build_enriched_event(event, metadata)
      event_store.publish(enriched_event, stream_name: stream_name)

      # Dispatch handlers based on async parameter
      if async
        # Asynchronous execution via DelayedJob
        EventDispatcherJob.perform_later(
          event.class.name.demodulize,
          enriched_event.data,
        )
      else
        # Synchronous execution - dispatch immediately
        Events::EventDispatcher.dispatch(enriched_event, async: false)
      end
    end

    def self.build_enriched_event(event, metadata)
      enriched_metadata = { timestamp: Time.current.utc }.merge(metadata)
      event.class.new(data: event.data, metadata: enriched_metadata)
    end

    def self.event_store
      Rails.configuration.event_store
    end
  end
end
