# app/events/event_dispatcher.rb

module Events
  module EventDispatcher
    def self.dispatch(event, async: true)
      Events::Handlers::SubscriptionRegistry.mappings.each do |handler_class, event_classes|
        next unless event_classes.include?(event.class)

        if async
          handler_class.delay.call_later(
            event.class.name,
            event.to_h,
          )
        else
          handler_class.call_now(event.class.name, event.to_h)
        end
      rescue StandardError => e
        Rails.logger.error("<PERSON>sp<PERSON> failed for #{event.class.name} in #{handler_class.name}: #{e.message}")
        Sentry.capture_exception(e)
      end
    end
  end
end
