# Event Handlers - Dynamic Template Pattern

This directory contains event handlers that follow the **Dynamic Handler Template Pattern**, a standardized approach for implementing event handlers in our event-driven architecture.

## 🏗️ Architecture Overview

All event handlers inherit from `BaseHandler` and follow a consistent template structure that provides:
- **Uniform code structure** across all handlers
- **Automatic error handling** with logging and Sentry integration
- **Standardized event publishing** with metadata enrichment
- **Consistent completion logging** and messaging
- **Reusable helper methods** for common operations

## 📋 Template Structure

Every handler follows this standardized flow:

```ruby
def call(event)
  validate_event_type(event, expected_event_type)
  return if should_skip_processing?(event)

  result = execute_handler_logic(event)
  log_completion(event, result) if should_log_completion?
  result
rescue StandardError => e
  handle_error(event, e)
  raise
end
```

## 🔧 Implementation Requirements

### Required Methods

Every handler **must** implement these methods:

#### `expected_event_type`
Returns the event definition class this handler expects.

```ruby
def expected_event_type
  Events::Definitions::YourEventDefinition
end
```

#### `execute_handler_logic(event)`
Contains the core business logic for the handler.

```ruby
def execute_handler_logic(event)
  # Your business logic here
  service_result = handle_service_result(
    YourService.call(param: event.param),
    "Error message for logging"
  )
  return unless service_result

  # Additional logic...
  service_result.result
end
```

### Optional Methods

Handlers can override these methods for custom behavior:

#### `should_skip_processing?(event)`
Define conditions when the handler should skip processing.

```ruby
def should_skip_processing?(event)
  return true if event.some_field.blank?
  # Additional skip conditions...
  false
end
```

#### `completion_message(event, result)`
Provide custom completion logging message.

```ruby
def completion_message(event, result)
  "YourHandler completed for #{event.id}: #{result[:status]}"
end
```

#### `should_log_completion?`
Control whether completion should be logged (default: true).

```ruby
def should_log_completion?
  false # Disable completion logging
end
```

## 🛠️ Helper Methods Available

The `BaseHandler` provides these helper methods:

### `handle_service_result(service_result, error_message)`
Handles service results with automatic error logging.

```ruby
service_result = handle_service_result(
  SomeService.call(params),
  "Service failed for entity #{event.id}"
)
return unless service_result # Returns nil if service failed
```

### `publish_event(event, stream_name, original_event, **metadata)`
Publishes events with automatic metadata enrichment.

```ruby
publish_event(
  new_event,
  EventStore::StreamNames.for_employee(event.employee_id),
  event, # original event for metadata
  custom_field: "value" # additional metadata
)
```

### `build_metadata(original_event, **additional_metadata)`
Builds standardized metadata for events.

```ruby
metadata = build_metadata(
  event,
  custom_field: "value",
  another_field: result[:data]
)
```

### `event_identifier(event)`
Generates a consistent identifier for logging.

```ruby
identifier = event_identifier(event) # Returns class name + event_id
```

## 📝 Creating New Handlers

### Step 1: Create Handler Class

```ruby
# app/events/handlers/your_feature_handler.rb
module Events
  module Handlers
    class YourFeatureHandler < Events::Handlers::BaseHandler
      private

      def expected_event_type
        Events::Definitions::YourFeatureDefinition
      end

      # Optional additional definitions
      def self.additional_definitions
        [Events::Definitions::AnotherDefinition]
      end

      # OR as a constant
      ADDITIONAL_DEFINITIONS = [Events::Definitions::AnotherDefinition]

      def execute_handler_logic(event)
        # Implement your business logic here
        service_result = handle_service_result(
          YourFeature::ProcessEvent.call(
            param1: event.param1,
            param2: event.param2
          ),
          "YourFeature processing failed for #{event.id}"
        )
        return unless service_result

        result = service_result.result

        # Publish follow-up events if needed
        if result[:should_publish_follow_up]
          publish_follow_up_event(event, result)
        end

        result
      end

      def completion_message(event, result)
        "YourFeatureHandler completed for #{event.id}: #{result[:status]}"
      end

      # Helper methods for this handler
      def publish_follow_up_event(original_event, result)
        follow_up_event = Events::Definitions::FollowUpDefinition.new(
          data: {
            entity_id: result[:entity_id],
            status: result[:status]
          }.with_indifferent_access
        )

        publish_event(
          follow_up_event,
          EventStore::StreamNames.for_entity(result[:entity_id]),
          original_event,
          processing_result: result
        )
      end
    end
  end
end
```

### Step 2: Register Handler

Handlers are now automatically discovered and registered through our enhanced SubscriptionRegistry:

```ruby
module Events
  module Handlers
    module SubscriptionRegistry
      def self.mappings
        # Discover all handler classes
        handlers = Events::Handlers.constants
          .reject { |c| c == :BaseHandler }
          .select { |c| Events::Handlers.const_get(c).is_a?(Class) }
          .map { |c| Events::Handlers.const_get(c) }

        # Build dynamic mappings
        handlers.each_with_object({}) do |handler, hash|
          primary_definition = handler.new.expected_event_type rescue nil
          definitions = [primary_definition].compact
          
          # Support multiple definitions
          if handler.respond_to?(:additional_definitions)
            definitions += Array(handler.additional_definitions)
          elsif handler.const_defined?(:ADDITIONAL_DEFINITIONS, false)
            definitions += Array(handler::ADDITIONAL_DEFINITIONS)
          end

          hash[handler] = definitions.uniq.compact unless definitions.empty?
        end
      end
    end
  end
end

## 🎯 Best Practices

### 1. Keep Business Logic in Services
Handlers should orchestrate service calls, not contain complex business logic.

### 2. Use Helper Methods
Leverage `handle_service_result` and `publish_event` for consistency.

### 3. Provide Clear Error Messages
Include relevant context in error messages for debugging.

### 4. Follow Naming Conventions
- Handler classes: `[Feature][Action]Handler`
- Methods: Use descriptive names for helper methods

### 5. Test Thoroughly
Test both success and failure scenarios, including edge cases.

## 🔍 Pattern Benefits

- **Consistency**: All handlers follow identical structure
- **Maintainability**: Common patterns centralized in BaseHandler
- **Reusability**: Template can be used across different features
- **Error Handling**: Standardized error handling with logging and Sentry
- **Metadata**: Automatic metadata enrichment for event tracing
- **Testing**: Predictable structure makes testing easier

## 📚 Examples

See existing handlers in this directory for real-world examples:
- `create_reviewee_snapshot_handler.rb` - Simple handler with service call
- `employee_manager_id_changed_handler.rb` - Handler with event publishing
- `validate_manager_change_in_review_cycles_handler.rb` - Complex handler with conditional logic
- `update_reviewee_manager_handler.rb` - Handler with skip conditions

## 🚀 Migration Guide

When updating existing handlers to use this pattern:

1. Move business logic to `execute_handler_logic`
2. Replace manual type checking with `expected_event_type`
3. Use `handle_service_result` for service calls
4. Replace manual event publishing with `publish_event`
5. Add custom `completion_message` if needed
6. Update tests to match new structure

This pattern ensures all handlers are maintainable, testable, and follow consistent conventions across the entire application.
