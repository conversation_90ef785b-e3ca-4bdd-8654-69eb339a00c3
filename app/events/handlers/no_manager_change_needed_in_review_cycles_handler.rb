# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]

module Events
  module Handlers
    class NoManagerChangeNeededInReviewCyclesHandler < Events::Handlers::BaseHandler
      

      def expected_event_type
        Events::Definitions::NoManagerChangeNeededInReviewCyclesDefinition
      end
      private

      def execute_handler_logic(event)
        # Log the reason why no changes were needed
        Rails.logger.info("No manager change needed for employee #{event.employee_id}. Reason: #{event.reason}")

        # Optionally perform cleanup or notification tasks
        log_detailed_reason(event)

        # TODO: Add any additional logic needed when no changes are required
        # For example:
        # - Send notifications to admins about manager changes that don't affect reviews
        # - Update audit logs
        # - Trigger other non-review-related manager change processes
      end

      def log_detailed_reason(event)
        case event.reason
        when 'no_eligible_review_cycles'
          Rails.logger.debug do
            "Employee #{event.employee_id}: No review cycles with enforce_system_manager_mapping enabled"
          end
        when 'employee_not_in_review_cycles'
          Rails.logger.debug { "Employee #{event.employee_id}: Not participating in any eligible review cycles" }
        when 'all_reviewees_already_have_correct_manager'
          Rails.logger.debug { "Employee #{event.employee_id}: All reviewee records already have correct manager" }
        end
      end
    end
  end
end
