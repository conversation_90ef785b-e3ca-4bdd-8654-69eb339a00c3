module Events
  module Handlers
    module Subscription<PERSON><PERSON><PERSON><PERSON>
      def self.mappings
        # Get all handler classes in the Events::Handlers namespace
        handlers = Events::Handlers.constants
          .reject { |c| c == :BaseHandler } # exclude the base handler
          .select { |c| Events::Handlers.const_get(c).is_a?(Class) }
          .map { |c| Events::Handlers.const_get(c) }

        # Create mappings automatically with support for multiple definitions
        handlers.each_with_object({}) do |handler, hash|
          # Get primary definition from expected_event_type
          primary_definition = begin
            handler.new.expected_event_type
          rescue StandardError
            nil
          end

          # Initialize array for this handler
          definitions = []
          definitions << primary_definition if primary_definition

          # Look for additional definitions via method or constant
          if handler.respond_to?(:additional_definitions)
            definitions += Array(handler.additional_definitions)
          elsif handler.const_defined?(:ADDITIONAL_DEFINITIONS, false)
            definitions += Array(handler::ADDITIONAL_DEFINITIONS)
          end

          # Only add to hash if we found definitions
          hash[handler] = definitions.uniq.compact unless definitions.empty?
        end
      end
    end
  end
end
