# frozen_string_literal: true

module Events
  module Handlers
    class UpdateRevieweeManagerHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::UpdateRevieweeManagerDefinition
      end

      private

      def execute_handler_logic(event)
        service = ManagerChanges::UpdateRevieweeManager.call(
          event.reviewee_id,
          event.review_cycle_id,
          event.new_manager_id,
        )

        if service.success?
          {
            old_manager_id: service.result[:old_manager_id],
            new_manager_id: event.new_manager_id,
          }
        else
          handle_service_result(service, 'Failed to update reviewee manager')
          nil
        end
      end

      def completion_message(event, result)
        if result
          "UpdateRevieweeManagerHandler updated reviewee #{event.reviewee_id} manager " \
            "from #{result[:old_manager_id]} to #{result[:new_manager_id]}"
        else
          "UpdateRevieweeManagerHandler skipped processing for reviewee #{event.reviewee_id}"
        end
      end
    end
  end
end
