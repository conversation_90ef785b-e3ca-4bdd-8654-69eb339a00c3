# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
Dir[Rails.root.join('app/event_store/**/*.rb')]
module Events
  module Handlers
    class EmployeeManagerIdChangedHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::EmployeeManagerIdChangedDefinition
      end

      private

      def execute_handler_logic(event)
        broadcast_manager_change(event)
      end

      def completion_message(event, _result)
        "[Events] EmployeeManagerIdChangedHandler processed for employee #{event.employee_id} " \
          "(#{event.previous_manager_id} → #{event.new_manager_id})"
      end

      def broadcast_manager_change(event)
        payload = {
          employee_id: event.employee_id,
          previous_manager_id: event.previous_manager_id,
          new_manager_id: event.new_manager_id,
        }.with_indifferent_access

        # This could grow in the future: you can fan out to multiple domains
        publish_event(
          Events::Definitions::ValidateManagerChangeInReviewCyclesDefinition.new(data: payload),
          EventStore::StreamNames.for_employee(event.employee_id),
          event,
          account_id: event.metadata[:account_id],
          async: true,
        )
      end
    end
  end
end
