# frozen_string_literal: true

module Events
  module Handlers
    class CreateNewReviewerTypeHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::CreateNewReviewerTypeDefinition
      end

      private

      def execute_handler_logic(event)
        reviewer_type_service = handle_service_result(
          ManagerChanges::CreateNewReviewerType.call(
            event.review_cycle_id,
            event.reviewer_type,
            event.old_reviewer_employee_id,
            event.reviewee_id,
          ),
          "Failed to create new reviewer type for review cycle #{event.review_cycle_id} in event id #{event.event_id}",
        )
        return unless reviewer_type_service

        result = reviewer_type_service.result
        old_reviewer_updated = result[:old_reviewer_updated]
        reviewer_type_created = result[:reviewer_type_created]

        # Fire the forms migration event if we have the necessary data
        if should_publish_forms_migration?(old_reviewer_updated, reviewer_type_created)
          publish_forms_migration_event(event, result)
        end

        result
      end

      def completion_message(event, result)
        if result && should_publish_forms_migration?(result[:old_reviewer_updated], result[:reviewer_type_created])
          "CreateNewReviewerTypeHandler completed for review cycle #{event.review_cycle_id}: " \
            "reviewer type #{result[:reviewer_type_created][:reviewer_type_name]} created, forms migration event published"
        else
          "CreateNewReviewerTypeHandler completed for review cycle #{event.review_cycle_id}: " \
            'reviewer type created, skipping forms migration'
        end
      end

      def should_publish_forms_migration?(old_reviewer_updated, reviewer_type_created)
        old_reviewer_updated[:updated] && reviewer_type_created[:former_reviewer_type].present?
      end

      def publish_forms_migration_event(event, result)
        stream = EventStore::StreamNames.for_employee(event.employee_id)

        create_forms_event = Events::Definitions::CreateAndMigrateReviewFormsAndResponsesDefinition.new(
          data: {
            reviewee_id: event.reviewee_id,
            old_reviewer_id: result[:old_reviewer_updated][:reviewer_id],
            new_reviewer_type_id: result[:new_reviewer_type_id],
            old_reviewer_type_id: result[:old_reviewer_type_id],
          }.with_indifferent_access,
        )

        publish_event(create_forms_event, stream, event)
      end
    end
  end
end
