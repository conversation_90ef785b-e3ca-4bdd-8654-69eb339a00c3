# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
Dir[Rails.root.join('app/event_store/**/*.rb')]

module Events
  module Handlers
    class CreateAndMigrateReviewFormsAndResponsesHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::CreateAndMigrateReviewFormsAndResponsesDefinition
      end

      private

      def should_skip_processing?(event)
        event.old_reviewer_id.blank?
      end

      def execute_handler_logic(event)
        service_result = ManagerChanges::CreateAndMigrateReviewFormsAndResponses.call(
          reviewee_id: event.reviewee_id,
          old_reviewer_id: event.old_reviewer_id,
          new_reviewer_type_id: event.new_reviewer_type_id,
          old_reviewer_type_id: event.old_reviewer_type_id,
        )

        unless service_result.success?
          Rails.logger.error("CreateAndMigrateReviewFormsAndResponsesHandler failed for reviewee #{event.reviewee_id}: #{service_result.errors}")
          raise StandardError, "Service failed: #{service_result.errors}"
        end

        # Send silent notification after successful forms migration
        send_silent_notification(event)

        service_result.result
      end

      def completion_message(event, result)
        "CreateAndMigrateReviewFormsAndResponsesHandler completed for reviewee #{event.reviewee_id}: #{result}"
      end

      def send_silent_notification(event)
        # Call the silent notification service
        notification_result = ManagerChanges::SendReviewerPhaseSilentNotification.call(
          reviewer_type_id: event.new_reviewer_type_id,
          reviewer_id: event.old_reviewer_id,
        )

        if notification_result
          Rails.logger.info("Silent notification sent successfully for reviewee #{event.reviewee_id}")
        else
          Rails.logger.warn("Silent notification failed for reviewee #{event.reviewee_id}")
        end
      rescue StandardError => e
        Rails.logger.error("Failed to send silent notification for reviewee #{event.reviewee_id}: #{e.message}")
        # Don't re-raise the error as this is a non-critical operation
      end
    end
  end
end
