# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
Dir[Rails.root.join('app/event_store/**/*.rb')]
module Events
  module Handlers
    class MoveDirectReportReviewerToCustomTypeHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::MoveDirectReportReviewerToCustomTypeDefinition
      end

      private

      def execute_handler_logic(event)
        service_result = handle_service_result(
          ManagerChanges::MoveDirectReportReviewerToCustomType.call(
            reviewer_employee_id: event.reviewer_employee_id,
            review_cycle_id: event.review_cycle_id,
            reviewee_employee_id: event.reviewee_employee_id,
          ),
          "MoveDirectReportReviewerToCustomType service failed for reviewee_employee_id #{event.reviewee_employee_id} in event id #{event.event_id}",
        )
        return unless service_result

        result = service_result.result

        # Check if the result indicates a failure (service succeeded but business logic failed)
        if result.is_a?(Hash) && result[:success] == false
          publish_failure_event(event, result)
          return result
        end

        # 1. Publish create reviewee snapshot event (synchronous)
        publish_create_reviewee_snapshot_event(event, result)

        # 2. Check if direct_report reviewer type phase has been launched before triggering remaining events
        if should_trigger_remaining_events?(event.review_cycle_id, event.reviewer_employee_id, 'direct_report',
                                            event.reviewee_employee_id)
          # 3. Publish CreateNewReviewerType event
          publish_create_new_reviewer_type_event(event, result)
        else
          # 4. if we are not moving the reviewer, then discard, the reviewer
          publish_reviewer_discard_event(event, result)
          Rails.logger.info("Skipping remaining events for reviewee_employee_id #{event.reviewee_employee_id}: direct_report reviewer type phase not launched")
        end

        result
      end

      def completion_message(event, _result)
        'MoveDirectReportReviewerToCustomTypeHandler completed for reviewee_employee_id ' \
          "#{event.reviewee_employee_id} in cycle #{event.review_cycle_id}"
      end

      def publish_reviewer_discard_event(original_event, result)
        discard_reviewer_event = Events::Definitions::DiscardReviewerDefinition.new(
          data: {
            reviewee_id: result[:reviewee_id],
            review_cycle_id: result[:review_cycle_id],
            old_reviewer_employee_id: result[:reviewer_employee_id],
            reviewer_type: 'direct_report',
          }.with_indifferent_access,
        )

        publish_event(
          discard_reviewer_event,
          EventStore::StreamNames.for_employee(original_event.reviewer_employee_id),
          original_event,
        )
      end

      def should_trigger_remaining_events?(review_cycle_id, reviewer_employee_id, target_reviewer_type, reviewee_employee_id)
        phase_check_service = ManagerChanges::CheckReviewerTypePhaseLaunched.call(
          review_cycle_id: review_cycle_id,
          reviewer_employee_id: reviewer_employee_id,
          target_reviewer_type: target_reviewer_type,
          reviewee_employee_id: reviewee_employee_id,
        )
        phase_check_service.success? && phase_check_service.result
      end

      def publish_create_reviewee_snapshot_event(original_event, result)
        Rails.logger.info("Publishing CreateRevieweeSnapshot event for reviewee #{result[:reviewee_id]} in cycle #{result[:review_cycle_id]}")

        snapshot_event = Events::Definitions::CreateRevieweeSnapshotDefinition.new(
          data: {
            employee_id: original_event.reviewer_employee_id,
            reviewee_id: result[:reviewee_id],
            review_cycle_id: result[:review_cycle_id],
            old_employee_id: result[:reviewer_employee_id],
            new_manager_id: nil,
            reason: 'direct_report_change',
          }.with_indifferent_access,
        )

        unless snapshot_event.valid?
          Rails.logger.error("Invalid CreateRevieweeSnapshotDefinition event: #{snapshot_event.errors.full_messages}")
          return
        end

        publish_event(
          snapshot_event,
          EventStore::StreamNames.for_employee(original_event.reviewer_employee_id),
          original_event,
          original_reviewer_id: result[:existing_reviewer_id],
          manager_reviewee_id: result[:manager_reviewee_id],
        )

        Rails.logger.info("CreateRevieweeSnapshot event published for employee #{original_event.reviewer_employee_id}")
      end

      def publish_create_new_reviewer_type_event(original_event, result)
        Rails.logger.info("Publishing CreateNewReviewerType event for reviewee #{result[:reviewee_id]} in cycle #{result[:review_cycle_id]}")

        follow_up_event = Events::Definitions::CreateNewReviewerTypeDefinition.new(
          data: {
            employee_id: original_event.reviewer_employee_id,
            reviewee_id: result[:reviewee_id],
            review_cycle_id: result[:review_cycle_id],
            old_reviewer_employee_id: result[:reviewer_employee_id],
            new_manager_id: nil,
            reviewer_type: 'direct_report',
          }.with_indifferent_access,
        )

        unless follow_up_event.valid?
          Rails.logger.error("Invalid CreateNewReviewerTypeDefinition event: #{follow_up_event.errors.full_messages}")
          return
        end

        publish_event(
          follow_up_event,
          EventStore::StreamNames.for_employee(original_event.reviewer_employee_id),
          original_event,
          original_reviewer_id: result[:existing_reviewer_id],
          manager_reviewee_id: result[:manager_reviewee_id],
        )

        Rails.logger.info("CreateNewReviewerType event published for employee #{original_event.reviewer_employee_id}")
      end

      def publish_failure_event(original_event, service_result)
        Rails.logger.info("Publishing MoveDirectReportReviewerFailed event for reason: #{service_result[:reason]}")

        event = Events::Definitions::MoveDirectReportReviewerFailedDefinition.new(
          data: {
            review_cycle_id: service_result[:review_cycle_id],
            reviewer_employee_id: service_result[:reviewer_employee_id],
            reviewee_employee_id: service_result[:reviewee_employee_id],
            reason: service_result[:reason],
          }.with_indifferent_access,
        )

        # Validate the event before publishing
        unless event.valid?
          Rails.logger.error("Invalid MoveDirectReportReviewerFailedDefinition event: #{event.errors.full_messages}")
          return
        end

        # Publish the event to the employee stream with complete result in metadata
        publish_event(
          event,
          EventStore::StreamNames.for_employee(service_result[:reviewer_employee_id]),
          original_event,
          review_cycle_id: service_result[:review_cycle_id],
          complete_service_result: service_result,
        )

        Rails.logger.warn("MoveDirectReportReviewerToCustomType failed: #{service_result[:reason]} - event published")
      end
    end
  end
end
