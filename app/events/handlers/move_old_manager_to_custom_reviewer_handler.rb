# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]

module Events
  module Handlers
    class MoveOldManagerToCustomReviewerHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::MoveOldManagerToCustomReviewerDefinition
      end

      private

      def should_skip_processing?(event)
        event.old_manager_id.blank?
      end

      def execute_handler_logic(event)
        stream = EventStore::StreamNames.for_employee(event.employee_id)

        # 1. Publish create reviewee snapshot event (synchronous)
        publish_create_snapshot_event(event, stream)

        # 2. Check if manager reviewer type phase has been launched before triggering remaining events
        if should_trigger_remaining_events?(event.review_cycle_id, event.old_manager_id, 'manager', event.employee_id)
          # 3. Publish create new reviewer type event (synchronous)
          publish_create_reviewer_type_event(event, stream)
        else
          # 4. if we are not moving the reviewer, then discard, the reviewer
          publish_reviewer_discard_event(event, stream)

          Rails.logger.info(
            "Skipping remaining events for reviewee #{event.reviewee_id}: manager reviewer type phase not launched",
          )
        end
      end

      def completion_message(event, _result)
        "MoveOldManagerToCustomReviewerHandler completed for reviewee #{event.reviewee_id}: " \
          'snapshot event published, reviewer type event published'
      end

      def publish_reviewer_discard_event(event, stream)
        discard_reviewer_event = Events::Definitions::DiscardReviewerDefinition.new(
          data: {
            reviewee_id: event.reviewee_id,
            review_cycle_id: event.review_cycle_id,
            old_reviewer_employee_id: event.old_manager_id,
            reviewer_type: 'manager',
          }.with_indifferent_access,
        )

        publish_event(discard_reviewer_event, stream, event)
      end

      def should_trigger_remaining_events?(review_cycle_id, reviewer_employee_id, target_reviewer_type, reviewee_employee_id)
        phase_check_service = ManagerChanges::CheckReviewerTypePhaseLaunched.call(
          review_cycle_id: review_cycle_id,
          reviewer_employee_id: reviewer_employee_id,
          target_reviewer_type: target_reviewer_type,
          reviewee_employee_id: reviewee_employee_id,
        )
        phase_check_service.success? && phase_check_service.result
      end

      def publish_create_snapshot_event(event, stream)
        create_snapshot_event = Events::Definitions::CreateRevieweeSnapshotDefinition.new(
          data: {
            employee_id: event.employee_id,
            reviewee_id: event.reviewee_id,
            review_cycle_id: event.review_cycle_id,
            old_employee_id: event.old_manager_id,
            new_manager_id: event.new_manager_id,
            reason: 'manager_change',
          }.with_indifferent_access,
        )

        publish_event(create_snapshot_event, stream, event)
      end

      def publish_create_reviewer_type_event(event, stream)
        create_reviewer_type_event = Events::Definitions::CreateNewReviewerTypeDefinition.new(
          data: {
            employee_id: event.employee_id,
            reviewee_id: event.reviewee_id,
            review_cycle_id: event.review_cycle_id,
            old_reviewer_employee_id: event.old_manager_id,
            new_manager_id: event.new_manager_id,
            reviewer_type: 'manager',
          }.with_indifferent_access,
        )

        publish_event(create_reviewer_type_event, stream, event)
      end
    end
  end
end
