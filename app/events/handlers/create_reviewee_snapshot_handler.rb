# frozen_string_literal: true

module Events
  module Handlers
    class CreateRevieweeSnapshotHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::CreateRevieweeSnapshotDefinition
      end

      private

      def should_skip_processing?(event)
        event.old_employee_id.blank?
      end

      def execute_handler_logic(event)
        snapshot_service = handle_service_result(
          ManagerChanges::CreateRevieweeSnapshot.call(
            event.reviewee_id,
            event.review_cycle_id,
            event.old_employee_id,
            event.reason,
          ),
          "Failed to create reviewee snapshot for reviewee #{event.reviewee_id} in event id #{event.event_id}",
        )
        return unless snapshot_service

        snapshot_service.result
      end

      def completion_message(event, result)
        "CreateRevieweeSnapshotHandler completed for reviewee #{event.reviewee_id}: " \
          "snapshot #{result[:snapshot_id]} created"
      end
    end
  end
end
