module Events
  module Handlers
    class DiscardReviewerHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::DiscardReviewerDefinition
      end

      private

      def execute_handler_logic(event)
        discard_service = handle_service_result(
          ManagerChanges::DiscardReviewer.call(
            event.review_cycle_id,
            event.reviewer_type,
            event.old_reviewer_employee_id,
            event.reviewee_id,
          ),
          "Failed to discard reviewer for reviewee #{event.reviewee_id} in event id #{event.event_id}",
        )
        return unless discard_service

        discard_service.result
      end

      def completion_message(event, _result)
        "DiscardReviewerHandler completed for reviewee #{event.reviewee_id}: " \
          'reviewer discarded'
      end
    end
  end
end
