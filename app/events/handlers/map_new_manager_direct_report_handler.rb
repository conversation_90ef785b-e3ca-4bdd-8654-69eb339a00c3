# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
Dir[Rails.root.join('app/event_store/**/*.rb')]

module Events
  module Handlers
    class MapNewManagerDirectReportHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::MapNewManagerDirectReportDefinition
      end

      private

      def execute_handler_logic(event)
        service_result = handle_service_result(
          ManagerChanges::MapNewManagerDirectReport.call(
            reviewer_employee_id: event.reviewer_employee_id,
            review_cycle_id: event.review_cycle_id,
            reviewee_employee_id: event.reviewee_employee_id,
          ),
          "MapNewManagerDirectReport service failed for reviewee_employee_id #{event.reviewee_employee_id} in event id #{event.event_id}",
        )
        return unless service_result

        result = service_result.result

        # Check if we need to publish follow-up events based on the result
        if result[:direct_report_mapped] == false
          publish_failure_event(event, result)
        end

        result
      end

      def completion_message(event, _result)
        'MapNewManagerDirectReportHandler completed for reviewee_employee_id ' \
          "#{event.reviewee_employee_id} in cycle #{event.review_cycle_id}"
      end

      def publish_failure_event(original_event, result)
        case result[:reason]
        when 'direct_report_reviewer_type_not_exists'
          publish_direct_report_reviewer_type_not_exists_event(original_event, result)
        when 'manager_does_not_exist_as_reviewee'
          publish_manager_does_not_exist_as_reviewee_event(original_event, result)
        end
      end

      def publish_direct_report_reviewer_type_not_exists_event(original_event, result)
        Rails.logger.info("Publishing DirectReportReviewerTypeDoesNotExist event for cycle #{result[:review_cycle_id]}")

        # Create the event
        event = Events::Definitions::DirectReportReviewerTypeDoesNotExistDefinition.new(
          data: {
            review_cycle_id: result[:review_cycle_id],
            reviewer_employee_id: result[:reviewer_employee_id],
            reviewee_employee_id: result[:reviewee_employee_id],
          }.with_indifferent_access,
        )

        # Validate the event before publishing
        unless event.valid?
          Rails.logger.error("Invalid DirectReportReviewerTypeDoesNotExistDefinition event: #{event.errors.full_messages}")
          return
        end

        # Publish the event to the employee stream
        publish_event(
          event,
          EventStore::StreamNames.for_employee(result[:reviewer_employee_id]),
          original_event,
          review_cycle_id: result[:review_cycle_id],
        )

        Rails.logger.warn("DirectReportReviewerType does not exist for review cycle #{result[:review_cycle_id]} - event published")
      end

      def publish_manager_does_not_exist_as_reviewee_event(original_event, result)
        Rails.logger.info("Publishing ManagerDoesNotExistAsReviewee event for employee #{result[:reviewee_employee_id]} in cycle #{result[:review_cycle_id]}")

        # Create the event
        event = Events::Definitions::ManagerDoesNotExistAsRevieweeDefinition.new(
          data: {
            review_cycle_id: result[:review_cycle_id],
            reviewer_employee_id: result[:reviewer_employee_id],
            reviewee_employee_id: result[:reviewee_employee_id],
          }.with_indifferent_access,
        )

        # Validate the event before publishing
        unless event.valid?
          Rails.logger.error("Invalid ManagerDoesNotExistAsRevieweeDefinition event: #{event.errors.full_messages}")
          return
        end

        # Publish the event to the employee stream
        publish_event(
          event,
          EventStore::StreamNames.for_employee(result[:reviewer_employee_id]),
          original_event,
          review_cycle_id: result[:review_cycle_id],
        )

        Rails.logger.warn("Manager employee #{result[:reviewee_employee_id]} does not exist as reviewee in cycle #{result[:review_cycle_id]} - event published")
      end
    end
  end
end
