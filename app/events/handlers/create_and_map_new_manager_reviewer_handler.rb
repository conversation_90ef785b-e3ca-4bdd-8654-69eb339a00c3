# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
Dir[Rails.root.join('app/event_store/**/*.rb')]

module Events
  module Handlers
    class CreateAndMapNewManagerReviewerHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::CreateAndMapNewManagerReviewerDefinition
      end

      private

      def execute_handler_logic(event)
        remapping_service = handle_service_result(
          ManagerChanges::TriggerAutoRemapping.call(
            event.reviewee_id,
            event.review_cycle_id,
            event.previous_manager_id,
            event.new_manager_id,
          ),
          "Auto remapping failed for reviewee #{event.reviewee_id} in event id #{event.event_id}",
        )
        return unless remapping_service

        result = remapping_service.result

        # Publish snapshot event after new manager reviewer creation
        publish_snapshot_event(event, result) if result[:new_reviewer_created][:created]

        # Optionally publish follow-up events based on what happened
        publish_follow_up_events(event, result) if should_publish_follow_up_events?(result)

        result
      end

      def completion_message(event, _result)
        "CreateAndMapNewManagerReviewerHandler completed for reviewee #{event.reviewee_id} " \
          "in cycle #{event.review_cycle_id}"
      end

      def publish_snapshot_event(event, result)
        Rails.logger.info("Publishing CreateRevieweeSnapshot event for new manager creation - reviewee #{event.reviewee_id} in cycle #{event.review_cycle_id}")

        snapshot_event = Events::Definitions::CreateRevieweeSnapshotDefinition.new(
          data: {
            employee_id: event.employee_id,
            reviewee_id: event.reviewee_id,
            review_cycle_id: event.review_cycle_id,
            old_employee_id: event.new_manager_id,
            new_manager_id: event.new_manager_id,
            reason: 'manager_creation',
          }.with_indifferent_access,
        )

        unless snapshot_event.valid?
          Rails.logger.error("Invalid CreateRevieweeSnapshotDefinition event: #{snapshot_event.errors.full_messages}")
          return
        end

        publish_event(
          snapshot_event,
          EventStore::StreamNames.for_employee(event.employee_id),
          event,
          new_reviewer_id: result[:new_reviewer_created][:reviewer_id],
        )

        Rails.logger.info("CreateRevieweeSnapshot event published for new manager creation - employee #{event.new_manager_id}")
      end

      def should_publish_follow_up_events?(result)
        # Publish follow-up events if a new reviewer was created
        result[:new_reviewer_created][:created]
      end

      def publish_follow_up_events(event, result)
        stream = EventStore::StreamNames.for_employee(event.employee_id)

        # Publish move old manager to custom reviewer event if a reviewer was actually created
        if result[:new_reviewer_created][:created]
          move_old_manager_event = Events::Definitions::MoveOldManagerToCustomReviewerDefinition.new(
            data: {
              employee_id: event.employee_id,
              reviewee_id: event.reviewee_id,
              review_cycle_id: event.review_cycle_id,
              old_manager_id: event.previous_manager_id,
              new_manager_id: event.new_manager_id,
            }.with_indifferent_access,
          )

          publish_event(move_old_manager_event, stream, event)

          Rails.logger.info("Published MoveOldManagerToCustomReviewerDefinition for reviewee #{event.reviewee_id}")
        end
      end
    end
  end
end
