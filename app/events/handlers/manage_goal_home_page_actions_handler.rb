# frozen_string_literal: true

module Events
  module Handlers
    class ManageGoalHomePageActionsHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::ManageGoalHomePageActionsDefinition
      end

      private

      def execute_handler_logic(event)
        service_result = handle_service_result(
          ManagerChanges::ManageGoalHomePageActions.call(
            employee_id: event.employee_id,
            reviewee_id: event.reviewee_id,
            review_cycle_id: event.review_cycle_id,
            previous_manager_id: event.previous_manager_id,
            new_manager_id: event.new_manager_id,
          ),
          "ManageGoalHomePageActions service failed for reviewee #{event.reviewee_id} in event id #{event.event_id}",
        )
        return unless service_result

        service_result.result
      end

      def completion_message(event, result)
        "ManageGoalHomePageActionsHandler completed for reviewee #{event.reviewee_id}: " \
          "previous_manager_handled=#{result[:previous_manager_actions_handled]}, " \
          "new_manager_actions_created=#{result[:new_manager_actions_created]}"
      end
    end
  end
end
