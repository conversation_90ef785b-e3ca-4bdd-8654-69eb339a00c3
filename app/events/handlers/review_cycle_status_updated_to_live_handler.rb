# frozen_string_literal: true

module Events
  module Handlers
    class ReviewCycleStatusUpdatedToLiveHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::ReviewCycleStatusUpdatedToLiveDefinition
      end

      private

      def execute_handler_logic(event)
        service_result = handle_service_result(
          ManagerChanges::VerifyReviewCycleMapping.call(event.review_cycle_id),
          "VerifyReviewCycleMapping service failed for review cycle #{event.review_cycle_id} in event id #{event.event_id}",
        )
        return unless service_result

        Rails.logger.info(
          "VerifyReviewCycleMapping completed successfully for review cycle #{event.review_cycle_id}",
        )

        service_result.result
      end

      def completion_message(event, _result)
        "ReviewCycleStatusUpdatedToLiveHandler completed for review cycle #{event.review_cycle_id} " \
          "(#{event.previous_status} → #{event.new_status})"
      end
    end
  end
end
