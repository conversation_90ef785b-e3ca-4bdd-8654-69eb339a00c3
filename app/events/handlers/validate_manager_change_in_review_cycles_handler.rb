# frozen_string_literal: true

Dir[Rails.root.join('app/events/**/*.rb')]
Dir[Rails.root.join('app/event_store/**/*.rb')]
module Events
  module Handlers
    class ValidateManagerChangeInReviewCyclesHandler < Events::Handlers::BaseHandler
      def expected_event_type
        Events::Definitions::ValidateManagerChangeInReviewCyclesDefinition
      end

      private

      def execute_handler_logic(event)
        validation_service = handle_service_result(
          ManagerChanges::ValidateReviewCycleImpact.call(
            event.employee_id,
            event.previous_manager_id,
            event.new_manager_id,
          ),
          "Validation failed for employee #{event.employee_id} in event id #{event.event_id}",
        )
        return unless validation_service

        result = validation_service.result
        stream = EventStore::StreamNames.for_employee(event.employee_id)

        if result[:no_changes_needed]
          publish_no_changes_needed_event(event, result, stream)
        else
          publish_auto_remapping_events(event, result, stream)
        end

        result
      end

      def completion_message(event, result)
        count = result ? result[:reviewees_needing_update].count : 0
        "ValidateManagerChangeInReviewCyclesHandler completed for employee #{event.employee_id}. " \
          "Found #{count} reviewees needing update."
      end

      def publish_no_changes_needed_event(event, result, stream)
        reason = if result[:eligible_review_cycles_count].zero?
                   'no_eligible_review_cycles'
                 elsif result[:total_reviewees_found].zero?
                   'employee_not_in_review_cycles'
                 else
                   'all_reviewees_already_have_correct_manager'
                 end

        no_changes_event = Events::Definitions::NoManagerChangeNeededInReviewCyclesDefinition.new(
          data: {
            employee_id: event.employee_id,
            previous_manager_id: event.previous_manager_id,
            new_manager_id: event.new_manager_id,
            reason: reason,
          }.with_indifferent_access,
        )

        publish_event(no_changes_event, stream, event, async: true)
      end

      def publish_auto_remapping_events(event, result, stream)
        result[:reviewees_needing_update].each do |reviewee_data|
          update_reviewee_manager_event = Events::Definitions::UpdateRevieweeManagerDefinition.new(
            data: {
              employee_id: event.employee_id,
              reviewee_id: reviewee_data[:reviewee_id],
              review_cycle_id: reviewee_data[:review_cycle_id],
              previous_manager_id: event.previous_manager_id,
              new_manager_id: event.new_manager_id,
            }.with_indifferent_access,
          )

          publish_event(
            update_reviewee_manager_event,
            stream,
            event,
            async: true,
            reviewee_id: reviewee_data[:reviewee_id],
            review_cycle_id: reviewee_data[:review_cycle_id],
          )
        end
      end
    end
  end
end
