# frozen_string_literal: true

module Events
  module Handlers
    class BaseHandler
      # Used by <PERSON><PERSON><PERSON><PERSON><PERSON> to run via .delay
      def self.call_later(event_class_or_name, event_data)
        event_class = event_class_or_name.is_a?(String) ? event_class_or_name.constantize : event_class_or_name
        event = event_class.new(event_data)
        new.call(event)
      end

      def self.call_now(event_class_or_name, event_data)
        event_class = event_class_or_name.is_a?(String) ? event_class_or_name.constantize : event_class_or_name
        event = event_class.new(event_data)
        new.call(event)
      end

      # Required by RubyEventStore - Template Method Pattern
      def call(event)
        validate_event_type(event, expected_event_type)
        return if should_skip_processing?(event)

        result = execute_handler_logic(event)
        log_completion(event, result) if should_log_completion?
        result
      rescue StandardError => e
        handle_error(event, e)
        raise
      end

      private

      # Template methods - override in subclasses
      def expected_event_type
        raise NotImplementedError, 'Subclasses must implement #expected_event_type'
      end

      def execute_handler_logic(_event)
        raise NotImplementedError, 'Subclasses must implement #execute_handler_logic(event)'
      end

      # Optional overrides
      def should_skip_processing?(_event)
        false
      end

      def should_log_completion?
        true
      end

      def completion_message(event, _result = nil)
        "#{self.class.name.demodulize} completed for #{event_identifier(event)}"
      end

      # Common helper methods
      def validate_event_type(event, expected_type)
        return if event.is_a?(expected_type)

        raise ArgumentError, "Expected #{expected_type.name}"
      end

      def handle_error(event, error)
        identifier = event_identifier(event)
        message = "#{self.class.name.demodulize} failed for #{identifier}: #{error.message}"
        Rails.logger.error(message)
        Sentry.capture_exception(error) if defined?(Sentry)

        send_slack_notification(
          title: "Event Handler Error: #{self.class.name.demodulize}",
          color: 'danger',
          icon: ':fire_engine:',
          fields: [
            { title: 'Event', value: identifier, short: true },
            { title: 'Error', value: error.message, short: true },
          ],
        )
      end

      def build_metadata(event, **overrides)
        {
          triggered_by: self.class.name.demodulize.underscore,
          original_event_id: event.event_id,
        }.merge(overrides)
      end

      def handle_service_result(service_result, context_message)
        return service_result if service_result.success?

        Rails.logger.error("#{context_message}: #{service_result.errors.full_messages}")

        send_slack_notification(
          title: "Service Result Error: #{self.class.name.demodulize}",
          color: 'warning',
          icon: ':warning:',
          fields: [
            { title: 'Context', value: context_message, short: true },
            { title: 'Errors', value: service_result.errors.full_messages.join(', '), short: true },
          ],
        )
        nil
      end

      def event_identifier(event)
        if event.respond_to?(:employee_id)
          "employee #{event.employee_id}"
        elsif event.respond_to?(:reviewee_id)
          "reviewee #{event.reviewee_id}"
        elsif event.respond_to?(:review_cycle_id)
          "review cycle #{event.review_cycle_id}"
        else
          "event #{event.event_id}"
        end
      end

      def send_slack_notification(title:, color:, icon:, fields:)
        attachment = {
          color: color,
          title: title,
          fields: fields + [
            {
              title: 'Handler',
              value: self.class.name,
              short: true,
            },
            {
              title: 'Timestamp',
              value: Time.current.strftime('%Y-%m-%d %H:%M:%S UTC'),
              short: true,
            },
          ],
        }

        InternalSlackNotifier.call(attachment, icon, 'Peoplebot', '#genai-360-pdf-generation-error-tracker')
      end

      def log_completion(event, result = nil)
        message = completion_message(event, result)
        Rails.logger.info(message)
      end

      def publish_event(event_definition, stream_name, event, async: false, **metadata_overrides)
        Events::Publisher.publish(
          event: event_definition,
          stream_name: stream_name,
          metadata: build_metadata(event, **metadata_overrides),
          async: async,
        )
      end
    end
  end
end
