require 'redcarpet'
require 'redcarpet/render_strip'

class ReviewMailer < ApplicationMailer
  include ReviewMailerHelper

  default from: 'Peoplebox <<EMAIL>>',
          reply_to: 'Peoplebox <<EMAIL>>'

  def goal_selection_email(reviewee, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?
    # Create a ReviewCopyMessage entry with section as this function name to invoke custom subject & content

    @review_cycle_id = reviewee.review_cycle.id
    @reviewee_id = reviewee.id
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/define-goals/#{@reviewee_id}"

    @employee = reviewee.employee
    @cycle_title = reviewee.review_cycle.title

    @subject = custom_subject(reviewee.review_cycle, trackable,
                              reviewee) || 'Define your Goals for Review Cycle'
    @content = custom_content(reviewee.review_cycle, trackable, reviewee)
    @content = reviewee.resolve_question_for_reviewee(@content)

    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def goal_selection_email_reminder(reviewee, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?
    # Create a ReviewCopyMessage entry with section as this function name to invoke custom subject & content

    @review_cycle_id = reviewee.review_cycle.id
    @reviewee_id = reviewee.id
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/define-goals/#{@reviewee_id}"

    @employee = reviewee.employee
    @cycle_title = reviewee.review_cycle.title

    @subject = custom_subject(reviewee.review_cycle, trackable,
                              reviewee) || '[Reminder] Define your Goals for Review Cycle'
    @content = custom_content(reviewee.review_cycle, trackable, reviewee)
    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def peer_selection_email(reviewee, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?
    # Create a ReviewCopyMessage entry with section as this function name to invoke custom subject & content

    @user = reviewee.employee.user
    @review_cycle_id = reviewee.review_cycle.id
    @employee = reviewee.employee
    @cycle_title = reviewee.review_cycle.title
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/nominate/peers"

    @subject = custom_subject(reviewee.review_cycle, trackable,
                              reviewee) || 'Select your Peer Reviewers'
    @content = custom_content(reviewee.review_cycle, trackable, reviewee)

    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def peer_selection_email_reminder(reviewee, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @user = reviewee.employee.user
    @review_cycle_id = reviewee.review_cycle.id
    @employee = reviewee.employee
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/nominate/peers"

    @subject = custom_subject(reviewee.review_cycle, trackable,
                              reviewee) || '[Reminder] Select your Peer Reviewers'
    @content = custom_content(reviewee.review_cycle, trackable, reviewee)

    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }
    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def peer_selection_by_manager_email(reviewee_manager, review_cycle, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?
    # Create a ReviewCopyMessage entry with section as this function name to invoke custom subject & content

    @user = reviewee_manager.user
    @review_cycle_id = review_cycle.id
    @employee = reviewee_manager
    @cycle_title = review_cycle.title
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/choose-direct-report-peers"

    @subject = custom_subject(review_cycle, trackable, @employee) || 'Select Peers for your Team'
    @content = custom_content(review_cycle, trackable, @employee)

    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def peer_selection_by_manager_email_reminder(reviewee_manager, review_cycle, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?
    # Create a ReviewCopyMessage entry with section as this function name to invoke custom subject & content

    @user = reviewee_manager.user
    @review_cycle_id = review_cycle.id
    @employee = reviewee_manager
    @cycle_title = review_cycle.title
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/choose-direct-report-peers"

    @subject = custom_subject(review_cycle, trackable, @employee) || '[Reminder] Select Peers for your Team'
    @content = custom_content(review_cycle, trackable, @employee)

    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }
    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def peer_confirmation_email(reviewee, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @reviewee = reviewee
    @user = reviewee.employee.user
    @review_cycle_id = reviewee.review_cycle.id
    @employee = reviewee.employee
    @approved_peers = @reviewee.reviewers
      .joins(:reviewer_type)
      .where(reviewer_types: { reviewer_type: 'peer' })
      .where.not(approver_id: nil)
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/nominate/peers"

    @subject = custom_subject(reviewee.review_cycle, trackable,
                              reviewee) || 'Your Manager has approved your peers'
    @content = custom_content(reviewee.review_cycle, trackable, reviewee)
    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def peer_approval_email(manager, review_cycle, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?
    # Create a ReviewCopyMessage entry with section as this function name to invoke custom subject & content

    @user = manager.user
    @review_cycle_id = review_cycle.id
    @employee = manager
    @cycle_title = review_cycle.title
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/approve-peers"

    @subject = custom_subject(review_cycle, trackable, @employee, 'peer_approval') || 'Approve Peers for your Team'
    @content = custom_content(review_cycle, trackable, @employee, 'peer_approval')

    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def peer_approval_email_reminder(manager, review_cycle, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @user = manager.user
    @review_cycle_id = review_cycle.id
    @employee = manager
    @cycle_title = review_cycle.title
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/approve-peers"

    @subject = custom_subject(review_cycle, trackable, @employee,
                              'peer_approval') || '[Reminder] Approve Peers for your Team'
    @content = custom_content(review_cycle, trackable, @employee, 'peer_approval')
    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def goal_approval_email(manager, review_cycle, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @user = manager.user
    @review_cycle_id = review_cycle.id
    @employee = manager
    @cycle_title = review_cycle.title
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/approve-goals"

    # Create a ReviewCopyMessage entry with section as this function name to invoke custom subject & content
    @subject = custom_subject(review_cycle, trackable, @employee, 'goal_approval') || 'Approve Goals for your Team'
    @content = custom_content(review_cycle, trackable, @employee, 'goal_approval')
    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def goal_approval_email_reminder(manager, review_cycle, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @user = manager.user
    @review_cycle_id = review_cycle.id
    @employee = manager
    @cycle_title = review_cycle.title
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/approve-goals"

    # Create a ReviewCopyMessage entry with section as this function name to invoke custom subject & content
    @subject = custom_subject(review_cycle, trackable, @employee,
                              'goal_approval') || '[Reminder] Approve Goals for your Team'
    @content = custom_content(review_cycle, trackable, @employee, 'goal_approval')
    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def goal_confirmation_email(reviewee, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    review_cycle = reviewee.review_cycle
    review_cycle_id = review_cycle.id
    user = reviewee.employee.user
    @manager_name = reviewee.manager.display_name
    @employee = reviewee.employee
    @url =  "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{review_cycle_id}/define-goals/#{reviewee.id}"

    subject = custom_subject(review_cycle, trackable, reviewee) || "#{@manager_name} has approved your goals"
    @content = custom_content(review_cycle, trackable, reviewee)

    mail_params = {
      subject: subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def write_review_email(employee, employee_reviews, review_cycle, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @employee = employee
    @user = @employee.user
    @cycle_title = review_cycle.title
    @employee_review_types = (employee_reviews.keys - [:employee_id]).compact
    @employee_reviews = employee_reviews
    @review_cycle = review_cycle

    @subject = custom_subject(@review_cycle, trackable,
                              @employee, 'write_review') || 'Time to start writing reviews!'
    @content = custom_content(@review_cycle, trackable, @employee, 'write_review')
    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def write_review_email_reminder(employee, employee_reviews, review_cycle, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @employee = employee
    @user = @employee.user
    @cycle_title = review_cycle.title
    @employee_review_types = (employee_reviews.keys - [:employee_id]).compact
    @employee_reviews = employee_reviews
    @review_cycle = review_cycle

    @subject = custom_subject(@review_cycle, trackable,
                              @employee, 'write_review') || '[Reminder] Complete your Reviews'
    @content = custom_content(@review_cycle, trackable, @employee, 'write_review')
    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def write_review_manager_email(manager, reviewees, review_cycle, reviewer_type_id, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @manager = manager
    @user = @manager.user
    @cycle_title = review_cycle.title
    @reviewees = reviewees
    @review_cycle = review_cycle
    @reviewer_type_id = reviewer_type_id

    @subject = custom_subject(review_cycle, trackable, @manager, 'manager_summary') || 'Write Summaries for your Team'
    @content = custom_content(review_cycle, trackable, @manager, 'manager_summary')
    mail_params = {
      subject: @subject,
      to: @manager.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def write_review_manager_email_reminder(manager, reviewees, review_cycle, reviewer_type_id, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    @manager = manager
    @user = @manager.user
    @cycle_title = review_cycle.title
    @reviewees = reviewees
    @review_cycle = review_cycle
    @reviewer_type_id = reviewer_type_id

    @subject = custom_subject(review_cycle, trackable, @manager,
                              'manager_summary') || '[Reminder] Write Summaries for your Team'
    @content = custom_content(review_cycle, trackable, @manager, 'manager_summary')
    mail_params = {
      subject: @subject,
      to: @manager.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def release_review_email(reviewee, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?

    return if reviewee.blank?

    @reviewee           = reviewee
    @account            = @reviewee.employee.account
    @review_cycle       = @reviewee.review_cycle
    @reviewee_email     = @reviewee.employee.user.try(:email)
    @reviewee_name      = @reviewee.employee.try(:full_name)
    @review_cycle_title = @review_cycle.try(:title)
    @manager_name       = @reviewee.employee&.manager&.full_name.presence || 'Review Admin'

    # Avoid generating and sending PDF in the email.
    # This was done for security purposes and to overcome the scenarios where wrong PDF is sent.
    #
    # anonymize = @review_cycle.anonymize_reports?
    # pdf_file  = GenerateReleaseReviewPdf.call(@reviewee.try(:id), anonymize)
    # filename  = "#{@reviewee.employee.full_name.gsub(' ', '-')}-#{@reviewee.id}-#{@review_cycle.id}.pdf"
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle.try(:id)}/reviewees/#{@reviewee.try(:id)}/view_review?isNonAdmin=true&downloadPDF=false"

    @subject = custom_subject(@review_cycle, trackable, @reviewee) || "Your Review Summary for #{@review_cycle_title}"
    @content = custom_content(@review_cycle, trackable, @reviewee)
    mail_params = {
      from: get_from_address(@account),
      subject: @subject,
      to: @reviewee.employee.full_name_with_email,
    }

    # commented to prevent sending the pdf file it self
    # mail.attachments[filename.to_s] = File.read(pdf_file)

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def send_review_cycle_zip_to_admin_email(review_cycle, s3_object, employee, trackable = nil)
    track extra: { trackable_id: trackable&.id, trackable_type: trackable&.class.to_s } if trackable.present?
    @review_cycle = review_cycle
    @employee = employee
    @s3_object = s3_object
    @expiring_link = @s3_object.presigned_url(:get, expires_in: 1.day.to_i)

    mail_params = {
      subject: 'Your Download Request: Review Packet PDFs (Expires in 24 Hours)',
      to: employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def send_request_to_edit_goals_to_admin_email(review_cycle, reviewee, _trackable = nil)
    @employee = reviewee.employee
    @manager = reviewee.manager
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{review_cycle.id}/approve-goals?source=edit-approval-request&request_by=#{@employee.display_name}&reviewee_id=#{reviewee.id}"

    mail_params = {
      subject: "#{@employee.full_name} has requested to edit goals",
      to: "#{@manager.first_name.capitalize} <#{@manager.user.email}>",
    }

    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def send_approval_to_edit_goals_to_reviewee(review_cycle, reviewee, _trackable = nil)
    @review_cycle = review_cycle
    @reviewee = reviewee
    @employee = reviewee.employee
    @manager = reviewee.manager
    @url = if @review_cycle.manager_selects_and_approve_goals?
             "https://#{ENV['APP_DOMAIN']}/dashboard/goals/employees/#{@employee.id}"
           else
             "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{review_cycle.id}/define-goals/#{reviewee.id}"
           end

    mail_params = {
      subject: "Update: #{@manager.full_name} Enabled Goal Editing for #{review_cycle.title}",
      to: "#{@employee.first_name.capitalize} <#{@employee.user.email}>",
    }

    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def send_calibration_data(user_email, url)
    @employee = User.find_by(email: user_email).employee
    @url = url
    @subject = 'Your Calibration Data is Ready'

    mail_params = {
      subject: @subject,
      to: @employee.full_name_with_email,
    }

    connected_user_emails = connected_workspace_user_emails(user_email)
    mail_params[:cc] = connected_user_emails[:cc] if connected_user_emails[:cc].present?
    mail_params[:bcc] = connected_user_emails[:bcc] if connected_user_emails[:bcc].present?

    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def manager_change_summary_notification(review_cycle:, admin:, reviewee_count:, summary_data:)
    @review_cycle = review_cycle
    @admin = admin
    @reviewee_count = reviewee_count
    @summary_data = summary_data
    @account = review_cycle.account
    @company_name = @account.company_name
    @goal_required = @review_cycle.reviewer_types.exists?(define_goals: true)
    @peer_required = @review_cycle.reviewer_types.exists?(reviewer_type: 'peer')

    # Generate CSV attachment
    csv_data = generate_manager_changes_csv(summary_data)
    csv_filename = "manager_changes_#{@review_cycle.title.parameterize}_#{Date.current.strftime('%Y%m%d')}.csv"

    mail_params = {
      subject: "Mid-Cycle Manager Changes for #{@review_cycle.title}",
      to: @admin.full_name_with_email,
    }

    mail_params[:bcc] = '<EMAIL>' if Rails.env.production?

    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end.tap do |mail|
      mail.attachments[csv_filename] = {
        mime_type: 'text/csv',
        content: csv_data,
      }
    end
  end

  def new_manager_direct_reports_notification(review_cycle:, manager:, reviewees_data:)
    @review_cycle = review_cycle
    @manager = manager
    @reviewees_data = reviewees_data
    @account = review_cycle.account
    @company_name = @account.company_name
    @goal_required = @review_cycle.reviewer_types.exists?(define_goals: true)
    @peer_required = @review_cycle.reviewer_types.exists?(reviewer_type: 'peer')

    mail_params = {
      subject: "You've New Direct Reports in the #{@review_cycle.title}",
      to: @manager.full_name_with_email,
    }

    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def direct_report_manager_change_notification(review_cycle:, reviewee:, new_manager:, reviewee_data:)
    @review_cycle = review_cycle
    @reviewee = reviewee
    @new_manager = new_manager
    @reviewee_data = reviewee_data
    @account = review_cycle.account
    @company_name = @account.company_name
    @goal_required = @review_cycle.reviewer_types.exists?(define_goals: true)

    mail_params = {
      subject: "Your Manager Has Been Updated for #{@review_cycle.title}",
      to: @reviewee.full_name_with_email,
    }

    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def previous_manager_reassignment_notification(review_cycle:, previous_manager:, reviewees_data:)
    @review_cycle = review_cycle
    @previous_manager = previous_manager
    @reviewees_data = reviewees_data
    @account = review_cycle.account
    @company_name = @account.company_name
    @goal_required = @review_cycle.reviewer_types.exists?(define_goals: true)
    @peer_required = @review_cycle.reviewer_types.exists?(reviewer_type: 'peer')

    mail_params = {
      subject: "Manager Reassignments for your Direct Reports in #{@review_cycle.title}",
      to: @previous_manager.full_name_with_email,
    }

    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end

  def view_approved_goals(review_cycle_id, reviewee_id, comment)
    # Stopping firing emails temporarily
    return
    load_reviewee_data(review_cycle_id, reviewee_id)
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/define-goals/#{@reviewee.id}"
    @subject = "#{@manager_name} has approved your goals"
    @comment = comment
    send_mail(@subject, @reviewee.employee)
  end

  def requested_goal_changes(review_cycle_id, reviewee_id, comment)
    # Stopping firing emails temporarily
    return
    load_reviewee_data(review_cycle_id, reviewee_id)
    @comment = comment
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/define-goals/#{@reviewee.id}"
    @subject = "#{@manager_name} has requested changes for your submitted goals"
    send_mail(@subject, @reviewee.employee)
  end

  def re_review_goals(review_cycle_id, reviewee_id)
    # Stopping firing emails temporarily
    return
    load_reviewee_data(review_cycle_id, reviewee_id)
    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{@review_cycle_id}/approve-goals/#{@reviewee.id}"
    @subject = 'Goals have been revised and re-submitted based on your comments'
    send_mail(@subject, @reviewee.manager)
  end

  private

  def fetch_notification_details(review_cycle, trackable, participant, phase_type)
    if trackable.present? && trackable&.class.to_s == 'ReviewCycleNotification'
      review_cycle_phase = trackable.review_cycle_phase
      notification_type = trackable.task_type
    end
    return if review_cycle_phase.blank?
    return if notification_type.blank?

    custom_message = trackable&.review_copy_message || review_cycle_phase.review_copy_messages.where(
      notification_channel: 'email',
      notification_type: notification_type,
    ).order(:created_at).first
    return if custom_message.blank?

    reviewer_name = manager_name = reviewee_name = nil

    case participant&.class.to_s
    when 'Employee'
      case phase_type
      when 'write_review'
        reviewer_name = participant&.display_name
      when 'peer_approval', 'goal_approval', 'manager_summary'
        manager_name = participant&.display_name
      end
    when 'Reviewee'
      manager_name = participant&.manager&.display_name
      reviewee_name = participant&.employee&.display_name
    end

    [custom_message, review_cycle&.title, reviewer_name || ' ', manager_name || ' ', reviewee_name || ' ']
  end

  def custom_subject(review_cycle, trackable, participant = nil, phase_type = nil)
    custom_message, review_cycle_name, reviewer_name, manager_name, reviewee_name = fetch_notification_details(
review_cycle, trackable, participant, phase_type
)
    return if custom_message.blank?

    Liquid::Template.parse(custom_message.subject).render(
      'review_cycle_name' => review_cycle_name,
      'reviewee_name' => reviewee_name,
      'manager_name' => manager_name,
      'reviewer_name' => reviewer_name,
    )
  end

  def custom_content(review_cycle, trackable, participant = nil, phase_type = nil)
    custom_message, review_cycle_name, reviewer_name, manager_name, reviewee_name = fetch_notification_details(
review_cycle, trackable, participant, phase_type
)
    return if custom_message.blank?

    Liquid::Template.parse(custom_message.email_content).render(
      'review_cycle_name' => review_cycle_name,
      'reviewee_name' => reviewee_name,
      'manager_name' => manager_name,
      'reviewer_name' => reviewer_name,
    )
  end

  def load_reviewee_data(review_cycle_id, reviewee_id)
    @reviewee = Reviewee.find(reviewee_id)
    @reviewee_name = @reviewee.employee.display_name
    @review_cycle = ReviewCycle.find(review_cycle_id)
    @review_cycle_id = @review_cycle.id
    @manager = @reviewee&.manager
    @manager_name = @manager.display_name
    @cycle_title = @review_cycle.title
  end

  def send_mail(subject, employee)
    mail_params = {
      subject: subject,
      to: employee.full_name_with_email,
    }

    # send email notification
    mail(mail_params) do |format|
      format.html { render layout: 'mailer_v2' }
    end
  end
end
