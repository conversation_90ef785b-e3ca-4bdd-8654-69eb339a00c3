# frozen_string_literal: true

module AclRoles
  module Permissions
    class Targetables < ApplicationService
      prepend SimpleCommand

      attr_reader :acl_role, :product_action, :account

      def initialize(acl_role_id, product_action_id)
        @acl_role = AclRole.find_by(id: acl_role_id)
        @product_action = ProductAction.find_by(id: product_action_id)
        @account = @acl_role&.account
      end

      def call
        return errors.add(:base, 'AclRole not found!') if acl_role.blank?
        return errors.add(:base, 'ProductAction not found!') if product_action.blank?
        return errors.add(:base, 'Account not found!') if account.blank?

        targetables
      end

      private

      def targetables
        @_targetables ||= {
          employees: targetable_employees,
          employee_filters: targetable_employee_filters,
          goal_table_filters: targetable_goal_table_filters,
        }
      end

      def targetable_employees
        acl_criteria.joins(
          "INNER JOIN employees e ON acls.targetable_type = 'Employee' AND acls.targetable_id = e.id "\
          'INNER JOIN users u ON e.user_id = u.id '\
          'LEFT JOIN departments d ON e.department_id = d.id',
        ).select(
          'e.id, e.full_name, u.email, d.name AS department_name, e.profile_picture, acls.id AS acl_id',
        ).distinct('e.id').order('e.full_name').map do |record|
          {
            id: record.id,
            full_name: record.full_name,
            email: record.email,
            department_name: record.department_name,
            profile_picture: Employee.profile_picture_url(record.id, record.profile_picture),
            acl_id: record.acl_id,
          }
        end
      end

      def targetable_employee_filters
        acl_criteria.where(targetable_type: 'EmployeeFilter').map do |record|
          ef = record.targetable
          {
            id: ef.id,
            name: ef.name,
            acl_id: record.id,
            employee_filter_conditions: model_filter_conditions(ef),
          }
        end
      end

      def targetable_goal_table_filters
        acl_criteria.where(targetable_type: 'GoalTableFilter').map do |record|
          gtf = record.targetable
          {
            id: gtf.id,
            name: gtf.name,
            acl_id: record.id,
            goal_table_filter_conditions: model_filter_conditions(gtf),
          }
        end
      end

      def model_filter_conditions(model_filter)
        model_filter.model_filter_conditions.map do |mfc|
          {
            id: mfc.id,
            table_attribute: mfc.table_attribute,
            operator: mfc.operator,
            values: format_mfc_value(mfc),
          }
        end
      end

      def format_mfc_value(mfc)
        return mfc.value.to_s.gsub(/\(|\)|'/, '').split(',').map(&:strip).compact unless mfc.instance_of?(EmployeeFilterCondition)

        mfc.json_value
      end

      def acl_criteria
        @_acl_criteria ||= Acl.where(actorable: acl_role, resource: product_action)
      end
    end
  end
end
