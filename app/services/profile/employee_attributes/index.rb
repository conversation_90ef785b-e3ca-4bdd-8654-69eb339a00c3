module Profile
  module EmployeeAttributes
    class Index
      prepend SimpleCommand
      attr_reader :target_employee, :current_employee, :account

      def initialize(target_employee, current_employee, account)
        @target_employee = target_employee
        @current_employee = current_employee
        @account = account
      end

      def call
        find_specific_employee_attributes
      end

      private

      def find_specific_employee_attributes
        employee_relations = current_employee.all_relations_to(target_employee)
        employee_attributes = target_employee.employee_attributes.includes(:employee_attribute_values)

        # Using map for in-place modification.
        employee_attributes.map do |employee_attribute|
          if (employee_attribute.role_access_config['allowed_viewer_roles'] & employee_relations).any?
            attribute_with_value = process_attribute(employee_attribute)
            attribute_with_value.except(:role_access_config, :fixed_position) if attribute_with_value.present?
          end
        end.compact
      end

      def process_attribute(employee_attribute)
        ActiveModelSerializers::SerializableResource.new(employee_attribute, serializer: EmployeeAttributeSerializer,
                                                                             value: get_attribute_value(employee_attribute)).serializable_hash
      end

      def get_attribute_value(employee_attribute)
        if employee_attribute.system?
          get_system_attribute_value(employee_attribute)
        else
          employee_attribute.employee_attribute_values.find_by(employee_id: target_employee.id)&.value
        end
      end

      def get_system_attribute_value(employee_attribute)
        case employee_attribute.title
        when 'Email Address'
          target_employee.user.email
        when 'Phone Number'
          target_employee.phone_number
        when 'Profile Link'
          get_profile_link(employee_attribute)
        when 'Department'
          target_employee.department&.name
        when 'Joining Date'
          doj = target_employee.date_of_joining
          doj = doj.in_time_zone(account.timezone) if doj && account.timezone.present?
          doj&.strftime('%d %B %Y')
        when 'Location'
          target_employee.location
        when 'Division'
          target_employee.division
        when 'Business Unit'
          target_employee.business_unit
        when 'Level'
          target_employee.level
        else
          ''
        end
      end

      def get_profile_link(employee_attribute)
        stored_value = employee_attribute.employee_attribute_values.find_by(employee_id: target_employee.id)&.value
        messaging_client = account.messaging_client
        client_icon = find_client_icon(messaging_client)

        if stored_value.present?
          profile_link = stored_value
        else
          profile_link = find_profile_link(messaging_client)
          employee_attribute.employee_attribute_values.create(employee_id: target_employee.id, value: profile_link)
        end

        {
          profile_link: profile_link,
          client_icon: client_icon,
          label: target_employee.first_name.capitalize
        }
      end

      def find_client_icon(messaging_client)
        asset_yml = YAML.load_file(Rails.root.join('config/settings/asset_info.yml'))
        case messaging_client
        when 'slack'
          asset_yml['slack']['icon']
        when 'ms_teams'
          asset_yml['ms_teams']['icon']
        else
          ''
        end
      end

      def find_profile_link(client)
        case client
        when 'slack'
          find_slack_profile_link
        when 'ms_teams'
          "https://teams.microsoft.com/l/chat/0/0?users=#{target_employee.user.email}"
        else
          ''
        end
      end

      def find_slack_profile_link
        slack_team = account.slack_team
        slack_user_id = target_employee&.omnichat_user&.team_user_id || ''

        return if slack_team.blank? || slack_user_id.blank?

        client = set_client(slack_team)
        return unless client

        domain_name = client&.team_info&.team&.domain || ''

        "https://#{domain_name}.slack.com/team/#{slack_user_id}"
      end

      def set_client(slack_team)
        bot_access_token = slack_team.bot_access_token
        client = Slack::Web::Client.new(token: bot_access_token)
        begin
          client.auth_test
        rescue Slack::Web::Api::Errors::SlackError => e
          # capture all Slack errors
          client = nil
          if client.nil?
            slack_team.account.update(
              messaging_client: nil,
              run_coffee_connect: false,
            )
          end
        end

        client
      end
    end
  end
end
