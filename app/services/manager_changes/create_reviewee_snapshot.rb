# frozen_string_literal: true

module ManagerChanges
  class CreateRevieweeSnapshot < ApplicationService
    prepend SimpleCommand

    attr_reader :reviewee_id, :review_cycle_id, :old_employee_id, :reason

    def initialize(reviewee_id, review_cycle_id, old_employee_id, reason = 'manager_change')
      @reviewee_id = reviewee_id
      @review_cycle_id = review_cycle_id
      @old_employee_id = old_employee_id
      @reason = reason
    end

    def call
      return errors.add(:reviewee, 'Reviewee not found') unless reviewee.present?
      return errors.add(:review_cycle, 'Review cycle not found') unless review_cycle.present?
      return errors.add(:old_manager, 'Old manager not found') unless old_manager.present?

      ActiveRecord::Base.transaction do
        create_snapshot_result = create_reviewee_snapshot

        {
          snapshot_created: create_snapshot_result,
          reviewee_id: reviewee_id,
          review_cycle_id: review_cycle_id,
        }
      end
    end

    private

    def reviewee
      @reviewee ||= Reviewee.find_by(id: reviewee_id)
    end

    def review_cycle
      @review_cycle ||= ReviewCycle.find_by(id: review_cycle_id)
    end

    def old_manager
      @old_manager ||= Employee.find_by(id: old_employee_id)
    end

    def create_reviewee_snapshot
      # Determine reviewer type based on reason
      target_reviewer_type = determine_reviewer_type

      # Find the reviewer type
      reviewer_type_record = review_cycle.reviewer_types.find_by(reviewer_type: target_reviewer_type)
      unless reviewer_type_record.present?
        Rails.logger.info("No #{target_reviewer_type} reviewer type found for review cycle #{review_cycle_id}")
        return { created: false, reason: "no_#{target_reviewer_type}_reviewer_type" }
      end

      old_reviewer = Reviewer.find_by(
        review_cycle: review_cycle,
        employee_id: old_employee_id,
        reviewee: reviewee,
        reviewer_type: reviewer_type_record,
      )

      unless old_reviewer.present?
        Rails.logger.info("No #{target_reviewer_type} reviewer found for employee #{old_employee_id}, reviewee #{reviewee_id}")
        return { created: false, reason: 'old_reviewer_not_found' }
      end

      # Create reviewee snapshot with current state
      snapshot = RevieweeSnapshot.create!(
           reviewee: reviewee,
           employee_id: old_employee_id,
           reviewer: old_reviewer,
           custom_attribute: "#{reviewee_id}-#{old_reviewer.id}",
           reason: reason,
           peer_selection_done: reviewee.peer_selection_done || false,
           peer_approval_done: reviewee.peer_approval_done || false,
           goal_selection_done: reviewee.goal_selection_done || false,
           goal_approval_done: reviewee.goal_approval_done || false,
         )

      Rails.logger.info(
        "Created reviewee snapshot #{snapshot.id} for reviewee #{reviewee_id}, old employee #{old_employee_id}",
      )
      { created: true, snapshot_id: snapshot.id, reason: 'snapshot_created' }
    end

    def determine_reviewer_type
      case reason
      when 'direct_report_change', 'direct_report_creation'
        'direct_report'
      else
        'manager' # Default to manager for backward compatibility
      end
    end
  end
end
