# frozen_string_literal: true

module ManagerChanges
  class CreateNormalPerfReviewRct < ApplicationService
    prepend SimpleCommand
    include ReviewTemplateHelpers

    attr_reader :review_cycle, :old_reviewer_type_id, :new_reviewer_type, :old_reviewer

    def initialize(review_cycle, old_reviewer_type_id, new_reviewer_type, old_reviewer = nil)
      @review_cycle = review_cycle
      @old_reviewer_type_id = old_reviewer_type_id
      @new_reviewer_type = new_reviewer_type
      @old_reviewer = old_reviewer
    end

    def call
      return unless validate_common_inputs

      begin
        # Step 1: Find reviewee snapshot and extract custom_attribute (using shared helper)
        custom_attribute = extract_custom_attribute_from_snapshot

        # Step 2: Fetch the old review cycle template (using shared helper)
        old_review_cycle_template = fetch_old_review_cycle_template

        # Step 3: Create new review cycle template with custom_attribute (using shared helper)
        # For performance reviews, no template reference is needed (nil)
        new_review_cycle_template = create_review_cycle_template_with_custom_attribute(custom_attribute, nil)
        return unless new_review_cycle_template

        # Step 4: Get template blocks from the old review cycle template if available
        template_blocks = get_template_blocks(old_review_cycle_template)

        # Step 5: Process template blocks if any exist
        result_data = process_template_blocks(template_blocks, new_review_cycle_template, old_review_cycle_template)

        Rails.logger.info("Performance review template creation completed: #{result_data[:created_count]} blocks created, #{result_data[:updated_responses_count]} responses updated#{old_reviewer.present? ? " for old reviewer #{old_reviewer.id}" : ''}")

        result_data
      rescue StandardError => e
        error_msg = "Failed to create performance review forms: #{e.message}"
        Rails.logger.error(error_msg)
        errors.add(:base, error_msg)
        nil
      end
    end

    private

    def get_template_blocks(old_review_cycle_template)
      if old_review_cycle_template.present?
        # Optimize query with includes to avoid N+1
        template_blocks = old_review_cycle_template.review_cycle_template_questions
          .includes(question: :options)
        Rails.logger.info("Found #{template_blocks.count} template questions from old review cycle template #{old_review_cycle_template.id} for performance review migration#{old_reviewer.present? ? " (old reviewer: #{old_reviewer.id})" : ''}")
        template_blocks
      else
        Rails.logger.info("No old review cycle template found, no template blocks to process#{old_reviewer.present? ? " for old reviewer #{old_reviewer.id}" : ''}")
        []
      end
    end

    def process_template_blocks(template_blocks, new_review_cycle_template, old_review_cycle_template)
      created_count = 0
      updated_responses_count = 0

      template_blocks.each do |template_block|
        result = process_single_template_block(template_block, new_review_cycle_template)
        created_count += result[:created_count]
        updated_responses_count += result[:updated_responses_count]
      end

      # Use shared helper to build result data
      build_result_data(created_count, new_review_cycle_template, old_review_cycle_template,
                        updated_responses_count)
    end

    def process_single_template_block(template_block, new_review_cycle_template)
      # Prepare params for ReviewCycleTemplateQuestions::Create service
      rctq_params = prepare_review_cycle_template_question_params(template_block, new_review_cycle_template)

      # Use the ReviewCycleTemplateQuestions::Create service to properly create the question
      create_service = ReviewCycleTemplateQuestions::Create.call(rctq_params)

      if create_service.success?
        new_template_question_id = create_service.result.id

        # Update review responses for this template block
        updated_responses_count = update_review_responses_for_new_template_question(
          template_block,
          new_template_question_id,
        )

        Rails.logger.info("Created performance review template question #{new_template_question_id} for new reviewer type #{new_reviewer_type.id} and updated #{updated_responses_count} review responses#{old_reviewer.present? ? " (migrating from reviewer #{old_reviewer.id})" : ''}")

        { created_count: 1, updated_responses_count: updated_responses_count }
      else
        error_msg = "Failed to create template question: #{create_service.errors.full_messages.join(', ')}"
        Rails.logger.error(error_msg)
        errors.add(:template_question, error_msg)
        { created_count: 0, updated_responses_count: 0 }
      end
    end

    def prepare_review_cycle_template_question_params(template_block, new_review_cycle_template)
      # Extract attributes from the original template block
      block_attributes = template_block.attributes.except('id', 'created_at', 'updated_at', 'review_cycle_template_id')
      block_type = block_attributes['block_type']

      # Prepare the question attributes based on block_type
      question_attributes = prepare_question_attributes(template_block, block_type)

      # Prepare goal_fields if present (mainly for goals block type)
      goal_fields = block_attributes['goal_fields'] || {}

      # Build the params hash in the format expected by ReviewCycleTemplateQuestions::Create
      # The service expects direct attributes, not wrapped in a nested hash
      params_hash = {
        block_type: block_type,
        question_id: block_attributes['question_id'],
        content: block_attributes['content'],
        show_goal_progress: block_attributes['show_goal_progress'],
        question_access: block_attributes['question_access'],
        position: block_attributes['position'],
        formula: block_attributes['formula'],
        default_weights: block_attributes['default_weights'] || {},
        goal_fields: goal_fields,
        review_cycle_template_id: new_review_cycle_template.id,
      }

      # Add question_attributes only for question/competency block types
      if question_attributes.present?
        params_hash[:question_attributes] = question_attributes
      end

      Rails.logger.info("Prepared RCTQ params for template block #{template_block.id}: block_type=#{block_type}, question_id=#{block_attributes['question_id']}")

      params_hash
    end

    def prepare_question_attributes(template_block, block_type)
      return nil unless %w[question competency].include?(block_type) && template_block.question.present?

      question_attrs = template_block.question.attributes.except('id', 'created_at', 'updated_at', 'serviceable_id',
                                                                 'serviceable_type')

      # Add required serviceable and account_id for the new review cycle
      review_cycle = new_reviewer_type.review_cycle
      question_attrs[:serviceable] = review_cycle
      question_attrs[:account_id] = review_cycle.account_id

      # Include options if they exist for question/competency blocks (already loaded via includes)
      if template_block.question.options.any?
        options_attributes = template_block.question.options.map do |option|
          option.attributes.except('id', 'created_at', 'updated_at', 'question_id')
        end
        question_attrs[:options_attributes] = options_attributes
      end

      Rails.logger.info("Prepared question_attributes for #{block_type} block #{template_block.id} with #{template_block.question.options.count} options")
      question_attrs
    end

    def update_review_responses_for_new_template_question(old_template_block, new_template_question_id)
      # Step 1: Only process if block_type is question, competency, or goals
      return 0 unless %w[question competency goals calculated_question].include?(old_template_block.block_type)

      # Return early if no old_reviewer to migrate from
      return 0 unless old_reviewer.present?

      # Step 2: Find all review responses for the old reviewer and old template block
      old_review_responses = ReviewResponse.includes(:option)
        .where(
                                            reviewer: old_reviewer,
                                            review_cycle_template_question_id: old_template_block.id,
                                          )

      # Return early if no responses to update
      return 0 if old_review_responses.empty?

      # Step 3: Get the new template question and its associated question
      new_template_question = ReviewCycleTemplateQuestion.find(new_template_question_id)
      new_question = new_template_question.question

      updated_count = 0

      # Use transaction to ensure data consistency
      ActiveRecord::Base.transaction do
        old_review_responses.each do |response|
          update_single_review_response(response, old_template_block, new_template_question_id, new_question)
          updated_count += 1
        end
      end

      Rails.logger.info("Updated #{updated_count}/#{old_review_responses.count} review responses for #{old_template_block.block_type} block #{old_template_block.id}")
      updated_count
    rescue StandardError => e
      Rails.logger.error("Failed to update review responses for template block #{old_template_block.id}: #{e.message}")
      0
    end

    def update_single_review_response(response, old_template_block, new_template_question_id, new_question)
      # Step 1: Update review_cycle_template_question_id
      response.review_cycle_template_question_id = new_template_question_id

      # Step 2: Handle question_id and option_id based on block_type
      if %w[question competency
            calculated_question].include?(old_template_block.block_type) && new_question.present?
        # Update question_id for question/competency blocks
        response.question_id = new_question.id

        # Update option_id by matching option values
        update_response_option(response, new_question)
      end
      # For goals blocks, only review_cycle_template_question_id is updated (already done in Step 1)
      # No additional changes needed for question_id or option_id

      # Step 3: Save the updated response
      if response.save
        Rails.logger.info("Updated review response #{response.id} for new template question #{new_template_question_id}")
      else
        Rails.logger.error("Failed to update review response #{response.id}: #{response.errors.full_messages.join(', ')}")
        raise ActiveRecord::Rollback
      end
    end

    def update_response_option(response, new_question)
      return unless response.option.present? && new_question.options.any?

      old_option_value = response.option.value
      new_option = new_question.options.find_by(value: old_option_value)

      if new_option.present?
        response.option_id = new_option.id
        Rails.logger.info("Matched option '#{old_option_value}' to new option #{new_option.id} for response #{response.id}")
      else
        Rails.logger.warn("No matching option found for value '#{old_option_value}' in new question #{new_question.id} for response #{response.id}")
        response.option_id = nil
      end
    end
  end
end
