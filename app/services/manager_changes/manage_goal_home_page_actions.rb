# frozen_string_literal: true

# Business Logic:
# This service manages home page actions for goal selection/approval when manager changes occur.
#
# Key Rules:
# 1. For previous manager:
#    - Check if previous manager has other direct reports in the review cycle
#    - Remove home page actions for previous manager if they have no other direct reports
#
# 2. For new manager:
#    - Create home page actions if goal approval is required and reviewer type conditions are met:
#      a) Self goal approval: reviewer_types.exists?(goal_approval_required: true, reviewer_type: 'self')
#      b) Manager goal selection/approval: reviewer_types.exists?(goal_approval_required: true, reviewer_type: 'manager', define_goals: true)
#
# Parameters:
# - employee_id: The employee whose manager changed
# - reviewee_id: The reviewee record ID
# - review_cycle_id: The review cycle ID
# - previous_manager_id: The previous manager's employee ID (optional)
# - new_manager_id: The new manager's employee ID

module ManagerChanges
  class ManageGoalHomePageActions < ApplicationService
    prepend SimpleCommand

    attr_reader :employee_id, :reviewee_id, :review_cycle_id, :previous_manager_id, :new_manager_id

    def initialize(employee_id:, reviewee_id:, review_cycle_id:, previous_manager_id:, new_manager_id:)
      @employee_id = employee_id
      @reviewee_id = reviewee_id
      @review_cycle_id = review_cycle_id
      @previous_manager_id = previous_manager_id
      @new_manager_id = new_manager_id
    end

    def call
      return errors.add(:reviewee, 'Reviewee not found') if reviewee.blank?
      return errors.add(:review_cycle, 'Review cycle not found') if review_cycle.blank?
      return errors.add(:new_manager, 'New manager not found') if new_manager.blank?

      # Skip if review cycle doesn't have goal approval requirements
      return { skipped: true, reason: 'no_goal_approval_required' } unless has_goal_approval_requirements?

      ActiveRecord::Base.transaction do
        # Handle previous manager home page actions
        handle_previous_manager_actions if previous_manager_id.present?

        # Handle new manager home page actions
        handle_new_manager_actions

        {
          previous_manager_actions_handled: previous_manager_id.present?,
          new_manager_actions_created: true,
          employee_id: employee_id,
          reviewee_id: reviewee_id,
          review_cycle_id: review_cycle_id,
        }
      end
    end

    private

    def reviewee
      @_reviewee ||= Reviewee.find_by(id: reviewee_id)
    end

    def review_cycle
      @_review_cycle ||= ReviewCycle.find_by(id: review_cycle_id)
    end

    def previous_manager
      @_previous_manager ||= Employee.find_by(id: previous_manager_id) if previous_manager_id.present?
    end

    def new_manager
      @_new_manager ||= Employee.find_by(id: new_manager_id)
    end

    def handle_previous_manager_actions
      return if previous_manager.blank?

      # Check if previous manager has other direct reports in this review cycle
      other_direct_reports = review_cycle.reviewees.where(manager_id: previous_manager_id).where.not(id: reviewee_id)

      if other_direct_reports.empty?
        # Remove home page actions for previous manager
        remove_goal_home_page_actions(previous_manager_id)
        Rails.logger.info("Removed goal home page actions for previous manager #{previous_manager_id}")
      else
        Rails.logger.info("Previous manager #{previous_manager_id} has other direct reports, keeping home page actions")
      end
    end

    def handle_new_manager_actions
      phase = review_cycle.review_cycle_phases.find_by(phase_type: 'goal_approval')
      return if phase && phase.start_time.nil?

      return unless has_self_goal_approval? || has_manager_goal_selection_and_approval?

      create_goal_home_page_action(new_manager_id, 'approve_goals')
    end

    def remove_goal_home_page_actions(manager_id)
      # Find and remove home page actions for goal approval
      home_page_actions = review_cycle.home_page_actions.where(
        employee_id: manager_id,
        action_type: 'approve_goals',
      )
      home_page_actions.discard_all
      goal_approval_phase = review_cycle.review_cycle_phases.find_by(phase_type: 'goal_approval')
      existing_notifications = goal_approval_phase.review_cycle_notifications
        .joins(:review_cycle_notification_recipients)
        .where(review_cycle_notification_recipients: { employee_id: manager_id })
      if existing_notifications.present?
        existing_notifications.each do |existing_notification|
          existing_notification.review_cycle_notification_recipients.where(employee_id: manager_id).discard_all
        end
      end
    end

    def create_goal_home_page_action(manager_id, _action_type)
      # Find the goal approval phase
      phase = review_cycle.review_cycle_phases.find_by(phase_type: 'goal_approval')
      return if phase && phase.start_time.nil?

      # Use the proper service to create home page actions
      launch_data = {
        participants: [manager_id],
        channel: 'silent_launch',
        schedule: 'now',
        task_type: 'launch',
        participants_type: nil,
        current_user: nil, # System-generated notification
      }

      PerformanceReview::CreateReviewCyclePhaseNotifications.call(
        review_cycle.id,
        phase.id,
        launch_data,
      )
      Rails.logger.info("Created goal home page action for manager #{manager_id}, action_type approve_goals")
    end

    def has_goal_approval_requirements?
      has_self_goal_approval? || has_manager_goal_selection_and_approval?
    end

    def has_self_goal_approval?
      # Goal approval is being done by manager for self reviewer type
      review_cycle.reviewer_types.exists?(
        goal_approval_required: true,
        reviewer_type: 'self',
      )
    end

    def has_manager_goal_selection_and_approval?
      # Goal selection and approval is being done by manager
      review_cycle.reviewer_types.exists?(
        goal_approval_required: true,
        reviewer_type: 'manager',
        define_goals: true,
      )
    end
  end
end
