# frozen_string_literal: true

# Business Logic:
# This service handles mapping a new manager's direct report reviewer relationship.
#
# Key Rules:
# 1. If the new manager does NOT already exist as a reviewee in the review cycle,
#    we will NOT add or create them as a reviewee. The service will exit early
#    and publish a "ManagerDoesNotExistAsReviewee" event for auditing.
#
# 2. If the new manager <PERSON><PERSON><PERSON> exist as a reviewee in the review cycle,
#    we will add their new direct report as a reviewer with reviewer_type 'direct_report'.
#
# 3. If the review cycle does not have a 'direct_report' reviewer_type configured,
#    the service will exit early and publish a "DirectReportReviewerTypeDoesNotExist" event.
#
# 4. When successfully adding a direct report reviewer, we create a reviewee_snapshot
#    entry for audit trail with reason 'direct_report'.
#
# Parameters:
# - reviewer_employee_id: The employee who will be the reviewer (the direct report)
# - reviewee_employee_id: The employee who will be reviewed (the new manager)
# - review_cycle_id: The review cycle where this relationship should be established

module ManagerChanges
  class MapNewManagerDirectReport < ApplicationService
    prepend SimpleCommand

    attr_reader :reviewer_employee_id, :review_cycle_id, :reviewee_employee_id

    def initialize(reviewer_employee_id:, review_cycle_id:, reviewee_employee_id:)
      @reviewer_employee_id = reviewer_employee_id
      @review_cycle_id = review_cycle_id
      @reviewee_employee_id = reviewee_employee_id
    end

    def call
      return errors.add(:reviewer_employee, 'Reviewer employee not found') if reviewer_employee.blank?
      return errors.add(:reviewee_employee, 'Reviewee employee not found') if reviewee_employee.blank?
      return errors.add(:review_cycle, 'Review cycle not found') if review_cycle.blank?

      ActiveRecord::Base.transaction do
        # Check if review cycle has direct_report reviewer type
        unless direct_report_reviewer_type.present?
          return {
            direct_report_mapped: false,
            reason: 'direct_report_reviewer_type_not_exists',
            reviewer_employee_id: reviewer_employee_id,
            reviewee_employee_id: reviewee_employee_id,
            review_cycle_id: review_cycle_id,
          }
        end

        # Check if manager exists as reviewee
        existing_reviewee = find_existing_reviewee
        unless existing_reviewee.present?
          return {
            direct_report_mapped: false,
            reason: 'manager_does_not_exist_as_reviewee',
            reviewer_employee_id: reviewer_employee_id,
            reviewee_employee_id: reviewee_employee_id,
            review_cycle_id: review_cycle_id,
          }
        end

        # Add reviewer_employee_id as direct_report reviewer
        reviewer_result = add_direct_report_reviewer(existing_reviewee)
        send_silent_notification(reviewer_result)

        {
          direct_report_mapped: true,
          reviewer_created: reviewer_result[:created],
          reviewer_employee_id: reviewer_employee_id,
          reviewee_employee_id: reviewee_employee_id,
          review_cycle_id: review_cycle_id,
          target_reviewee_id: existing_reviewee.id,
        }
      end
    end

    private

    def send_silent_notification(reviewer_result)
      # Call the silent notification service
      notification_result = ManagerChanges::SendReviewerPhaseSilentNotification.call(
        reviewer_type_id: direct_report_reviewer_type.id,
        reviewer_id: reviewer_result[:reviewer_id],
      )

      if notification_result
        Rails.logger.info('Silent notification sent successfully for reviewee ')
      else
        Rails.logger.warn('Silent notification failed for reviewee ')
      end
    end

    def reviewer_employee
      @_reviewer_employee ||= Employee.find_by(id: reviewer_employee_id)
    end

    def reviewee_employee
      @_reviewee_employee ||= Employee.find_by(id: reviewee_employee_id)
    end

    def review_cycle
      @_review_cycle ||= ReviewCycle.find_by(id: review_cycle_id)
    end

    def direct_report_reviewer_type
      @_direct_report_reviewer_type ||= review_cycle.reviewer_types.find_by(reviewer_type: 'direct_report')
    end

    def find_existing_reviewee
      existing_reviewee = review_cycle.reviewees.find_by(employee_id: reviewee_employee_id)

      if existing_reviewee.present?
        Rails.logger.info("Found existing reviewee #{existing_reviewee.id} for employee #{reviewee_employee_id}")
        existing_reviewee
      else
        Rails.logger.info("Manager employee #{reviewee_employee_id} does not exist as reviewee in cycle #{review_cycle_id}")
        nil
      end
    end

    def add_direct_report_reviewer(target_reviewee)
      reviewer = review_cycle.reviewers.find_or_create_by!(
        employee: reviewer_employee,
        reviewer_type: direct_report_reviewer_type,
        review_cycle: review_cycle,
        reviewee: target_reviewee,
      )

      if reviewer.persisted? && reviewer.previous_changes.blank?
        Rails.logger.info("Direct report reviewer already exists: #{reviewer.id}")
        { created: false, reviewer_id: reviewer.id, reason: 'already_exists' }
      else
        Rails.logger.info("Created new direct report reviewer: #{reviewer.id}")
        { created: true, reviewer_id: reviewer.id, reason: 'reviewer_created' }
      end
    end

    def trigger_create_reviewee_snapshot_event(target_reviewee)
      event = Events::Definitions::CreateRevieweeSnapshotDefinition.new(
        data: {
          employee_id: reviewer_employee_id, # The direct report employee
          reviewee_id: target_reviewee.id, # The manager (reviewee)
          review_cycle_id: review_cycle_id,
          old_employee_id: reviewer_employee_id, # The direct report employee (for lookup)
          new_manager_id: reviewee_employee_id, # The new manager
          reason: 'direct_report_creation',
        }.with_indifferent_access,
      )

      Events::Publisher.publish(
        event: event,
        stream_name: EventStore::StreamNames.for_employee(reviewer_employee_id),
        metadata: {
          triggered_by: self.class.name.demodulize.underscore,
          reviewer_employee_id: reviewer_employee_id,
          reviewee_employee_id: reviewee_employee_id,
          review_cycle_id: review_cycle_id,
        },
        async: false, # Synchronous execution
      )

      Rails.logger.info(
        'CreateRevieweeSnapshot event triggered for direct report addition: ' \
        "reviewer #{reviewer_employee_id}, reviewee #{target_reviewee.id}",
      )
    end
  end
end
