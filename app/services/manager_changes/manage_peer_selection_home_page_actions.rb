# frozen_string_literal: true

# Business Logic:
# This service manages home page actions for peer selection/approval when manager changes occur.
#
# Key Rules:
# 1. For previous manager:
#    - Check if reviewee has completed peer selection/approval
#    - Check if previous manager has other direct reports in the review cycle
#    - Remove home page actions for previous manager if they have no other direct reports
#
# 2. For new manager:
#    - Create home page actions if the associated phase has been launched
#
# Parameters:
# - employee_id: The employee whose manager changed
# - reviewee_id: The reviewee record ID
# - review_cycle_id: The review cycle ID
# - previous_manager_id: The previous manager's employee ID (optional)
# - new_manager_id: The new manager's employee ID

module ManagerChanges
  class ManagePeerSelectionHomePageActions < ApplicationService
    prepend SimpleCommand

    attr_reader :employee_id, :reviewee_id, :review_cycle_id, :previous_manager_id, :new_manager_id

    def initialize(employee_id:, reviewee_id:, review_cycle_id:, previous_manager_id:, new_manager_id:)
      @employee_id = employee_id
      @reviewee_id = reviewee_id
      @review_cycle_id = review_cycle_id
      @previous_manager_id = previous_manager_id
      @new_manager_id = new_manager_id
    end

    def call
      return errors.add(:reviewee, 'Reviewee not found') unless reviewee.present?
      return errors.add(:review_cycle, 'Review cycle not found') unless review_cycle.present?
      return errors.add(:new_manager, 'New manager not found') unless new_manager.present?

      # Skip if review cycle doesn't have peer selection or approval phases
      return { skipped: true, reason: 'no_peer_phases' } unless has_peer_selection_or_approval?

      ActiveRecord::Base.transaction do
        # Handle previous manager home page actions
        handle_previous_manager_actions if previous_manager_id.present?

        # Handle new manager home page actions
        handle_new_manager_actions

        {
          previous_manager_actions_handled: previous_manager_id.present?,
          new_manager_actions_created: true,
          employee_id: employee_id,
          reviewee_id: reviewee_id,
          review_cycle_id: review_cycle_id,
        }
      end
    end

    private

    def reviewee
      @_reviewee ||= Reviewee.find_by(id: reviewee_id)
    end

    def review_cycle
      @_review_cycle ||= ReviewCycle.find_by(id: review_cycle_id)
    end

    def previous_manager
      @_previous_manager ||= Employee.find_by(id: previous_manager_id) if previous_manager_id.present?
    end

    def new_manager
      @_new_manager ||= Employee.find_by(id: new_manager_id)
    end

    def handle_previous_manager_actions
      return unless previous_manager.present?

      # Check if previous manager has other direct reports in this review cycle
      other_direct_reports = review_cycle.reviewees
        .where(manager_id: previous_manager_id)
        .where.not(id: reviewee_id)

      if other_direct_reports.empty?
        # Remove home page actions for previous manager
        remove_peer_selection_home_page_actions(previous_manager_id)
        Rails.logger.info("Removed peer selection home page actions for previous manager #{previous_manager_id}")
      else
        Rails.logger.info("Previous manager #{previous_manager_id} has other direct reports, keeping home page actions")
      end
    end

    def handle_new_manager_actions
      # Check peer_configs with review_cycle_user_roles for manager-based peer selection or approval
      # Create actions based on the peer config requirements
      if review_cycle.peer_selection_required && ['manager'].include?(review_cycle.peer_selection_role)
        create_peer_selection_home_page_action(new_manager_id, 'peer_selection')
      end

      if review_cycle.peer_approval_required && ['manager'].include?(review_cycle.peer_approval_role)
        create_peer_selection_home_page_action(new_manager_id, 'peer_approval')
      end
    end

    def remove_peer_selection_home_page_actions(manager_id)
      return unless review_cycle

      # Process peer approval actions if required
      process_peer_actions(
        manager_id,
        required: review_cycle.peer_approval_required,
        role: review_cycle.peer_approval_role,
        action_types: %w[approve_peers],
        phase_type: 'peer_approval',
      )

      # Process peer selection actions if required
      process_peer_actions(
        manager_id,
        required: review_cycle.peer_selection_required,
        role: review_cycle.peer_selection_role,
        action_types: %w[choose_direct_report_peers],
        phase_type: 'peer_selection',
      )
    end

    def process_peer_actions(manager_id, required:, role:, action_types:, phase_type:)
      return unless required && ['manager'].include?(role)

      # Remove home page actions
      review_cycle.home_page_actions.where(
        employee_id: manager_id,
        action_type: action_types,
      ).discard_all

      # Remove notifications for the phase
      phase = review_cycle.review_cycle_phases.find_by(phase_type: phase_type)
      return unless phase

      notifications = phase.review_cycle_notifications
        .joins(:review_cycle_notification_recipients)
        .where(review_cycle_notification_recipients: { employee_id: manager_id })

      notifications.each do |notification|
        notification.review_cycle_notification_recipients.where(employee_id: manager_id).discard_all
      end
    end

    def create_peer_selection_home_page_action(manager_id, action_type)
      # Find the corresponding phase for date information
      phase = review_cycle.review_cycle_phases.find_by(phase_type: action_type)
      return if phase&.start_time.nil?

      launch_data = {
        participants: [manager_id],
        channel: 'silent_launch',
        schedule: 'now',
        task_type: 'launch',
        participants_type: nil,
        current_user: nil, # System-generated notification
      }

      PerformanceReview::CreateReviewCyclePhaseNotifications.call(
        review_cycle.id,
        phase.id,
        launch_data,
      )

      Rails.logger.info("Created peer selection home page action for manager #{manager_id}, action_type #{action_type}")
    end

    def has_peer_selection_or_approval?
      # Check peer_configs with review_cycle_user_roles for manager-based peer selection or approval
      if review_cycle.peer_selection_required && ['manager'].include?(review_cycle.peer_selection_role)
        return true
      end

      if review_cycle.peer_approval_required && ['manager'].include?(review_cycle.peer_approval_role)
        return true
      end

      false
    end
  end
end
