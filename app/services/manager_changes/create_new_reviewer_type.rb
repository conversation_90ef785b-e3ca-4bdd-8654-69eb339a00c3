# frozen_string_literal: true

module ManagerChanges
  class CreateNewReviewerType < ApplicationService
    prepend SimpleCommand

    attr_reader :review_cycle_id, :reviewer_type, :old_reviewer_employee_id, :reviewee_id

    def initialize(review_cycle_id, reviewer_type = 'manager', old_reviewer_employee_id = nil, reviewee_id = nil)
      @review_cycle_id = review_cycle_id
      @reviewer_type = reviewer_type
      @old_reviewer_employee_id = old_reviewer_employee_id
      @reviewee_id = reviewee_id
    end

    def call
      return errors.add(:review_cycle, 'Review cycle not found') unless review_cycle.present?

      ActiveRecord::Base.transaction do
        # Find the old reviewer type once and reuse it
        return errors.add(:reviewer_type, "#{reviewer_type} reviewer type not found") unless old_reviewer_type.present?

        create_reviewer_type_result = create_new_reviewer_type(old_reviewer_type)
        update_old_reviewer_result = update_old_reviewer_type(create_reviewer_type_result, old_reviewer_type,
                                                              create_reviewer_type_result[:former_reviewer_type])
        PerformanceReview::CreateReviewCyclePhases.call(review_cycle&.id)

        {
          reviewer_type_created: create_reviewer_type_result,
          old_reviewer_updated: update_old_reviewer_result,
          review_cycle_id: review_cycle_id,
          new_reviewer_type_id: create_reviewer_type_result[:reviewer_type_id],
          old_reviewer_type_id: old_reviewer_type.id,
        }
      end
    end

    private

    def review_cycle
      @_review_cycle ||= ReviewCycle.find_by(id: review_cycle_id)
    end

    def old_reviewer_type
      @_old_reviewer_type ||= review_cycle.reviewer_types.find_by(reviewer_type: reviewer_type)
    end

    def create_new_reviewer_type(old_reviewer_type)
      former_reviewer_type_name = "previous_#{old_reviewer_type.reviewer_type}"

      # Try to find the existing former reviewer type first
      former_reviewer_type = find_existing_former_reviewer_type(former_reviewer_type_name)

      unless former_reviewer_type
        former_reviewer_type = build_former_reviewer_type(old_reviewer_type, former_reviewer_type_name)
        former_reviewer_type.save!
      end

      log_former_reviewer_type_creation(former_reviewer_type, former_reviewer_type_name)

      {
        reviewer_type_id: former_reviewer_type.id,
        reviewer_type_name: former_reviewer_type_name,
        former_reviewer_type: former_reviewer_type,
      }
    end

    def find_existing_former_reviewer_type(name)
      review_cycle.reviewer_types.system.find_by(reviewer_type: name)
    end

    def build_former_reviewer_type(old_reviewer_type, new_name)
      duplicated_attributes = old_reviewer_type
        .dup
        .attributes
        .except('id', 'created_at', 'updated_at', 'reviewer_type', 'review_cycle_id')

      review_cycle.reviewer_types.system.build(
        { reviewer_type: new_name }.merge(duplicated_attributes),
      ).tap do |new_type|
        new_type.first_person     = "Previous #{old_reviewer_type.first_person}"
        new_type.second_person    = "Previous #{old_reviewer_type.second_person}"
        new_type.creation_source  = 'system'
        new_type.standalone_launch = true
      end
    end

    def log_former_reviewer_type_creation(reviewer_type, name)
      Rails.logger.info(
        "Previous reviewer type #{reviewer_type.id} (#{name}) processed for review cycle #{review_cycle_id}",
      )
    end

    def update_old_reviewer_type(reviewer_type_result, old_reviewer_type, former_reviewer_type)
      # Skip if no old manager or reviewee specified
      unless old_reviewer_employee_id.present? && reviewee_id.present?
        return { updated: false,
                 reason: 'no_old_manager_or_reviewee' }
      end

      # Skip if reviewer type processing failed
      unless reviewer_type_result[:former_reviewer_type].present?
        return { updated: false,
                 reason: 'reviewer_type_processing_failed' }
      end

      # Find the old reviewer record
      old_reviewer = review_cycle.reviewers.find_by(
        employee_id: old_reviewer_employee_id,
        reviewee_id: reviewee_id,
        reviewer_type: old_reviewer_type,
      )
      if old_reviewer.blank?
        Rails.logger.info("No old reviewer found for old manager #{old_reviewer_employee_id}, reviewee #{reviewee_id}")
        return { updated: false, reason: 'old_reviewer_not_found' }
      end

      # Update the old reviewer's reviewer_type using the passed former_reviewer_type
      if old_reviewer.update(reviewer_type: former_reviewer_type)
        remove_notifications(old_reviewer_type)
        destroy_old_home_page_action(old_reviewer_type, old_reviewer)
      end

      Rails.logger.info(
        "Updated reviewer #{old_reviewer.id} from #{reviewer_type} to #{former_reviewer_type.reviewer_type}",
      )
      { updated: true, reviewer_id: old_reviewer.id, old_reviewer_type: reviewer_type,
        new_reviewer_type: former_reviewer_type.reviewer_type }
    end

    def remove_notifications(old_reviewer_type)
      # remove review_cycle_notification_recipients from outgoing phase
      # only consider manager summary - moving to former manager
      # where direct report is a standalone phase - moving to former direct report
      # if direct report is part of the normal write review,do nothing as it a combination of other reviews

      if old_reviewer_type.reviewer_type == 'manager'
        phase = review_cycle.review_cycle_phases.find_by(phase_type: 'manager_summary')
        other_direct_reports = review_cycle.reviewees.where(manager_id: old_reviewer_employee_id).present?
      else
        phase = old_reviewer_type.review_cycle_phase
      end
      return unless phase
      return if old_reviewer_employee_id.blank?
      return if other_direct_reports.present?

      recipient_scope = {
        employee_id: old_reviewer_employee_id,
      }
      recipient_scope[:reviewer_type_id] = old_reviewer_type.id if other_reviewers_in_the_phase(phase)

      notifications = phase.review_cycle_notifications
        .joins(:review_cycle_notification_recipients)
        .where(review_cycle_notification_recipients: recipient_scope)

      notifications.each do |notification|
        notification.review_cycle_notification_recipients
          .where(recipient_scope)
          .discard_all
      end
    end

    def other_reviewers_in_the_phase(phase)
      return false if phase.reviewer_type_id.present?
      return false if phase.phase_type == 'manager_summary'

      reviewer_types = review_cycle.reviewer_types.normal.where.not(reviewer_type: 'manager')
      standalone_write_review_phases = review_cycle.review_cycle_phases.where.not(reviewer_type_id: nil)
      if standalone_write_review_phases.present?
        reviewer_types = reviewer_types.where.not(id: standalone_write_review_phases.pluck(:reviewer_type_id).compact.uniq)
      end
      reviewers = review_cycle.reviewers.where(employee_id: old_reviewer_employee_id,
                                               reviewer_type_id: reviewer_types.pluck(:id))
      reviewers.present?
    end

    def destroy_old_home_page_action(old_reviewer_type, old_reviewer)
      # why destroy, home page actions have an assocination with reviewers though pagable
      # we are using the reviewer to a new reviewer type
      # the content and link for that old home page now dont a line with the new updated reviewer type
      # let not take the risk of the old content conflicting with new system reviewer type, eg the link in the content_json
      # home page actions have less impact when destroyed, they are just links
      case old_reviewer_type.reviewer_type
      when 'manager'
        if review_cycle.reviewees.where(manager: old_reviewer_employee_id).blank?
          review_cycle.home_page_actions.where(action_type: 'downward_review',
                                               employee_id: old_reviewer_employee_id).destroy_all
        end
      else
        if review_cycle.reviewers.where(reviewer_type: old_reviewer_type, employee: old_reviewer.employee).blank?
          old_reviewer.home_page_actions.destroy_all
        end
      end
    end
  end
end
