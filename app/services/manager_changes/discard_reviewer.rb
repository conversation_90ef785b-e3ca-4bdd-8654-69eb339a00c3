module ManagerChanges
  class DiscardReviewer < ApplicationService
    prepend SimpleCommand

    attr_reader :review_cycle_id, :reviewer_type, :old_reviewer_employee_id, :reviewee_id

    def initialize(review_cycle_id, reviewer_type = 'manager', old_reviewer_employee_id = nil, reviewee_id = nil)
      @review_cycle_id = review_cycle_id
      @reviewer_type = reviewer_type
      @old_reviewer_employee_id = old_reviewer_employee_id
      @reviewee_id = reviewee_id
    end

    def call
      return errors.add(:review_cycle, 'Review cycle not found') if review_cycle.blank?
      return errors.add(:reviewee, 'Reviewee not found') if reviewee.blank?
      return errors.add(:employee, 'Reviewer Employee not found') if reviewer_employee.blank?
      return errors.add(:reviewer_type, "#{reviewer_type} reviewer type not found") if old_reviewer_type.blank?
      return errors.add(:reviewers, 'Reviewers not found') if reviewers.blank?

      ActiveRecord::Base.transaction do
        reviewers.discard_all
        destroy_old_home_page_action
      end
    end

    private

    def destroy_old_home_page_action
      case old_reviewer_type.reviewer_type
      when 'manager'
        if review_cycle.reviewees.where(manager: old_reviewer_employee_id).blank?
          review_cycle.home_page_actions.where(action_type: 'downward_review',
                                               employee_id: old_reviewer_employee_id).discard_all
        end
      else
        reviewers.each do |reviewer|
          next if reviewer.nil?
          next if reviewer.home_page_actions.blank?
          next if review_cycle.reviewers.where(reviewer_type: old_reviewer_type, employee: reviewer.employee).present?

          reviewer.home_page_actions.destroy_all
        end

      end
    end

    def review_cycle
      @_review_cycle ||= ReviewCycle.find_by(id: review_cycle_id)
    end

    def old_reviewer_type
      @_old_reviewer_type ||= review_cycle.reviewer_types.find_by(reviewer_type: reviewer_type)
    end

    def reviewee
      @_reviewee ||= review_cycle.reviewees.find_by(id: reviewee_id)
    end

    def reviewer_employee
      @_reviewer_employee ||= review_cycle.account.employees.find_by(id: old_reviewer_employee_id)
    end

    def reviewers
      @_reviewers ||= review_cycle.reviewers.where(reviewee: reviewee, employee: reviewer_employee,
                                                   reviewer_type: old_reviewer_type)
    end
  end
end
