module ManagerChanges
  class UpdateRevieweeManager < ApplicationService
    prepend SimpleCommand

    attr_reader :reviewee, :review_cycle, :new_manager

    def initialize(reviewee_id, review_cycle_id, new_manager_id)
      @reviewee = Reviewee.find_by(id: reviewee_id)
      @review_cycle = ReviewCycle.find_by(id: review_cycle_id)
      @new_manager = Employee.find_by(id: new_manager_id)
      validate_parameters
    end

    def call
      return if errors.any? || manager_already_correct?

      ActiveRecord::Base.transaction do
        old_manager_id = reviewee.manager_id
        reviewee.update!(manager_id: new_manager.id)
        {
          old_manager_id: old_manager_id,
          new_manager_id: new_manager.id,
        }
      end
    end

    private

    def validate_parameters
      errors.add(:reviewee, 'Reviewee not found') if reviewee.blank?
      errors.add(:review_cycle, 'ReviewCycle not found') if review_cycle.blank?
      errors.add(:employee, 'New manager not found') if new_manager.blank?

      if reviewee.present? && review_cycle.present? && !(reviewee.review_cycle_id == review_cycle.id)
        errors.add(:base, 'Reviewee not in specified review cycle')
      end
    end

    def manager_already_correct?
      reviewee.manager_id == new_manager.id
    end
  end
end
