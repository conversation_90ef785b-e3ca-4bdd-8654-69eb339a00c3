# frozen_string_literal: true

module ManagerChanges
  class CreateThreeSixtyPerfReviewTemplate < ApplicationService
    prepend SimpleCommand
    include ReviewTemplateHelpers

    attr_reader :review_cycle, :old_reviewer_type_id, :new_reviewer_type, :old_reviewer

    def initialize(review_cycle, old_reviewer_type_id, new_reviewer_type, old_reviewer = nil)
      @review_cycle = review_cycle
      @old_reviewer_type_id = old_reviewer_type_id
      @new_reviewer_type = new_reviewer_type
      @old_reviewer = old_reviewer
    end

    def call
      return unless validate_common_inputs

      begin
        # Step 1: Extract custom attribute from snapshot (using shared helper)
        custom_attribute = extract_custom_attribute_from_snapshot

        # Step 2: Fetch old review cycle template (using shared helper)
        old_review_cycle_template = fetch_old_review_cycle_template

        # Step 3: Find the 360 template (using shared helper)
        template_reference = find_or_create_360_template(old_review_cycle_template)

        if template_reference.present?
          # Step 4: Create new review cycle template with custom attribute and template association
          new_review_cycle_template = create_review_cycle_template_with_custom_attribute(custom_attribute, template_reference)
          return unless new_review_cycle_template

          # Step 5: For 360 reviews, we don't migrate review responses, just ensure template is associated
          created_count = 1 # Template was found and associated

          # Step 6: Build and return result data (using shared helper)
          result_data = build_result_data(created_count, new_review_cycle_template, old_review_cycle_template)

          Rails.logger.info("360 review template creation completed: #{result_data[:created_count]} items created with template #{template_reference.id}#{old_reviewer.present? ? " for old reviewer #{old_reviewer.id}" : ""}")

          result_data
        else
          # No template found - return error for 360 reviews
          error_msg = "No existing template found for 360 review cycle #{review_cycle.id} and reviewer type #{new_reviewer_type.reviewer_type}. Cannot create 360 review template without source template."
          Rails.logger.error(error_msg)
          errors.add(:template, error_msg)
          return
        end

      rescue StandardError => e
        error_msg = "Failed to create 360 review templates: #{e.message}"
        Rails.logger.error(error_msg)
        errors.add(:base, error_msg)
        nil
      end
    end


  end
end
