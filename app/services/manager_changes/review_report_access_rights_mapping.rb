# ManagerChanges::ReviewReportAccessRightsMapping.call
module ManagerChanges
  class ReviewReportAccessRightsMapping < ApplicationService
    prepend SimpleCommand

    NOTIFICATION_TYPES = {
      admin: ['ReviewMailer#manager_change_summary_notification'],
      manager: ['ReviewMailer#new_manager_direct_reports_notification'],
      direct_report: ['ReviewMailer#direct_report_manager_change_notification'],
      previous_manager: ['ReviewMailer#previous_manager_reassignment_notification'],
    }.freeze

    def initialize(review_cycle_id = nil)
      @review_cycle_id = review_cycle_id
    end

    def call
      return if review_cycles.blank?

      review_cycles.each do |review_cycle|
        process_notifications_for(review_cycle)
      end
    end

    private

    def process_notifications_for(review_cycle)
      NOTIFICATION_TYPES.each do |role, notifications|
        employees = send("#{role}_employees", review_cycle)
        update_notification_settings(employees, notifications)
      end
    end

    def update_notification_settings(employees, notifications)
      EmployeeNotificationSetting.transaction do
        employees.find_each do |employee|
          notifications.each do |notification|
            EmployeeNotificationSetting.find_or_initialize_by(
              employee_id: employee.id,
              notification_key: notification,
              notification_channel: 'email',
            ).tap do |setting|
              setting.enabled = true if setting.new_record?
              setting.save!
            end
          end
        end
      end
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "Failed to update notification settings: #{e.message}"
      Sentry.capture_exception(e.message)
      raise # Re-raise the exception if you want the transaction to roll back
    end

    def admin_employees(review_cycle)
      review_cycle.account.employees.where(id: review_cycle.creator_id)
    end

    def manager_employees(review_cycle)
      review_cycle.account.employees.where(id: review_cycle.reviewees.pluck(:manager_id).compact.uniq)
    end

    def direct_report_employees(review_cycle)
      review_cycle.account.employees.where(id: review_cycle.reviewees.pluck(:employee_id))
    end

    def previous_manager_employees(review_cycle)
      previous_manager_ids = RevieweeSnapshot.joins(:reviewee)
        .where(reviewee_id: review_cycle.reviewees.pluck(:id))
        .where(reason: 'manager_change')
        .pluck(:employee_id)
        .compact
        .uniq

      review_cycle.account.employees.where(id: previous_manager_ids)
    end

    def review_cycles
      @_review_cycles ||= if @review_cycle_id.present?
                            ReviewCycle.where(id: @review_cycle_id)
                          else
                            ReviewCycle.where.not(review_cycles: { enforce_system_manager_mapping: nil })
                              .where('review_cycles.enforce_system_manager_mapping >= ?', Time.current)

                          end
    end
  end
end
