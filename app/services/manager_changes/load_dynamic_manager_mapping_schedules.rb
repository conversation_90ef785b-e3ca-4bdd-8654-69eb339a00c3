module ManagerChanges
  class LoadDynamicManagerMappingSchedules
    # Dynamically registers timezone-based jobs per account
    # Deduplicates jobs so they aren’t double-registered if call runs multiple times
    @registered_job_keys = Set.new

    def self.call
      Account.joins(:review_cycles)
        .where.not(review_cycles: { enforce_system_manager_mapping: nil })
        .where('review_cycles.enforce_system_manager_mapping >= ?', Time.current)
        .distinct
        .find_in_batches(batch_size: 100) do |accounts|
          accounts.each do |account|
            job_key = "daily_manager_mapping_verification_account_#{account.id}"

            next if @registered_job_keys.include?(job_key)

            Clockwork.every(1.day, job_key, at: '18:00', tz: account.timezone) do
              ManagerChanges::ReportReviewCycleMapping.call(account.id)
            end

            @registered_job_keys << job_key
          end
        end
    end
  end
end
