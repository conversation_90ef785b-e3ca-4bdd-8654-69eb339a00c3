# frozen_string_literal: true

module ManagerChanges
  module ReviewTemplateHelpers
    extend ActiveSupport::Concern

    protected

    def extract_custom_attribute_from_snapshot
      return nil unless old_reviewer.present?

      # Find the reviewee snapshot associated with the old reviewer
      snapshot = old_reviewer.reviewee_snapshots.first

      if snapshot.present?
        custom_attribute = snapshot.custom_attribute
        Rails.logger.info("Found reviewee snapshot #{snapshot.id} with custom_attribute: '#{custom_attribute}' for old reviewer #{old_reviewer.id}")
        custom_attribute
      else
        Rails.logger.info("No reviewee snapshot found for old reviewer #{old_reviewer.id}")
        nil
      end
    end

    def fetch_old_review_cycle_template
      return nil unless old_reviewer.present?

      # Get the reviewee from the old_reviewer
      reviewee = old_reviewer.reviewee
      return nil unless reviewee.present?

      # Get the old reviewer type
      old_reviewer_type = ReviewerType.find_by(id: old_reviewer_type_id)
      return nil unless old_reviewer_type.present?

      # Find the review cycle template for the old reviewer type with the reviewee's custom_attribute
      old_review_cycle_template = old_reviewer_type.review_cycle_templates
                                                   .find_by(employee_attribute: reviewee.custom_attribute&.strip&.downcase)

      if old_review_cycle_template.present?
        Rails.logger.info("Found old review cycle template #{old_review_cycle_template.id} for old reviewer type #{old_reviewer_type.id} with employee_attribute: '#{reviewee.custom_attribute}'")
        old_review_cycle_template
      else
        # Fallback: try to find template with nil employee_attribute
        fallback_template = old_reviewer_type.review_cycle_templates.find_by(employee_attribute: nil)
        if fallback_template.present?
          Rails.logger.info("Found fallback review cycle template #{fallback_template.id} for old reviewer type #{old_reviewer_type.id} with nil employee_attribute")
        else
          Rails.logger.warn("No review cycle template found for old reviewer type #{old_reviewer_type.id} with employee_attribute: '#{reviewee.custom_attribute}' or nil")
        end
        fallback_template
      end
    end

    def create_review_cycle_template_with_custom_attribute(custom_attribute, template_reference = nil)
      # Validate that new_reviewer_type has the review_cycle_templates association
      unless new_reviewer_type.respond_to?(:review_cycle_templates)
        errors.add(:new_reviewer_type, 'does not have review_cycle_templates association')
        return nil
      end

      begin
        # Create or find review cycle template with the custom_attribute as employee_attribute
        review_cycle_template = new_reviewer_type.review_cycle_templates
                                                 .find_or_create_by(employee_attribute: custom_attribute&.strip&.downcase) do |rct|
          Rails.logger.info("Creating new review cycle template for reviewer type #{new_reviewer_type.id} with employee_attribute: '#{custom_attribute}'")

          # For 360 reviews, associate with template if provided
          if template_reference.present?
            rct.template = template_reference
            Rails.logger.info("Associating review cycle template with template #{template_reference.id} for 360 review")
          end
        end

        if review_cycle_template.persisted?
          Rails.logger.info("Review cycle template #{review_cycle_template.id} created/found for reviewer type #{new_reviewer_type.id} with employee_attribute: '#{custom_attribute}'#{template_reference.present? ? " and template #{template_reference.id}" : ""}")
          review_cycle_template
        else
          error_msg = "Failed to create review cycle template: #{review_cycle_template.errors.full_messages.join(', ')}"
          Rails.logger.error(error_msg)
          errors.add(:review_cycle_template, error_msg)
          nil
        end

      rescue StandardError => e
        error_msg = "Failed to create review cycle template with custom attribute: #{e.message}"
        Rails.logger.error(error_msg)
        errors.add(:review_cycle_template, error_msg)
        nil
      end
    end

    def find_or_create_360_template(old_review_cycle_template)
      # For 360 reviews, we need to find an existing Template that contains the actual questions
      return nil unless review_cycle.three_sixty_review?

      begin
        if old_review_cycle_template&.template.present?
          # Use the existing template from the old review cycle template
          template = old_review_cycle_template.template
          Rails.logger.info("Found existing template #{template.id} from old review cycle template #{old_review_cycle_template.id}")
          template
        else
          # No existing template found - this is an error condition for 360 reviews
          Rails.logger.error("No existing template found for 360 review cycle #{review_cycle.id} and reviewer type #{new_reviewer_type.reviewer_type}. 360 reviews require an existing template to reference.")
          nil
        end
      rescue StandardError => e
        error_msg = "Failed to find 360 template: #{e.message}"
        Rails.logger.error(error_msg)
        errors.add(:template, error_msg)
        nil
      end
    end

    def build_result_data(created_count, review_cycle_template, old_review_cycle_template, updated_responses_count = 0)
      {
        created_count: created_count,
        review_cycle_template_created: review_cycle_template.present?,
        review_cycle_template_id: review_cycle_template&.id,
        old_template_found: old_review_cycle_template.present?,
        old_template_id: old_review_cycle_template&.id,
        updated_responses_count: updated_responses_count
      }
    end

    def validate_common_inputs
      if review_cycle.blank?
        errors.add(:review_cycle, 'Review cycle not found')
        return false
      end

      if new_reviewer_type.blank?
        errors.add(:new_reviewer_type, 'New reviewer type not found')
        return false
      end

      if old_reviewer_type_id.blank?
        errors.add(:old_reviewer_type_id, 'Old reviewer type ID not provided')
        return false
      end

      true
    end
  end
end
