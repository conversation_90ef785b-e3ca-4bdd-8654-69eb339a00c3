module ManagerChanges
  class ReportReviewCycleMapping < ApplicationService
    prepend SimpleCommand
    attr_reader :account
    attr_accessor :snapshot_created_today

    def initialize(account_id)
      @account = Account.find(account_id)
    end

    def call
      return if account.blank?
      return if review_cycles.blank?

      generate_reports
    end

    private

    def generate_reports
      review_cycles.each do |review_cycle|
        ManagerChanges::ReviewReportAccessRightsMapping.call(review_cycle.id)
        @snapshot_created_today = reviewees_with_snapshot_created_today_only(review_cycle)
        next if snapshot_created_today.blank?
        next if snapshot_created_today.pluck(:reviewee_id).uniq.count.zero?
        next unless review_cycle.creator.active?

        # Generate 4 reports for each review cycle
        generate_admin_report(review_cycle)
        next if review_cycle.review_cycle_notifications.blank?

        generate_dr_report(review_cycle)
        generate_manager_report(review_cycle)
        generate_previous_manager_report(review_cycle)
      end
    end

    def review_cycles
      @_review_cycles ||= account.review_cycles.where.not(enforce_system_manager_mapping: nil)
        .where('enforce_system_manager_mapping >= ?', Time.current).uniq
    end

    def reviewees_with_snapshot_created_today_only(review_cycle)
      # created in the range of start of the day to end of the day
      RevieweeSnapshot.joins(:reviewee).where(created_at: Time.current.all_day)
        .where(reviewee_id: review_cycle.reviewees.pluck(:id))
    end

    def map_snapshot_data
      # a reviewee can have many snapshots created in a day
      # snapshot can have multiple reasons
      # reason 1: manager creation - new manager reviewer type for that reviewee created
      # reason 2: manager change - old manager reviewer  for that reviewee changed,moved or deleted
      # reason 3: direct report creation - new direct report reviewer  for that reviewee created
      # reason 4: direct report change - old direct report reviewer  for that reviewee changed,moved or deleted
      # this can happen a number of times
      # we want to present all this to the review creator
      # each row represent a change, we want to show this change
      # if the reviewee has been deleted, snapshot.reviewer will return nil
      # if they have been moved, snapshot.reviewer will return the old reviewer
      # if we did old reviewer.reviewer type, it will return the new reviewer type
      # if the snapshot reason is manager creation and the reviewer.employee is same as reviewee.employee, who this is the current state
      # if reason has anything to do with creation, that record does not have old reviewer but current reviewer
      # data needed, a reviewee can appear in a report multiple times
      #  1. the reviewee name and email
      #  2. the current reviewee manager
      # 3. the old reviewer name and email
      # 4. what is the current state of that reviewer,deleted or moved
      # 5. the timestamp of the change, factor in the account timezone
      # 6. the reviewee goal submission status at the moment of the change
      # 7. the reviewee goal approval status at the moment of the change
      # 8. the reviewee peer selection status at the moment of the change
      # 9. the reviewee peer approval review status at the moment of the change
      # 10. had the reviewer submitted a review
      # 11. the reviewer new reviewer type, if it was moved
      # 12. the reviewer new reviewer type, if its a creation
      manager_creation_snapshots = snapshot_created_today.select do |snapshot|
        snapshot.reason.include?('manager_creation') || snapshot.reason.include?('manager_change')
      end.group_by(&:reviewee_id)

      manager_creation_snapshots.map do |reviewee_id, snapshots|
        reviewee = Reviewee.find_by(id: reviewee_id)
        current_manager = reviewee.manager
        # Get previous managers from manager_change snapshots
        previous_manager_snapshots = snapshots.select { |s| s.reason.include?('manager_change') }
        previous_managers_emails = ' '

        previous_managers_list = previous_manager_snapshots.each_with_index.map do |snapshot, idx|
          manager_name = snapshot.employee.full_name
          reviewer = snapshot.reviewer
          previous_managers_emails << if previous_manager_snapshots.count > 1
                                        "#{idx + 1}. #{manager_name} (#{snapshot.employee.user.email}) <br>"
                                      else
                                        "#{manager_name} (#{snapshot.employee.user.email})"
                                      end
          submitted_status = if reviewer.nil?
                               'Not Submitted'
                             else
                               (reviewer.review_submitted ? 'Submitted' : 'Not Submitted')
                             end
          if previous_manager_snapshots.count > 1
            "#{idx + 1}. #{manager_name} (#{submitted_status})"
          else
            "#{manager_name} (#{submitted_status})"
          end
        end.join('<br>')
        {
          'Reviewee Name' => reviewee.employee.full_name,
          'Reviewee Email' => reviewee.employee.user.email,
          'New Manager' => current_manager.full_name,
          'New Manager Email' => current_manager.user.email,
          'Previous Managers' => previous_managers_list,
          'Previous Managers Emails' => previous_managers_emails,
          'Goals Status' => build_goals_status(reviewee),
          'Peers Status' => build_peers_status(reviewee),

        }
      end
    end

    def build_goals_status(snapshot)
      if snapshot.goal_approval_done
        'Approved'
      else
        (snapshot.goal_selection_done ? 'Submitted' : 'Not Submitted')
      end
    end

    def build_peers_status(snapshot)
      if snapshot.peer_approval_done
        'Approved'
      else
        (snapshot.peer_selection_done ? 'Selected' : 'Not Selected')
      end
    end

    def format_change_reason(reason)
      reason.split('_').map(&:capitalize).join(' ')
    end

    def group_snapshots_by_new_manager
      # Group snapshots by new manager for manager creation scenarios
      new_manager_snapshots = snapshot_created_today.select { |snapshot| snapshot.reason.include?('manager_creation') }
      return {} if new_manager_snapshots.blank?

      reviewees = Reviewee.where(id: new_manager_snapshots.pluck(:reviewee_id).compact.uniq)
      return {} if reviewees.blank?

      reviewees.group_by(&:manager_id).transform_values do |reviewees|
        reviewees.map do |reviewee|
          previous_manager = Employee.find_by(id: previous_manager_id(reviewee))

          {
            'Reviewee Name' => reviewee.employee.full_name,
            'Reviewee Email' => reviewee.employee.user.email,
            'Previous Manager' => previous_manager ? previous_manager.full_name.to_s : 'Unknown',
            'Goals Status' => build_goals_status(reviewee),
            'Peers Status' => build_peers_status(reviewee),
            'Review by Old Manager' => any_review_written_by_old_managers(reviewee),
            'Your Action' => determine_manager_action(reviewee),
            'Form Link' => form_url(reviewee),

            # Additional metadata
            reviewee_id: reviewee.id,
          }
        end
      end
    end

    def any_review_written_by_old_managers(reviewee)
      reviewee_snapshots = snapshot_created_today.select do |snapshot|
        snapshot.reason.include?('manager_change') && snapshot.reviewee_id == reviewee.id
      end
      return 'Not Submitted' if reviewee_snapshots.blank?

      old_reviewers = Reviewer.where(id: reviewee_snapshots.pluck(:reviewer_id).compact.uniq)
      return 'Not Submitted' if old_reviewers.blank?

      return 'Submitted' if old_reviewers.where(review_submitted: true).present?

      'Not Submitted'
    end

    def previous_manager_id(reviewee)
      return nil unless reviewee.versions.loaded? || reviewee.versions.any?

      # Look for the last change to `manager_id` that differs from the current one
      manager_changes = reviewee.versions.map(&:object_changes).compact.select { |changes| changes.key?('manager_id') }

      # Find the most recent previous value not equal to current
      previous = manager_changes.reverse.find do |change|
        change['manager_id'].last != reviewee.manager_id
      end

      previous ? previous['manager_id'].last : nil
    end

    def form_url(reviewee)
      # Determine what action the new manager needs to take
      manager_reviewer_type = reviewee.review_cycle.reviewer_types.find_by(reviewer_type: :manager)
      return nil if manager_reviewer_type.blank?

      manager_reviewer = reviewee.reviewers.find_by(reviewer_type: manager_reviewer_type)
      return nil if manager_reviewer.blank?

      manager_phase = reviewee.review_cycle.review_cycle_phases.find_by(phase_type: :manager_summary)
      if manager_phase.nil? || manager_phase.start_time.nil?
        "https://#{ENV['APP_DOMAIN']}/dashboard"
      else
        "https://#{ENV['APP_DOMAIN']}/dashboard/reviews/#{reviewee.review_cycle.id}/reviewers/reviewees_for_reviewer/#{manager_reviewer_type.id}"
      end
    end

    def determine_manager_action(reviewee)
      # Determine what action the new manager needs to take
      manager_reviewer_type = reviewee.review_cycle.reviewer_types.find_by(reviewer_type: :manager)
      return 'No Action Required' if manager_reviewer_type.blank?

      manager_reviewer = reviewee.reviewers.find_by(reviewer_type: manager_reviewer_type)
      return 'Setup Required' if manager_reviewer.blank?

      if manager_reviewer.review_submitted?
        'Review Completed'
      else
        'Write Review'
      end
    end

    def group_snapshots_by_reviewee
      # Group snapshots by reviewee for direct report notifications (manager creation/change scenarios)
      reviewee_snapshots = snapshot_created_today.select do |snapshot|
        snapshot.reason.include?('manager_creation') || snapshot.reason.include?('manager_change') ||
          snapshot.reason.include?('direct_report_creation') || snapshot.reason.include?('direct_report_change')
      end
      return [] if reviewee_snapshots.blank?

      # Group by reviewee_id and get the latest snapshot for each reviewee (final state only)
      grouped_snapshots = reviewee_snapshots.group_by(&:reviewee_id)

      grouped_snapshots.map do |_reviewee_id, snapshots|
        # Get the latest snapshot for this reviewee (final state)
        latest_snapshot = snapshots.max_by(&:created_at)
        reviewee_record = latest_snapshot.reviewee
        new_manager = reviewee_record.manager

        # Check if reviewee will be writing a review for the new manager
        will_review_manager = check_if_reviewee_reviews_manager(reviewee_record, new_manager)

        {
          reviewee_id: reviewee_record.employee_id,
          reviewee_name: reviewee_record.employee.full_name,
          reviewee_email: reviewee_record.employee.user.email,
          new_manager: new_manager,
          new_manager_name: new_manager.full_name,
          will_review_manager: will_review_manager,
          goals_approved: reviewee_record.goal_approval_done?,
          change_reason: format_change_reason(latest_snapshot.reason),
          change_time: latest_snapshot.created_at.in_time_zone(account.timezone).strftime('%b %d, %l:%M %p %Z').strip,

          # Additional metadata
          snapshot_id: latest_snapshot.id,
          review_cycle_id: reviewee_record.review_cycle_id,
        }
      end
    end

    def check_if_reviewee_reviews_manager(reviewee_record, new_manager)
      # Check if the reviewee will be writing a review for their new manager
      # This happens when the new manager is also a reviewee in the same cycle
      manager_as_reviewee = reviewee_record.review_cycle.reviewees.find_by(employee: new_manager)
      return false if manager_as_reviewee.blank?

      # Check if this reviewee is assigned to review their manager
      manager_reviewers = manager_as_reviewee.reviewers.where(employee: reviewee_record.employee)
      manager_reviewers.exists?
    end

    def group_snapshots_by_previous_manager
      # Group snapshots by previous manager for manager change scenarios
      previous_manager_snapshots = snapshot_created_today.select do |snapshot|
        snapshot.reason.include?('manager_change')
      end
      return [] if previous_manager_snapshots.blank?

      # Group by previous manager (snapshot.employee is the old manager)
      grouped_snapshots = previous_manager_snapshots.group_by(&:employee_id)

      grouped_snapshots.map do |previous_manager_id, snapshots|
        previous_manager = Employee.find_by(id: previous_manager_id)
        next if previous_manager.blank?

        reviewees_data = snapshots.map do |snapshot|
          reviewee_record = snapshot.reviewee
          new_manager = reviewee_record.manager

          {
            'Reviewee Name' => reviewee_record.employee.full_name,
            'Reviewee Email' => reviewee_record.employee.user.email,
            'New Manager' => new_manager.full_name,
            'New Manager Email' => new_manager.user.email,
            'Goals Status' => build_goals_status(snapshot),
            'Peers Status' => build_peers_status(snapshot),
            'Change Time' => snapshot.created_at.in_time_zone(account.timezone).strftime('%b %d, %l:%M %p %Z').strip,

            # Additional metadata
            reviewee_id: reviewee_record.employee_id,
            new_manager_id: new_manager.id,
            snapshot_id: snapshot.id,
          }
        end

        {
          previous_manager: previous_manager,
          reviewees_data: reviewees_data,
        }
      end.compact
    end

    def generate_admin_report(review_cycle)
      # Admin report logic - send email to review cycle creator
      reviewee_count = snapshot_created_today.pluck(:reviewee_id).uniq.count
      return if reviewee_count.zero?

      summary_data = map_snapshot_data
      admin = review_cycle.creator

      return if admin.blank?
      return unless email_notification_enabled?(admin, 'manager_change_summary_notification')

      # Send admin email notification
      ReviewMailer.manager_change_summary_notification(
        review_cycle: review_cycle,
        admin: admin,
        reviewee_count: reviewee_count,
        summary_data: summary_data,
      ).deliver_now

      Rails.logger.info("Generated admin report for review cycle #{review_cycle.id}: #{reviewee_count} reviewees affected, email sent to #{admin.full_name}")
    end

    def generate_dr_report(review_cycle)
      # Direct report (dr) report logic - send email to reviewees about their manager change
      direct_report_data = group_snapshots_by_reviewee
      return if direct_report_data.blank?

      direct_report_data.each do |reviewee_data|
        reviewee = Employee.find_by(id: reviewee_data[:reviewee_id])
        next if reviewee.blank?
        next unless email_notification_enabled?(reviewee, 'direct_report_manager_change_notification')

        # Send direct report email notification
        ReviewMailer.direct_report_manager_change_notification(
          review_cycle: review_cycle,
          reviewee: reviewee,
          new_manager: reviewee_data[:new_manager],
          reviewee_data: reviewee_data,
        ).deliver_now

        Rails.logger.info("Direct report notification sent to #{reviewee.full_name} for review cycle #{review_cycle.id}: manager changed to #{reviewee_data[:new_manager].full_name}")
      end
    end

    def generate_manager_report(review_cycle)
      # New Manager report logic - send email to new managers about their new direct reports
      new_managers_data = group_snapshots_by_new_manager
      return if new_managers_data.blank?

      new_managers_data.each do |manager_id, reviewees_data|
        manager = Employee.find_by(id: manager_id)
        next if manager.blank?
        next unless email_notification_enabled?(manager, 'new_manager_direct_reports_notification')

        # Send new manager email notification
        ReviewMailer.new_manager_direct_reports_notification(
          review_cycle: review_cycle,
          manager: manager,
          reviewees_data: reviewees_data,
        ).deliver_now

        Rails.logger.info("New manager report sent to #{manager.full_name} for review cycle #{review_cycle.id}: #{reviewees_data.count} new direct reports")
      end
    end

    def generate_previous_manager_report(review_cycle)
      # Previous manager report logic - send email to previous managers about their reassigned direct reports
      previous_managers_data = group_snapshots_by_previous_manager
      return if previous_managers_data.blank?

      previous_managers_data.each do |previous_manager_data|
        previous_manager = previous_manager_data[:previous_manager]
        reviewees_data = previous_manager_data[:reviewees_data]
        next unless email_notification_enabled?(previous_manager, 'previous_manager_reassignment_notification')

        # Send previous manager email notification
        ReviewMailer.previous_manager_reassignment_notification(
          review_cycle: review_cycle,
          previous_manager: previous_manager,
          reviewees_data: reviewees_data,
        ).deliver_now

        Rails.logger.info("Previous manager notification sent to #{previous_manager.full_name} for review cycle #{review_cycle.id}: #{reviewees_data.count} reassigned reviewees")
      end
    end

    def email_notification_enabled?(employee, notification_type)
      EmployeeNotificationSetting.exists?(
        employee_id: employee.id,
        notification_key: "ReviewMailer##{notification_type}",
        notification_channel: 'email',
        enabled: true,
      )
    end
  end
end
