# when the review cycle status change to live,
#  we need to verify that all the reviewees have the correct manager mapping
# get all the reviewee whose reviewee.manager is not same as reviewee.employee.managar is not same
# update the reviewee.manager to that diffrent managar
# ManagerChanges::VerifyReviewCycleMapping.call
module ManagerChanges
  class VerifyReviewCycleMapping < ApplicationService
    prepend SimpleCommand

    attr_reader :review_cycle_id

    def initialize(review_cycle_id)
      @review_cycle_id = review_cycle_id
    end

    def call
      return if review_cycle_id.blank?
      return if review_cycle.blank?
      return if review_cycle.status != 'live'
      return if review_cycle.enforce_system_manager_mapping.nil?
      return if review_cycle.enforce_system_manager_mapping <= Time.now.utc
      return if reviewees.blank?

      process_reviewees
    end

    private

    def process_reviewees
      reviewees.each do |reviewee|
        update_reviewee_manager(reviewee)
      end
    end

    def reviewees
      @_reviewees ||= review_cycle.reviewees.joins(:employee).where('reviewees.manager_id != employees.manager_id')
    end

    def review_cycle
      @_review_cycle ||= ReviewCycle.find(@review_cycle_id)
    end

    def update_reviewee_manager(reviewee)
      ActiveRecord::Base.transaction do
        reviewee.update(manager: reviewee.employee.manager)
      end
    end
  end
end
