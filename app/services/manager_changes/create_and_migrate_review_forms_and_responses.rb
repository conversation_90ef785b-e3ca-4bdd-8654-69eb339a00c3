# frozen_string_literal: true

module ManagerChanges
  class CreateAndMigrateReviewFormsAndResponses < ApplicationService
    prepend SimpleCommand

    attr_reader :reviewee_id, :old_reviewer_id, :new_reviewer_type_id, :old_reviewer_type_id

    def initialize(reviewee_id:, old_reviewer_id:, new_reviewer_type_id:, old_reviewer_type_id:)
      @reviewee_id = reviewee_id
      @old_reviewer_id = old_reviewer_id
      @new_reviewer_type_id = new_reviewer_type_id
      @old_reviewer_type_id = old_reviewer_type_id
    end

    def call
      return unless validate_inputs

      begin
        ActiveRecord::Base.transaction do
          # Create review forms for the new reviewer type
          forms_result = create_review_forms

          # Check if forms creation had errors
          if forms_result[:errors].any?
            forms_result[:errors].each { |error| errors.add(:forms_creation, error) }
            raise ActiveRecord::Rollback
          end

          # Build final result
          result_data = {
            forms_created: forms_result[:created_count] || 0,
            responses_migrated: 0, # NOTE: Response migration will be handled separately when new reviewer is created
            review_cycle_template_created: forms_result[:review_cycle_template_created] || false,
            old_template_found: forms_result[:old_template_found] || false,
            updated_responses_count: forms_result[:updated_responses_count] || 0,
          }

          Rails.logger.info("Review forms and responses migration completed for reviewee #{reviewee_id}: #{result_data}")
          result_data
        end
      rescue StandardError => e
        Rails.logger.error("Review forms and responses migration failed for reviewee #{reviewee_id}: #{e.message}")
        errors.add(:base, "Migration failed: #{e.message}")
        nil
      end
    end

    private

    def validate_inputs
      if reviewee.blank?
        errors.add(:reviewee, 'Reviewee not found')
        return false
      end

      if old_reviewer.blank?
        errors.add(:old_reviewer, 'Old reviewer not found')
        return false
      end

      if new_reviewer_type.blank?
        errors.add(:new_reviewer_type, 'New reviewer type not found')
        return false
      end

      if old_reviewer_type.blank?
        errors.add(:old_reviewer_type, 'Old reviewer type not found')
        return false
      end

      true
    end

    def reviewee
      @_reviewee ||= Reviewee.find_by(id: reviewee_id)
    end

    def review_cycle
      @_review_cycle ||= reviewee&.review_cycle
    end

    def old_reviewer
      @_old_reviewer ||= Reviewer.find_by(id: old_reviewer_id)
    end

    def new_reviewer_type
      @_new_reviewer_type ||= ReviewerType.find_by(id: new_reviewer_type_id)
    end

    def old_reviewer_type
      @_old_reviewer_type ||= ReviewerType.find_by(id: old_reviewer_type_id)
    end

    def create_review_forms
      # Check for unsupported review cycle types first
      unless review_cycle.performance_review? || review_cycle.three_sixty_review?
        error_msg = "Unsupported review cycle type: #{review_cycle.review_type}"
        Rails.logger.error(error_msg)
        return { created_count: 0, errors: [error_msg] }
      end

      # Determine which service to use based on review cycle type
      service_result = if review_cycle.performance_review?
                         Rails.logger.info("Using CreateNormalPerfReviewRct for performance review cycle #{review_cycle.id}")
                         ManagerChanges::CreateNormalPerfReviewRct.call(
                           review_cycle,
                           old_reviewer_type_id,
                           new_reviewer_type,
                           old_reviewer,
                         )
                       else # three_sixty_review
                         Rails.logger.info("Using CreateThreeSixtyPerfReviewTemplate for 360 review cycle #{review_cycle.id}")
                         ManagerChanges::CreateThreeSixtyPerfReviewTemplate.call(
                           review_cycle,
                           old_reviewer_type_id,
                           new_reviewer_type,
                           old_reviewer,
                         )
                       end
      # Handle service result
      if service_result.success?
        result_data = service_result.result || {}
        Rails.logger.info("Review forms creation completed using #{review_cycle.review_type} service: #{result_data[:created_count] || 0} items created")

        # Ensure we have the expected structure
        {
          created_count: result_data[:created_count] || 0,
          review_cycle_template_created: result_data[:review_cycle_template_created] || false,
          old_template_found: result_data[:old_template_found] || false,
          updated_responses_count: result_data[:updated_responses_count] || 0,
          errors: [],
        }
      else
        error_messages = service_result.errors.full_messages
        Rails.logger.error("Review forms creation failed: #{error_messages.join(', ')}")

        {
          created_count: 0,
          review_cycle_template_created: false,
          old_template_found: false,
          updated_responses_count: 0,
          errors: error_messages,
        }
      end
    rescue StandardError => e
      error_msg = "Failed to create review forms: #{e.message}"
      Rails.logger.error(error_msg)

      {
        created_count: 0,
        review_cycle_template_created: false,
        old_template_found: false,
        updated_responses_count: 0,
        errors: [error_msg],
      }
    end
  end
end
