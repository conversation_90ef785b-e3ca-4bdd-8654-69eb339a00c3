# frozen_string_literal: true

module ManagerChanges
  class TriggerAutoRemapping < ApplicationService
    prepend SimpleCommand

    attr_reader :reviewee_id, :review_cycle_id, :previous_manager_id, :new_manager_id

    def initialize(reviewee_id, review_cycle_id, previous_manager_id, new_manager_id)
      @reviewee_id = reviewee_id
      @review_cycle_id = review_cycle_id
      @previous_manager_id = previous_manager_id
      @new_manager_id = new_manager_id
    end

    def call
      return errors.add(:reviewee, 'Reviewee not found') if reviewee.blank?
      return errors.add(:review_cycle, 'Review cycle not found') if review_cycle.blank?
      return errors.add(:new_manager, 'New manager not found') if new_manager.blank?

      ActiveRecord::Base.transaction do
        # Create new manager reviewer if needed
        create_new_manager_reviewer_result = create_new_manager_reviewer

        # Handle peer reviewer cleanup when manager changes
        cleanup_peer_reviewers

        {
          new_reviewer_created: create_new_manager_reviewer_result,
          reviewee_id: reviewee_id,
          review_cycle_id: review_cycle_id,
        }
      end
    end

    private

    def send_silent_notification(reviewer_type_id, reviewer_id)
      notification_result = ManagerChanges::SendReviewerPhaseSilentNotification.call(
       reviewer_type_id: reviewer_type_id,
       reviewer_id: reviewer_id,
     )
      if notification_result
        Rails.logger.info("Silent notification sent successfully for reviewee")
      else
        Rails.logger.warn("Silent notification failed for reviewee")
      end
    end

    def reviewee
      @_reviewee ||= Reviewee.find_by(id: reviewee_id)
    end

    def review_cycle
      @_review_cycle ||= ReviewCycle.find_by(id: review_cycle_id)
    end

    def new_manager
      @_new_manager ||= Employee.find_by(id: new_manager_id)
    end

    def create_new_manager_reviewer
      # Find the manager reviewer type for this review cycle
      manager_reviewer_type = review_cycle.reviewer_types.find_by(reviewer_type: 'manager')

      unless manager_reviewer_type.present?
        Rails.logger.info("No manager reviewer type found for review cycle #{review_cycle_id}")
        return { created: false, reason: 'no_manager_reviewer_type' }
      end

      # Check if new manager already has a reviewer record for this reviewee
      existing_reviewer = review_cycle.reviewers.find_by(
        review_cycle: review_cycle,
        employee: new_manager,
        reviewee: reviewee,
        reviewer_type: manager_reviewer_type,
      )

      if existing_reviewer.present?
        # Call the silent notification service
        send_silent_notification(manager_reviewer_type.id, existing_reviewer.id)
        Rails.logger.info("New manager #{new_manager_id} already has reviewer record for reviewee #{reviewee_id} in cycle #{review_cycle_id}")
        return { created: false, reason: 'already_exists', reviewer_id: existing_reviewer.id }
      end

      # Create new reviewer record for the new manager
      new_reviewer = review_cycle.reviewers.find_or_create_by!(
        review_cycle: review_cycle,
        reviewer_type: manager_reviewer_type,
        reviewee: reviewee,
        employee: new_manager,
      )

      update_reviewee_attributes(new_reviewer)
      # Call the silent notification service
      send_silent_notification(manager_reviewer_type.id, new_reviewer.id)
      Rails.logger.info("Created new manager reviewer #{new_reviewer.id} for manager #{new_manager_id}, reviewee #{reviewee_id}, cycle #{review_cycle_id}")
      { created: true, reason: 'reviewer_created', reviewer_id: new_reviewer.id }
    end

    def update_reviewee_attributes(new_reviewer)
      if new_reviewer.present?
        reviewee.update(manager_summary_done: false)
        Rails.logger.info("Updated reviewee #{reviewee_id} manager_summary_done to false")
      end
    end

    def cleanup_peer_reviewers
      # Remove new manager as peer reviewer for this reviewee
      # A manager cannot also be a peer reviewer for the same person
      manager_peer_reviewers = reviewee.reviewers
        .joins(:reviewer_type)
        .where(reviewer_types: { reviewer_type: 'peer' })
        .where(employee: new_manager)

      if manager_peer_reviewers.present?
        Rails.logger.info("Removing new manager #{new_manager_id} as peer reviewer for reviewee #{reviewee_id}")
        manager_peer_reviewers.discard_all
      end

      # Remove reviewee as peer reviewer for the new manager (if manager is also a reviewee)
      # If the new manager is being reviewed in this cycle, the reviewee cannot be their peer reviewer
      manager_as_reviewee = review_cycle.reviewees.find_by(employee: new_manager)

      if manager_as_reviewee.present?
        reviewee_as_peer = manager_as_reviewee.reviewers
          .joins(:reviewer_type)
          .where(reviewer_types: { reviewer_type: 'peer' })
          .where(employee: reviewee.employee)

        if reviewee_as_peer.present?
          Rails.logger.info("Removing reviewee #{reviewee_id} as peer reviewer for manager #{new_manager_id}")
          reviewee_as_peer.discard_all
        end
      end
    end
  end
end
