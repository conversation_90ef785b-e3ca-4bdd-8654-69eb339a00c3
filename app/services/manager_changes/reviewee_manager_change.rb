# ManagerChanges::RevieweeManagerChange
module ManagerChanges
  class RevieweeManagerChange < ApplicationService
    prepend SimpleCommand
    attr_reader :reviewee, :previous_manager_id, :new_manager_id, :employee_id, :reviewee_id, :review_cycle_id

    def initialize(reviewee, previous_manager_id, new_manager_id)
      @reviewee = reviewee
      @previous_manager_id = previous_manager_id
      @new_manager_id = new_manager_id
      @employee_id = reviewee.employee_id
      @reviewee_id = reviewee.id
      @review_cycle_id = reviewee.review_cycle_id
    end

    def call
      trigger_manager_change_event
    end

    def trigger_manager_change_event
      return if should_skip_manager_change?

      resolve_missing_previous_manager

      publish_manager_change_event
      trigger_map_old_managers_direct_report_event(previous_manager_id)
      trigger_map_new_manager_direct_report_event(@new_manager_id)
      trigger_manage_peer_selection_home_page_actions_event
      trigger_manage_goal_home_page_actions_event
    end

    def trigger_map_old_managers_direct_report_event(previous_manager_id)
      return unless previous_manager_id.present?

      event = Events::Definitions::MoveDirectReportReviewerToCustomTypeDefinition.new(
        data: {
          reviewer_employee_id: @employee_id,
          reviewee_employee_id: previous_manager_id,
          review_cycle_id: @review_cycle_id,
        }.with_indifferent_access,
      )

      publish_event(
        event: event,
        event_type: 'MoveDirectReportReviewerToCustomTypeDefinition',
        additional_metadata: { reviewer_employee_id: @employee_id,
                               reviewee_employee_id: previous_manager_id },
      )
    end

    def trigger_map_new_manager_direct_report_event(new_manager_id)
      return unless new_manager_id.present?

      event = Events::Definitions::MapNewManagerDirectReportDefinition.new(
        data: {
          reviewer_employee_id: @employee_id,
          reviewee_employee_id: new_manager_id,
          review_cycle_id: @review_cycle_id,
        }.with_indifferent_access,
      )

      publish_event(
        event: event,
        event_type: 'MapNewManagerDirectReportDefinition',
        additional_metadata: {
          reviewer_employee_id: @employee_id,
          reviewee_employee_id: new_manager_id,
        },
      )
    end

    def trigger_manage_peer_selection_home_page_actions_event
      event = Events::Definitions::ManagePeerSelectionHomePageActionsDefinition.new(
        data: {
          employee_id: @employee_id,
          reviewee_id: @reviewee_id,
          review_cycle_id: @review_cycle_id,
          previous_manager_id: @previous_manager_id,
          new_manager_id: @new_manager_id,
        }.with_indifferent_access,
      )

      publish_event(
        event: event,
        event_type: 'ManagePeerSelectionHomePageActionsDefinition',
        additional_metadata: {
          previous_manager_id: @previous_manager_id,
          new_manager_id: @new_manager_id,
        },
      )
    end

    def trigger_manage_goal_home_page_actions_event
      event = Events::Definitions::ManageGoalHomePageActionsDefinition.new(
        data: {
          employee_id: @employee_id,
          reviewee_id: @reviewee_id,
          review_cycle_id: @review_cycle_id,
          previous_manager_id: @previous_manager_id,
          new_manager_id: @new_manager_id,
        }.with_indifferent_access,
      )

      publish_event(
        event: event,
        event_type: 'ManageGoalHomePageActionsDefinition',
        additional_metadata: {
          previous_manager_id: @previous_manager_id,
          new_manager_id: @new_manager_id,
        },
      )
    end

    private

    def should_skip_manager_change?
      (@previous_manager_id.nil? && @new_manager_id.nil?) ||
        @reviewee.review_cycle.enforce_system_manager_mapping.nil? ||
        @reviewee.review_cycle.enforce_system_manager_mapping <= Time.now.utc ||
        !@reviewee.review_cycle.live?
    end

    def resolve_missing_previous_manager
      return unless @previous_manager_id.nil?

      manager_reviewer = @reviewee.reviewers.joins(:reviewer_type)
        .where(reviewer_types: { reviewer_type: 'manager' })
        .where.not(employee: @new_manager_id)
        .first
      @previous_manager_id = manager_reviewer.employee_id if manager_reviewer.present?
    end

    def publish_manager_change_event
      event = Events::Definitions::CreateAndMapNewManagerReviewerDefinition.new(
        data: {
          employee_id: @employee_id,
          reviewee_id: @reviewee_id,
          review_cycle_id: @review_cycle_id,
          previous_manager_id: @previous_manager_id,
          new_manager_id: @new_manager_id,
        }.with_indifferent_access,
      )

      publish_event(
        event: event,
        event_type: 'CreateAndMapNewManagerReviewerDefinition',
      )
    end

    def publish_event(event:, event_type:, additional_metadata: {})
      unless event.valid?
        Rails.logger.error("Invalid #{event_type} event for reviewee #{@reviewee_id}: #{event.errors.full_messages}")
        return
      end

      Events::Publisher.publish(
        event: event,
        stream_name: EventStore::StreamNames.for_employee(@employee_id),
        metadata: {
          triggered_by: 'reviewee_model_callback', # Maintained original value
          reviewee_id: @reviewee_id,
          review_cycle_id: @review_cycle_id,
        }.merge(additional_metadata),
        async: true,
      )

      Rails.logger.info("#{event_type} event published for reviewee #{@reviewee_id}")
    rescue StandardError => e
      Rails.logger.error("Failed to publish #{event_type} event for reviewee #{@reviewee_id}: #{e.message}")
      Sentry.capture_exception(e) if defined?(Sentry)
    end
  end
end
