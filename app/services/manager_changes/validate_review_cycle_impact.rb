# frozen_string_literal: true

module ManagerChanges
  class ValidateReviewCycleImpact < ApplicationService
    prepend SimpleCommand

    attr_reader :employee_id, :previous_manager_id, :new_manager_id

    def initialize(employee_id, previous_manager_id, new_manager_id)
      @employee_id = employee_id
      @previous_manager_id = previous_manager_id
      @new_manager_id = new_manager_id
    end

    def call
      return errors.add(:employee_id, 'Employee not found') if employee.blank?
      return errors.add(:new_manager_id, 'New manager not found') if new_manager.blank?

      reviewees_needing_update = find_reviewees_needing_update

      {
        reviewees_needing_update: reviewees_needing_update,
        no_changes_needed: reviewees_needing_update.empty?,
        eligible_review_cycles_count: eligible_review_cycles.count,
        total_reviewees_found: employee_reviewees.count,
      }
    end

    private

    def employee
      @_employee ||= Employee.find_by(id: employee_id)
    end

    def new_manager
      @_new_manager ||= Employee.find_by(id: new_manager_id)
    end

    def eligible_review_cycles
      @_eligible_review_cycles ||= employee.account.review_cycles.where(
        'enforce_system_manager_mapping IS NOT NULL AND enforce_system_manager_mapping >= ?',
        Time.now.utc,
      )
    end

    def employee_reviewees
      @_employee_reviewees ||= Reviewee.joins(:review_cycle)
        .where(employee_id: employee_id)
        .where(review_cycle: eligible_review_cycles)
        .includes(:review_cycle)
    end

    def find_reviewees_needing_update
      employee_reviewees.map do |reviewee|
        next unless reviewee.manager_id != new_manager_id

        {
          reviewee_id: reviewee.id,
          review_cycle_id: reviewee.review_cycle_id,
          current_manager_id: reviewee.manager_id,
          review_cycle_name: reviewee.review_cycle.title,
        }
      end.compact
    end
  end
end
