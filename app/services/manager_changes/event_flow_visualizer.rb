# # Current day only
# ManagerChanges::EventFlowVisualizer.new(employee).

# # Specific day (e.g., 5 days ago)
# ManagerChanges::EventFlowVisualizer.new(employee, 5).

# # Range of days (e.g., 10 to 3 days ago)
# ManagerChanges::EventFlowVisualizer.new(employee, 10..3).

module ManagerChanges
  class EventFlowVisualizer
    # Initializes the visualizer with an employee and optional input
    # Supported input:
    # - nil (defaults to today)
    # - Integer (N.days.ago)
    # - Range (N..M.days.ago)
    def initialize(employee, input = nil)
      @employee = employee
      @range = parse_input_to_range(input)
      @stream_name = EventStore::StreamNames.for_employee(employee.id)
    end

    # Builds the execution flow structure for the selected date range
    def execution_flow
      events = fetch_events
      return empty_response if events.empty?

      event_map = build_event_map(events)
      root_events = find_root_events(events, event_map)

      {
        employee_id: @employee.id,
        events: root_events.map { |event| build_flow_node(event, event_map) },
        date_range: format_range(@range),
      }
    end

    # Returns a printed flow (either as :text or :json)
    def print_flow(formatter: :text)
      flow = execution_flow
      case formatter
      when :json
        format_for_json(flow)
      when :text
        text_representation(flow[:events])
      end
    end

    private

    # Parses input (Integer, Range, or nil) into a Date range
    def parse_input_to_range(input)
      case input
      when Range
        (input.first.to_i.days.ago.to_date)..(input.last.to_i.days.ago.to_date)
      when Integer
        day = input.to_i.days.ago.to_date
        day..day
      else
        Date.current..Date.current
      end
    end

    # Fetches events for the employee in the given time range
    def fetch_events
      start_time = @range.first.beginning_of_day
      end_time = @range.last.end_of_day

      Rails.configuration.event_store.read
        .stream(@stream_name)
        .forward
        .between(start_time...end_time)
        .to_a
    rescue RubyEventStore::EventNotFound, RubyEventStore::IncorrectStreamData
      []
    end

    # Fallback for empty event list
    def empty_response
      { employee_id: @employee.id, events: [], date_range: format_range(@range) }
    end

    # Maps events by their ID for easy lookup
    def build_event_map(events)
      events.index_by(&:event_id)
    end

    # Finds root-level events (not triggered by other known events)
    def find_root_events(events, event_map)
      events.select do |event|
        original_id = event.metadata[:original_event_id]
        original_id.nil? || !event_map.key?(original_id)
      end
    end

    # Recursively builds a tree of triggered events
    def build_flow_node(event, event_map, depth = 0)
      {
        event_type: event.class.name.demodulize,
        event_id: event.event_id,
        triggered_by: event.metadata[:triggered_by],
        timestamp: format_timestamp(event.metadata[:timestamp]),
        data: filter_sensitive_data(event.data),
        depth: depth,
        called_events: event_map.values
          .select { |child| child.metadata[:original_event_id] == event.event_id }
          .map { |child| build_flow_node(child, event_map, depth + 1) },
      }
    end

    # Formats timestamps for output
    def format_timestamp(timestamp)
      timestamp.respond_to?(:iso8601) ? timestamp.iso8601 : timestamp.to_s
    end

    # Filters out sensitive fields (e.g., token, password)
    def filter_sensitive_data(data)
      return {} unless data.is_a?(Hash)

      data.each_with_object({}) do |(key, value), filtered|
        if key.to_s.in?(%w[encrypted_data password token])
          next
        elsif value.is_a?(String)
          filtered[key] = value.length > 50 ? "#{value[0..10]}...[truncated]" : value
        elsif value.is_a?(Hash)
          filtered[key] = filter_sensitive_data(value)
        else
          filtered[key] = value
        end
      end
    end

    # Transforms a hash to safely serialize it into JSON
    def format_for_json(flow)
      transformed = deep_transform_values(flow) do |value|
        case value
        when Time, DateTime, ActiveSupport::TimeWithZone
          value.iso8601
        else
          value
        end
      end

      JSON.pretty_generate(transformed)
    end

    # Deeply transforms all values in nested structures
    def deep_transform_values(obj, &block)
      case obj
      when Hash
        obj.transform_values { |v| deep_transform_values(v, &block) }
      when Array
        obj.map { |v| deep_transform_values(v, &block) }
      else
        yield(obj)
      end
    end

    # Renders a text view of the event tree
    def text_representation(nodes, output = [], indent = 0)
      nodes.each do |node|
        indent_str = '  ' * indent
        output << "#{indent_str}└ #{node[:event_type]} (id: #{node[:event_id]})"
        output << "#{indent_str}   Triggered by: #{node[:triggered_by]}"
        output << "#{indent_str}   Timestamp: #{node[:timestamp]}"
        output << "#{indent_str}   Data: #{node[:data].inspect}"

        text_representation(node[:called_events], output, indent + 1) if node[:called_events].any?
      end
      output.join("\n")
    end

    # Formats the date range as a string
    def format_range(range)
      "#{range.first.strftime('%Y-%m-%d')}..#{range.last.strftime('%Y-%m-%d')}"
    end
  end
end
