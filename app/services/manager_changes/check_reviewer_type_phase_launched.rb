# frozen_string_literal: true

module ManagerChanges
  class CheckReviewerTypePhaseLaunched < ApplicationService
    prepend SimpleCommand

    attr_reader :review_cycle_id, :reviewer_employee_id, :target_reviewer_type, :reviewee_employee_id

    def initialize(review_cycle_id:, reviewer_employee_id:, target_reviewer_type:, reviewee_employee_id:)
      @review_cycle_id = review_cycle_id
      @reviewer_employee_id = reviewer_employee_id
      @target_reviewer_type = target_reviewer_type
      @reviewee_employee_id = reviewee_employee_id
    end

    def call
      return false unless validate_inputs
      return false unless review_cycle.present? && reviewer_employee.present? && reviewer_type.present? &&
        reviewee.present? && reviewer.present?

      # Find the appropriate phase based on reviewer type
      phase = find_appropriate_phase(review_cycle, reviewer_type)
      return false if phase.blank?

      # when we sync data from the BE for them
      return true if reviewer.review_responses.present? || reviewer.review_submitted?

      # Check if the phase has been launched (start_time is not nil and is in the past)
      phase_launched = phase.start_time.present? && phase.start_time <= Time.current

      # Also check if the reviewee employee has received any notification from this phase
      notification_sent_out = check_notification_sent_out?(phase)

      phase_type = phase.phase_type
      Rails.logger.info("Phase check for reviewer type #{target_reviewer_type} in cycle #{review_cycle_id}: checking #{phase_type} phase, launched=#{phase_launched}, notification_received=#{notification_sent_out}")

      # Both conditions must be true: phase launched AND notification received

      phase_launched && notification_sent_out && (reviewer.review_responses.present? || reviewer.review_submitted?)
    rescue StandardError => e
      Rails.logger.error("CheckReviewerTypePhaseLaunched failed for reviewer #{reviewer_employee_id} in cycle #{review_cycle_id}: #{e.message}")
      errors.add(:base, "Failed to check phase launch status: #{e.message}")
      false
    end

    private

    def review_cycle
      @_review_cycle ||= ReviewCycle.find_by(id: review_cycle_id)
    end

    def reviewer_employee
      @_reviewer_employee ||= Employee.find_by(id: reviewer_employee_id)
    end

    def reviewee
      @_reviewee ||= review_cycle.reviewees.find_by(employee_id: reviewee_employee_id)
    end

    def reviewer_type
      @_reviewer_type ||= review_cycle.reviewer_types.find_by(reviewer_type: target_reviewer_type)
    end

    def reviewer
      @_reviewer ||= review_cycle.reviewers
        .joins(:reviewer_type)
        .where(reviewer_types: { reviewer_type: reviewer_type.reviewer_type })
        .find_by(employee: reviewer_employee, reviewee: reviewee)
    end

    def validate_inputs
      if review_cycle_id.blank?
        errors.add(:review_cycle_id, 'Review cycle ID is required')
        return false
      end

      if reviewer_employee_id.blank?
        errors.add(:reviewer_employee_id, 'Reviewer employee ID is required')
        return false
      end

      if target_reviewer_type.blank?
        errors.add(:target_reviewer_type, 'Target reviewer type is required')
        return false
      end

      true
    end

    def find_appropriate_phase(review_cycle, reviewer_type)
      if reviewer_type.reviewer_type == 'manager'
        # For manager reviewer type, look for manager_summary phase
        review_cycle.review_cycle_phases.find_by(phase_type: 'manager_summary')
      else
        # For other reviewer types, look for write_reviews phase
        # First try to find phase specific to this reviewer type
        phase = review_cycle.review_cycle_phases.find_by(
          phase_type: 'write_reviews',
          reviewer_type: reviewer_type,
        )

        # If no specific phase found, look for general write_reviews phase (reviewer_type is nil)
        phase || review_cycle.review_cycle_phases.find_by(
          phase_type: 'write_reviews',
          reviewer_type: nil,
        )
      end
    end

    def check_notification_sent_out?(phase)
      phase.review_cycle_notifications.exists?
    end
  end
end
