# frozen_string_literal: true

module ManagerChanges
  class SendReviewerPhaseSilentNotification < ApplicationService
    prepend SimpleCommand

    attr_reader :reviewer_type_id, :reviewer_id

    def initialize(reviewer_type_id:, reviewer_id:)
      @reviewer_type_id = reviewer_type_id
      @reviewer_id = reviewer_id
    end

    def call
      return false unless validate_inputs
      return false unless review_cycle.present? && reviewer_employee.present? && reviewer_type.present?

      # Find the appropriate phase based on reviewer type
      phase = find_appropriate_phase(review_cycle, reviewer_type)
      return false if phase.blank?
      
      return false if reviewer_type.normal? && phase.review_cycle_notifications.blank?

      # Create silent notification for the reviewer
      create_silent_notification(phase)

      Rails.logger.info(
        "Silent notification created for reviewer #{reviewer_employee.id} "
      )

      true
    rescue StandardError => e
      Rails.logger.error(
        "SendReviewerPhaseSilentNotification failed for reviewer #{reviewer_employee.id} " \
        "in cycle #{review_cycle.id}: #{e.message}",
      )
      errors.add(:base, "Failed to send silent notification: #{e.message}")
      false
    end

    private

    def review_cycle
      @_review_cycle ||= reviewer_type.review_cycle
    end

    def reviewer_employee
      @_reviewer_employee ||= Reviewer.find_by(id: reviewer_id).employee
    end

    def reviewer_type
      @_reviewer_type ||= ReviewerType.find_by(id: reviewer_type_id)
    end

    def validate_inputs
      if review_cycle.blank?
        errors.add(:review_cycle, 'Review cycle is required')
        return false
      end

      if reviewer_employee.blank?
        errors.add(:reviewer_employee, 'Reviewer employee is required')
        return false
      end

      if reviewer_type.blank?
        errors.add(:reviewer_type, 'reviewer type is required')
        return false
      end

      true
    end

    def find_appropriate_phase(review_cycle, reviewer_type)
      if reviewer_type.reviewer_type == 'manager'
        # For manager reviewer type, look for manager_summary phase
        review_cycle.review_cycle_phases.find_by(phase_type: 'manager_summary')
      else
        # For other reviewer types, look for write_reviews phase
        # First try to find phase specific to this reviewer type
        phase = review_cycle.review_cycle_phases.find_by(
          phase_type: 'write_reviews',
          reviewer_type: reviewer_type,
        )

        # If no specific phase found, look for general write_reviews phase (reviewer_type is nil)
        phase || review_cycle.review_cycle_phases.find_by(
          phase_type: 'write_reviews',
          reviewer_type: nil,
        )
      end
    end

    def create_silent_notification(phase)
      # Use the standard notification creation service
      launch_data = {
        participants: [reviewer_employee.id],
        channel: 'silent_launch',
        schedule: 'now',
        task_type: 'launch',
        participants_type: nil,
        current_user: nil, # System-generated notification
      }

      PerformanceReview::CreateReviewCyclePhaseNotifications.call(
        review_cycle.id,
        phase.id,
        launch_data,
      )
    end
  end
end
