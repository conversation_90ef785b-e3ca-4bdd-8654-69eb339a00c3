module ManagerChanges
  class RevieweePreviousManagerActions < ApplicationService
    def initialize(reviewee)
      @reviewee = reviewee
    end

    def call
      {
        goals: goals_actions,
        peers: peers_actions,
      }
    end

    private

    attr_reader :reviewee

    def goals_actions
      goal_approver_ids = reviewee.reviewee_goals.where(exclude: false).pluck(:approver_id).uniq

      {
        approved_by_previous_manager: goal_approval_done_with_previous_manager?(goal_approver_ids),
        approved_by: approver_info(goal_approver_ids.first),
      }
    end

    def peers_actions
      peer_approver_ids = peer_approver_ids_for_reviewee

      {
        approved_by_previous_manager: peer_approval_done_with_previous_manager?(peer_approver_ids) &&
          peer_approver_ids.present?,
        approved_by: approver_info(peer_approver_ids),
      }
    end

    def goal_approval_done_with_previous_manager?(goal_approver_ids)
      reviewee.goal_approval_done &&
        goal_approver_ids.present? &&
        goal_approver_ids != [reviewee.manager_id]
    end

    def peer_approval_done_with_previous_manager?(peer_approver_ids)
      peer_selection_or_approval_completed? &&
        peer_approver_ids != [reviewee.manager_id]
    end

    def peer_approver_ids_for_reviewee
      admin_role = reviewee.review_cycle.review_cycle_user_roles.find_by(role_type: 'admin')
      if reviewee.review_cycle.peer_selection_role == 'manager'
        reviewee.reviewers.joins(:reviewer_type)
          .where(reviewer_types: { reviewer_type: 'peer' })
          .where('nominator_role_id IS NULL OR nominator_role_id != ?', admin_role.id)
          .pluck(:nominator_id).flatten.compact.uniq
      else
        reviewee.reviewers.joins(:reviewer_type)
          .where(reviewer_types: { reviewer_type: 'peer' })
          .where('approver_role_id IS NULL OR approver_role_id != ?', admin_role.id)
          .pluck(:approver_id, :rejected_by_id).flatten.compact.uniq
      end
    end

    def peer_selection_or_approval_completed?
      case reviewee.review_cycle.peer_selection_role
      when 'manager'
        reviewee.peer_selection_done
      else
        reviewee.peer_approval_done
      end
    end

    def approver_info(approver_id)
      return { id: nil, full_name: nil } unless approver_id

      employee = Employee.find_by(id: approver_id)
      {
        id: approver_id,
        full_name: employee&.full_name,
      }
    end
  end
end
