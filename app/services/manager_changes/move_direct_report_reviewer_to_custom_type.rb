# ManagerChanges::MoveDirectReportReviewerToCustomType.call(reviewer_employee_id: 58244,review_cycle_id: 6222, reviewee_employee_id: 15553)
module ManagerChanges
  class MoveDirectReportReviewerToCustomType < ApplicationService
    prepend SimpleCommand

    def initialize(reviewer_employee_id:, review_cycle_id:, reviewee_employee_id:)
      @reviewer_employee_id = reviewer_employee_id # direct report employee id
      @reviewee_employee_id = reviewee_employee_id # manager's employee id
      @review_cycle_id = review_cycle_id
    end

    def call
      # 1. Fetch the review cycle
      return errors.add(:review_cycle, 'Review cycle not found') if review_cycle.blank?

      # 2. Fetch the managers reviewee record

      if manager_reviewee.blank?
        return {
          success: false,
          reason: 'Manager reviewee not found',
          reviewer_employee_id: @reviewer_employee_id,
          reviewee_employee_id: @reviewee_employee_id,
          review_cycle_id: @review_cycle_id,

        }
      end

      # 3. Fetch the direct_report reviewer type for this cycle
      if direct_report_reviewer_type.blank?
        return {
          success: false,
          reason: 'Direct report reviewer type not found',
          reviewer_employee_id: @reviewer_employee_id,
          reviewee_employee_id: @reviewee_employee_id,
          review_cycle_id: @review_cycle_id,
        }

      end

      # 4. Fetch the direct report (reviewer) based on the employee record, reviewee and the reviewer type
      existing_reviewer = find_existing_direct_report_reviewer
      if existing_reviewer.blank?
        return {
          success: false,
          reason: 'Direct report reviewer not found',
          reviewer_employee_id: @reviewer_employee_id,
          reviewee_employee_id: @reviewee_employee_id,
          review_cycle_id: @review_cycle_id,
        }

      end

      # Return success response with all necessary data for the handler
      {
        success: true,
        reviewer_employee_id: existing_reviewer.employee_id,
        reviewee_employee_id: manager_reviewee.employee_id,
        review_cycle_id: @review_cycle_id,
        reviewer_id: existing_reviewer.id,
        reviewee_id: manager_reviewee.id,
      }
    end

    private

    def review_cycle
      @_review_cycle ||= ReviewCycle.find_by(id: @review_cycle_id)
    end

    def manager_reviewee
      @_manager_reviewee ||= review_cycle.reviewees.find_by(
        employee_id: @reviewee_employee_id,
        review_cycle_id: @review_cycle_id,
      )
    end

    def direct_report_employee
      @_direct_report_employee ||= Employee.find_by(id: @reviewer_employee_id)
    end

    def direct_report_reviewer_type
      @_direct_report_reviewer_type ||= review_cycle.reviewer_types.find_by(
        review_cycle_id: @review_cycle_id,
        reviewer_type: 'direct_report',
      )
    end

    def find_existing_direct_report_reviewer
      # Find the direct report reviewer based on employee, reviewee, and reviewer type
      review_cycle.reviewers.find_by(
        employee_id: @reviewer_employee_id,
        reviewee: manager_reviewee,
        reviewer_type: direct_report_reviewer_type,
        review_cycle: review_cycle,
      )
    end
  end
end
