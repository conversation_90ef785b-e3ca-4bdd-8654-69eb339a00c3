require 'google/apis/calendar_v3'
require "googleauth"

class SyncGoogleCalendarEvents < ApplicationService
  attr_reader :user, :employee, :add_event, :events_list, :token, :calendar_setting, :items_arr, :run_fetch_events, :events_arr

  def initialize(user_id, add_event, calendar_setting_id, run_fetch_events)
    @user = User.find(user_id)
    @employee = @user.employee
    @add_event = add_event
    @calendar_setting = CalendarSetting.find_by(id: calendar_setting_id)
    @run_fetch_events = run_fetch_events
    @token = @user.google_token
    @items_arr = []
    @events_arr = []
  end

  def call
    return unless token
    return unless token.calendar_grant

    create_service_auth
    get_events
    update_employee
    create_update_one_on_ones
    create_update_calendar_settings
  end

  def create_service_auth
    @service = Google::Apis::CalendarV3::CalendarService.new
    @service.authorization = token.google_secret.to_authorization
    return unless token.expired?

    new_access_token = @service.authorization.refresh!
    token.access_token = new_access_token['access_token']
    token.expires_at = Time.now.to_i + new_access_token['expires_in'].to_i
    token.save
  end

  def get_events
    Rails.logger.silence do
      begin
        @events_list = @service.list_events('primary', single_events: true, max_results: 500, sync_token: calendar_setting.sync_token)
        @sync_token = @events_list.next_sync_token
        @page_token = @events_list.next_page_token
        items_arr << @events_list.items
        items_arr_count = items_arr.size
        store_events(items_arr)
        items_arr = []

        while @sync_token.blank?
          @events_list = @service.list_events('primary', single_events: true, max_results: 500, page_token: @page_token)
          @sync_token = @events_list.next_sync_token
          @page_token = @events_list.next_page_token
          items_arr << @events_list.items
          items_arr_count = items_arr_count + items_arr.size
          store_events(items_arr)
          items_arr = []
        end

        FetchGoogleCalendarEvents.delay(queue: 'high').call(user.id, false) if (items_arr_count == 0 || run_fetch_events)
      rescue
        FullSyncGoogleCalendar.call(user.id, true)
      end
    end
  end

  def store_events(items_arr)
    return unless items_arr.present?

    items_arr.flatten.each do |item|
      if item.status == 'cancelled'
        @calendar_event = CalendarEvent.find_by(cal_event_id: item.id)
        if @calendar_event.present?
          @calendar_event&.one_on_one&.archived!
          @calendar_event.destroy
        end
      else
        next unless item.try(:attendees).try(:count)
        next unless item.summary
        next unless item.start.date_time
        next unless item.start.date_time <= Time.current.end_of_year + 1.year

        @calendar_event = CalendarEvent.find_by(cal_event_id: item.id)

        if @calendar_event.present?
          events_arr << update_event(item)
          if @calendar_event.one_on_one_id.present?
            one_on_one = @calendar_event.one_on_one
            one_on_one.update!(scheduled_time: @calendar_event.start_time, start_time: @calendar_event.start_time, end_time: @calendar_event.end_time, title: @calendar_event.title, cal_id: @calendar_event.cal_event_id, ical_uid: @calendar_event.ical_uid, recurring_event_id: @calendar_event.recurring_event_id)
          end
        else
          events_arr << create_event(item)
        end
      end
    end
  end

  def update_employee
    employee.calendar_data_last_updated_at = Time.current
    employee.save!
  end

  def create_update_one_on_ones
    return if events_arr.blank?

    AddOneOnOneFromEvents.call(events_arr.map(&:id), add_event)
  end

  def create_update_calendar_settings
    return if @events_list.blank?

    calendar_setting = CalendarSetting.where(employee: employee).where('expiration > ?', Time.now).last
    calendar_setting = CalendarSetting.create!(employee: employee, sync_token: @events_list.next_sync_token) if calendar_setting.nil?
    calendar_setting.sync_token = @events_list.next_sync_token
    calendar_setting.save!
  end

  def build_attributes(item)
    timezone = employee.get_timezone
    manager, report, reportee_email, attendees_email = calendar_event_attendees_details(item).values_at(
      :manager,
      :report,
      :reportee_email,
      :attendees_email
    )

    if (report.present?) && (report&.manager != manager) && (report&.direct_reports&.include? manager)
      manager, report = report, manager
      reportee_email = report&.user&.email&.downcase
    end

    start_time = OneOnOne.resolve_scheduled_time(item.start.date_time.strftime('%m/%d/%Y'), item.start.date_time.strftime('%I:%M:%S %p'), timezone)
    end_time = OneOnOne.resolve_scheduled_time(item.end.date_time.strftime('%m/%d/%Y'), item.end.date_time.strftime('%I:%M:%S %p'), timezone)
    title = item.summary

    cal_event_id = item.id
    recurring_event_id =  item.recurring_event_id
    ical_uid  = item.i_cal_uid
    html_link = item.html_link
    full_name = item.attendees&.find{|a| a.email&.downcase == reportee_email}&.display_name.try(:titleize) || reportee_email&.split('@')&.first.try(:titleize) || ''

    {
      manager: manager, title: title, start_time: start_time,
      end_time: end_time, attendee_email: reportee_email,
      html_link: html_link, attendee_full_name: full_name,
      cal_event_id: cal_event_id, ical_uid: ical_uid,
      recurring_event_id: recurring_event_id,
      attendees: attendees_email
    }
  end

  def is_calendar_organizer_event(creator, attendees, attendees_email)
    (attendees&.count || 0) == 3 &&
      creator.present? &&
      creator.calendar_event_organizer_clients.count.positive? &&
      (creator.calendar_event_organizer_clients.map { |c| c.user.email.downcase } & attendees_email).present?
  end

  def remove_meeting_rooms(attendees_email)
    return attendees_email if attendees_email.nil?
    attendees_email.reject { |x| x.include? "resource.calendar.google.com" }
  end
  
  def calendar_event_attendees_details(item)
    attendees = item.attendees
    attendees_email = attendees&.map { |attendee| attendee.email&.downcase }.uniq
    creator = User.find_by(email: item.creator.email)&.employee
    attendees_email = remove_meeting_rooms(attendees_email)

    if is_calendar_organizer_event(creator, attendees, attendees_email)
      attendees_email = attendees_email.reject { |e| e == item.creator.email }
      manager = User.find_by(email: attendees_email[0])&.employee
      report = User.find_by(email: attendees_email[1])&.employee
      reportee_email = attendees_email[1]
    else
      manager = @employee
      reportee_email = attendees_email&.reject { |a| a&.downcase == user.email&.downcase }.try(:first)
      report = User.where(email: reportee_email)&.first&.employee
    end
    {
      attendees_email: attendees_email,
      manager: manager,
      reportee_email: reportee_email,
      report: report
    }
  end

  def create_event(item)
    CalendarEvent.create!(build_attributes(item))
  end

  def update_event(item)
    @calendar_event.update!(build_attributes(item))
    @calendar_event
  end

end
