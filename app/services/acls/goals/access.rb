module Acls
  module Goals
    class Access < ::Acls::Base::Access
      prepend SimpleCommand

      def call
        return unfiltered_result if no_filter_targets?

        super_call = super
        return errors.merge(super_call.errors) unless super_call.success?

        return feature_disabled_result unless feature_enabled?

        targetable_scope = employee.admin? ? Goal.none : goals_targetable
        super_call.result.merge(
          {
            goals_targetable: targetable_scope,
            goals_targetable_sql: targetable_scope.select('goals.id').to_sql.presence,
          },
        )
      end

      private

      def unfiltered_result
        account = employee.account
        unfiltered_employees = account.employees
        unfiltered_goals = account.goals

        { access: product_access?,
          super_admin: false,
          employee_targetable: unfiltered_employees,
          employee_targetable_sql: unfiltered_employees.select('employees.id').to_sql.presence,
          goals_targetable: unfiltered_goals,
          goals_targetable_sql: unfiltered_goals.select('goals.id').to_sql.presence }
      end

      def no_filter_targets?
        target_goal_table_filter_ids.blank? &&
          target_employee_ids.blank? &&
          target_employee_filter_ids.blank?
      end

      def feature_disabled_result
        super.merge(goals_targetable: Goal.none, goals_targetable_sql: '')
      end

      def target_goal_table_filter_ids
        @_target_goal_table_filter_ids = Acl.where(
          actorable_type: 'AclRole',
          actorable_id: employee.acl_role_ids,
          resource: product_action,
          targetable_type: 'GoalTableFilter',
        ).pluck(:targetable_id)
      end

      def goals_targetable
        variable_values = { 'current_employee_id' => employee.id }
        goal_base_criteria = Goal.left_joins(:goals_departments, :employee_goals).where(account_id: employee.account_id)
        GoalTableFilter.apply_all(
          target_goal_table_filter_ids,
          base_criteria: goal_base_criteria,
          variable_values: variable_values,
        ).distinct(:id)
      end
    end
  end
end
