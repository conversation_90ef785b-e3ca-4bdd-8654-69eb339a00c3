module Acls
  module Base
    class Access
      prepend SimpleCommand

      attr_reader :employee, :product_action, :base_scope

      def initialize(employee, product_action_key:)
        @employee = employee
        @product_action = ProductAction.find_by(key: product_action_key)
        @base_scope = Employee.where(account_id: employee.account_id)
      end

      def call
        return errors.add(:base, 'Employee not found!') if employee.blank?

        return feature_disabled_result unless feature_enabled?
        return super_admin_result if employee.admin?

        return errors.add(:base, 'Employee is not an admin!') unless employee.acl_role_admin?
        return errors.add(:base, 'Product Action not found!') if product_action.blank?

        targetable_scope = product_access? ? employee_targetable : Employee.none
        {
          access: product_access?,
          super_admin: false,
          employee_targetable: targetable_scope,
          employee_targetable_sql: targetable_scope.select('employees.id').to_sql.presence,
        }
      end

      private

      # When changing the feature flag settings, make sure to clear the cache for all accounts
      #
      # Account.pluck(:id).each do |account_id|
      #   Rails.cache.delete("posthog/goals_access_control/#{account_id}")
      # end
      #
      # See: Cache key structure in PosthogClient.feature_enabled?
      def feature_enabled?
        return true unless Rails.env.production?

        PosthogClient.feature_enabled?('goals-access-control', employee)
      end

      def super_admin_result
        { access: true, super_admin: true, employee_targetable: Employee.none }
      end

      def feature_disabled_result
        { access: false, super_admin: employee.admin?, employee_targetable: Employee.none, employee_targetable_sql: '' }
      end

      def target_employee_ids
        @_target_employee_ids = Acl.where(
          actorable_type: 'AclRole',
          actorable_id: employee.acl_role_ids,
          resource: product_action,
          targetable_type: 'Employee',
        ).pluck(:targetable_id)
      end

      def target_employee_filter_ids
        @_target_employee_filter_ids = Acl.where(
          actorable_type: 'AclRole',
          actorable_id: employee.acl_role_ids,
          resource: product_action,
          targetable_type: 'EmployeeFilter',
        ).pluck(:targetable_id)
      end

      def employee_targetable
        emp_scope1 = Employee.where(id: target_employee_ids)

        variable_values = { 'current_employee_id' => employee.id }
        emp_scope2 = EmployeeFilter.apply_all(target_employee_filter_ids, base_criteria: base_scope, variable_values: variable_values)

        (emp_scope1.or(emp_scope2)).distinct(:id)
      end

      def product_access?
        @_product_access ||= begin
          account_access = AccountProduct.exists?(account_id: employee.account_id, product_id: product_action.product_id)
          emp_access = Acl.exists?(actorable_type: 'AclRole', actorable_id: employee.acl_role_ids, resource: product_action)

          account_access && emp_access
        end
      end
    end
  end
end
