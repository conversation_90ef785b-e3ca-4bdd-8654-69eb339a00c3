class Employee
  module SelectLists
    class AddableReviewees < ApplicationService
      include Employee::SelectLists::EmployeesListHelper
      prepend SimpleCommand

      def initialize(account:, current_user:, params: {}, groups: [])
        @account = account
        @current_user = current_user
        @review_cycle = account.review_cycles.find_by(id: params[:review_cycle_id])
        @params = params
        @search = params[:search]
        @groups = groups
        @limit = params[:limit].to_i.positive? ? params[:limit].to_i : 10
      end

      def call
        return errors.add(:base, 'Review Cycle not found!') if review_cycle.blank?

        build_base_query
        apply_search
        return employees.limit(limit).pluck(:id) if groups.blank?

        build_grouped_employee_ids
      end

      private

      attr_reader :account, :review_cycle

      def build_base_query
        scope = account.employees.active.where.not(id: review_cycle.reviewees.pluck(:employee_id))
        scope = Pundit.policy_scope!(current_user, scope).joins(:user)
        @employees = scope.left_joins(:department) if include_department?
      end
    end
  end
end
