class Employee
  module SelectLists
    module Employees<PERSON>ist<PERSON>elper
      private

      attr_reader :params, :search, :employees, :selected_employee_ids, :limit, :current_user, :groups

      def parse_selected_employees
        return [] unless params[:selected_employees].present?

        params[:selected_employees].split(',').map(&:to_i).select(&:positive?)
      end

      def apply_search
        return unless search.present?

        search_term = "%#{search}%"
        search_condition = 'employees.full_name LIKE :search OR users.email LIKE :search'
        search_condition += ' OR departments.name LIKE :search' if include_department?

        @employees = employees.where(search_condition, search: search_term)
      end

      def prioritize_selected_employees
        non_selected_emp_base_query = employees.where.not(id: selected_employee_ids)
        non_selected_emp_base_query = non_selected_emp_base_query.limit(limit) if groups.blank?
        non_selected_employee_ids = non_selected_emp_base_query.ids
        combined_ids = selected_employee_ids + non_selected_employee_ids

        if combined_ids.present?
          @employees = account.employees.where(id: combined_ids)
          @employees = employees.order("FIELD(employees.id,#{combined_ids.join(',')})")
        end
      end

      def build_grouped_employee_ids
        group_limit = (limit.to_f / groups.size).ceil
        groups.each_with_object({}) do |group, result|
          base_scope =
            case group
            when 'direct_reports'
              employees.where(manager_id: current_user.employee.id)
            when 'non_direct_reports'
              employees.where('employees.manager_id IS NULL OR employees.manager_id != ?', current_user.employee.id)
            when 'admins'
              employees.where(org_role: 'admin')
            when 'non_admins'
              employees.where.not(org_role: 'admin')
            end
          result[group] = base_scope.limit(group_limit).pluck(:id)
        end
      end

      def include_department?
        return true unless params.key?(:department)

        params[:department].to_s.downcase != 'false'
      end
    end
  end
end
