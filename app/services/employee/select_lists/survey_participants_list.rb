class Employee
  module SelectLists
    class SurveyParticipantsList < ApplicationService
      prepend SimpleCommand

      def initialize(account:, params: {}, groups: [])
        @account = account
        @survey = account.schedules.find_by(id: params[:schedule_survey_id])
        @report_type = params[:report_type]
        @params = params
        @groups = groups
      end

      def call
        return errors.add(:base, 'Account not found!') if account.blank?
        return errors.add(:base, 'Survey not found!') if survey.blank?
        return errors.add(:base, 'Report type should be present!') if report_type.blank?
        return errors.add(:base, 'Invalid report type!') unless valid_report_type?

        fetch_participants
      end

      private

      attr_reader :account, :survey, :report_type, :params, :groups

      def valid_report_type?
        %w[list_all list_non_respondents].include?(report_type)
      end

      def fetch_participants
        SurveyParticipants::Participants::Index.call(participant_params)
      end

      def participant_params
        {
          account_id: account.id,
          schedule_survey_id: params[:schedule_survey_id],
          report_type: params[:report_type],
          search: params[:search],
          groups: groups,
          limit: params[:limit],
          ids_only: true,
          department: params[:department],
          selected_employees: params[:selected_employees],
        }
      end
    end
  end
end
