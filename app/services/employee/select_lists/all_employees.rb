class Employee
  module SelectLists
    class AllEmployees < ApplicationService
      include Employee::SelectLists::EmployeesListHelper
      prepend SimpleCommand

      def initialize(account:, current_user:, params: {}, groups: [])
        @account = account
        @current_user = current_user
        @params = params
        @search = params[:search]
        @skip_acl = params[:skip_acl].presence == 'true'
        @selected_employee_ids = parse_selected_employees
        @groups = groups
        @limit = params[:limit].to_i.positive? ? params[:limit].to_i : 10
      end

      def call
        return errors.add(:base, 'Account not found!') if account.blank?

        @employees = build_base_query
        apply_search
        prioritize_selected_employees
        return employees.pluck(:id) if groups.blank?

        build_grouped_employee_ids
      end

      private

      attr_reader :account, :skip_acl 

      def build_base_query
        scope = account.employees.active
        scope = Pundit.policy_scope!(current_user, scope) unless skip_acl
        scope = scope.joins(:user)
        scope = scope.left_joins(:department) if include_department?
        scope
      end
    end
  end
end
