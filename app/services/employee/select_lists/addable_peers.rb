class Employee
  module SelectLists
    class AddablePeers < ApplicationService
      prepend SimpleCommand

      def initialize(account:, current_user:, params: {}, groups: [])
        @account = account
        @current_user = current_user
        @reviewee = Reviewee
          .joins(:review_cycle)
          .where(id: params[:reviewee_id], review_cycles: { account_id: account.id })
          .first
        @params = params
        @groups = groups
      end

      def call
        return errors.add(:base, 'Account not found!') if account.blank?
        return errors.add(:base, 'Reviewee not found!') if reviewee.blank?

        fetch_peers
      end

      private

      attr_reader :account, :current_user, :reviewee, :params, :groups

      def fetch_peers
        PerformanceReview::WebPerformanceReview::RevieweePeerSearch.call(
          account,
          current_user.employee,
          reviewee,
          peer_search_params,
        )
      end

      def peer_search_params
        {
          search_text: params[:search],
          intent: params[:intent],
          groups: groups,
          limit: params[:limit],
          ids_only: true,
          department: params[:department],
        }
      end
    end
  end
end
