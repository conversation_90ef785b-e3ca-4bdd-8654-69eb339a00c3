class Employee
  module SelectLists
    class AddableReviewers < ApplicationService
      include Employee::SelectLists::EmployeesListHelper
      prepend SimpleCommand

      def initialize(account:, current_user:, params: {}, groups: [])
        @account = account
        @current_user = current_user
        @review_cycle = account.review_cycles.find_by(id: params[:review_cycle_id])
        @reviewee = review_cycle&.reviewees&.find_by(id: params[:reviewee_id])
        @reviewer_type = review_cycle&.reviewer_types&.find_by(id: params[:reviewer_type_id])
        @params = params
        @search = params[:search]
        @selected_employee_ids = parse_selected_employees
        @groups = groups
        @limit = params[:limit].to_i.positive? ? params[:limit].to_i : 10
      end

      def call
        return errors.add(:base, 'Review Cycle not found!') if review_cycle.blank?
        return errors.add(:base, 'Reviewee not found!') if reviewee.blank?
        return errors.add(:base, 'Reviewer type not found!') if reviewer_type.blank?

        @employees = build_base_query
        apply_search
        prioritize_selected_employees
        return employees.pluck(:id) if groups.blank?

        build_grouped_employee_ids
      end

      private

      attr_reader :account, :review_cycle, :reviewee, :reviewer_type

      def build_base_query
        existing_reviewer_employee_ids = reviewee.reviewers.where(reviewer_type_id: reviewer_type&.id)
          .pluck(:employee_id).uniq

        scope = account.employees.active.where.not(id: existing_reviewer_employee_ids)
        scope = Pundit.policy_scope!(current_user, scope).joins(:user)
        scope = scope.left_joins(:department) if include_department?
        scope
      end
    end
  end
end
