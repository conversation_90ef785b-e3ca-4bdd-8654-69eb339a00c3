class Employee
  class SelectList < ApplicationService
    prepend SimpleCommand

    def initialize(account:, current_user:, params: {})
      @account = account
      @current_user = current_user
      @params = params
      @action_type = params[:action_type] || 'list_all'
      @groups = params[:groups].to_s.split(',').map(&:strip)
    end

    def call
      validate_groups
      return if errors.any?

      fetch_base_employee_ids || []
      if groups.present?
        build_grouped_employees
      else
        build_ungrouped_employees
      end
    end

    private

    attr_reader :account, :current_user, :params, :action_type, :groups, :employee_ids

    def validate_groups
      return if groups.empty?

      valid_groups = %w[direct_reports non_direct_reports admins non_admins]
      invalid_groups = groups - valid_groups

      errors.add(:groups, "Invalid groups: #{invalid_groups.join(', ')}") if invalid_groups.any?
    end

    def fetch_base_employee_ids
      service_result =
        case action_type
        when 'non_reviewee_participants'
          Employee::SelectLists::AddableReviewees.call(
            account: account,
            current_user: current_user,
            params: params,
            groups: groups,
          )
        when 'addable_reviewee_manager'
          Employee::SelectLists::AddableRevieweeManager.call(
            account: account,
            current_user: current_user,
            params: params,
            groups: groups,
          )
        when 'addable_reviewers'
          Employee::SelectLists::AddableReviewers.call(
            account: account,
            current_user: current_user,
            params: params,
            groups: groups,
          )
        when 'list_all'
          Employee::SelectLists::AllEmployees.call(
            account: account,
            current_user: current_user,
            params: params,
            groups: groups,
          )
        when 'addable_peers'
          Employee::SelectLists::AddablePeers.call(
            account: account,
            current_user: current_user,
            params: params,
            groups: groups,
          )
        when 'survey_participants'
          Employee::SelectLists::SurveyParticipantsList.call(
            account: account,
            params: params,
            groups: groups,
          )
        end
      if service_result.success?
        @employee_ids = service_result.result
      else
        errors.add(:base, service_result.errors.full_messages.join(', '))
      end
    end

    def build_grouped_employees
      employee_ids.each_with_object({}) do |(group, ids), result|
        if ids.blank?
          result[group] = []
        else
          scoped_employees = build_base_query(ids)
          result[group] = scoped_employees.map { |employee| format_employee(employee) }
        end
      end
    end

    def build_ungrouped_employees
      build_base_query.map { |employee| format_employee(employee) }
    end

    def build_base_query(ids = [])
      base_query_ids = ids.presence || employee_ids
      query = account.employees.where(id: base_query_ids).joins(:user)
      query = query.order("FIELD(employees.id, #{base_query_ids.join(',')})") if base_query_ids.present?
      query = query.left_joins(:department) if include_department?
      query.select(select_fields_array.join(', '))
    end

    def select_fields_array
      fields = ['employees.id', 'employees.full_name', 'users.email']
      fields << 'departments.name as department_name' if include_department?
      fields << 'employees.profile_picture' if include_profile_picture?
      fields
    end

    def format_employee(employee)
      result = {
        id: employee.id,
        full_name: employee.full_name,
        email: employee.email,
      }

      result[:department_name] = employee.department_name if include_department?
      result[:profile_picture] = employee.profile_picture&.thumb&.url if include_profile_picture?

      result
    end

    def include_department?
      return true unless params.key?(:department)

      params[:department].to_s.downcase != 'false'
    end

    def include_profile_picture?
      return true unless params.key?(:profile_picture)

      params[:profile_picture].to_s.downcase != 'false'
    end
  end
end
