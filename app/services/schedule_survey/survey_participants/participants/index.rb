require 'csv'

module SurveyParticipants
  module Participants
    class Index < ApplicationService
      include SurveyParticipants::Shared::SharedModule
      include Employee::SelectLists::EmployeesListHelper
      attr_reader :survey, :export_csv, :page, :filters, :results_per_page, :report_type,
                  :ids_only, :account

      def initialize(params)
        @params = params
        @account = Account.find_by(id: params[:account_id])
        @survey = Schedule.where(account_id: params[:account_id], id: params[:schedule_survey_id]).first
        @page = params[:page]&.to_i&.positive? ? params[:page].to_i : 1
        @filters = params[:filters].present? ? JSON.parse(params[:filters]).deep_symbolize_keys : {}
        @results_per_page = results_per_page.to_i
        @report_type = params[:report_type]
        @results_per_page = params[:results_per_page] || 8
        @search = params[:search]
        @groups = params[:groups]
        @limit = params[:limit].to_i.positive? ? params[:limit].to_i : 10
        @ids_only = params[:ids_only].present?
        @selected_employee_ids = parse_selected_employees
      end

      def call
        case report_type
        when 'normal'
          generate_normal_report
        when 'csv'
          generate_csv_report
        when 'list_all'
          # Fetching all_participants from employee chats
          handle_list_all_report
        when 'all_respondents'
          # Fetching all_respondents from survey objects
          generate_all_respondents
        when 'list_non_respondents'
          # Fetching all employees who are not added to the survey.
          handle_non_respondents_report
        end
      end

      private

      def employee_chats
        @_employee_chats ||= employee_chats_query(survey)
      end

      def statuses
        @_statuses ||= find_statuses(employee_chats)
      end

      def paginated_employee_chats(chats)
        offset_value = (page - 1) * results_per_page
        chats.offset(offset_value).limit(results_per_page)
      end

      def handle_list_all_report
        if ids_only
          employee_ids = get_not_completed_participants
          build_filtered_employee_ids(employee_ids)
        else
          generate_normal_report(false)
        end
      end

      def handle_non_respondents_report
        if ids_only
          employee_ids = get_non_respondents_ids
          build_filtered_employee_ids(employee_ids)
        else
          get_non_respondents
        end
      end

      def build_filtered_employee_ids(employee_ids)
        @employees = build_base_query(employee_ids)
        apply_search
        prioritize_selected_employees

        return employees.pluck(:id) if groups.blank?

        build_grouped_employee_ids
      end

      def get_not_completed_participants
        employee_chats.map do |chat|
          next unless chat.employee_id

          if survey.identified_survey?
            status = statuses[chat.id]
            chat.employee_id if status != 'COMPLETED'
          else
            chat.employee_id
          end
        end.compact
      end

      def generate_normal_report(apply_filters=true)
        output = { participants: [], total_participants: 0, total_unfiltered_participants: 0 }
        chats = if apply_filters
                  filtered_chats = get_filtered_employee_chats(survey, employee_chats, filters, statuses)
                  output[:total_participants] = filtered_chats.count
                  output[:total_unfiltered_participants] = employee_chats.count
                  paginated_employee_chats(filtered_chats)
                else
                  output[:total_participants] = employee_chats.count
                  employee_chats
                end

        chats.each do |chat|
          # If survey is anonymous then don't fetch status or questions_responded 
          if survey.identified_survey?
            ques_count = chat.questions.count
            resp_count = chat.responses.select('DISTINCT question_id').count
            status = statuses[chat.id]
            questions_responded = get_participant_questions_responded(ques_count, resp_count)
          end
          output[:participants] << serialize_survey_participant(chat.employee, status, questions_responded)
        end
        output
      end

      def serialize_survey_participant(employee, status=nil, questions_responded=nil)
        ActiveModelSerializers::SerializableResource.new(
          employee,
          serializer: SurveyParticipantSerializer,
          scope: {
            status: status,
            questions_responded: questions_responded 
          }
        )
      end

      def get_participant_questions_responded(question_count, response_count)
        return "0" if response_count.zero?
        percentage_of_questions_answered = ((response_count.to_f / question_count) * 100).round(0)

        "#{percentage_of_questions_answered}%(#{response_count})"
      end

      def generate_csv_report
        CSV.generate(headers: true) do |csv|
          headers = ['Name', 'Email', 'Manager', 'Department']
          headers << ['Status', 'Questions Responded'] if survey.identified_survey?
          chats = employee_chats
          csv << headers.flatten

          chats.each do |chat|
            participant = chat.employee
            name = participant.full_name
            email = participant.user.email
            manager = participant.manager&.full_name
            department = participant.department&.name
            if survey.identified_survey?
              ques_count = chat.questions.count
              resp_count = chat.responses.select('DISTINCT question_id').count
              status = chat.progress_status
              questions_responded = get_participant_questions_responded(ques_count, resp_count)
            end
            columns = [name, email, manager, department, status, questions_responded]
            csv << columns
          end
        end
      end

      def generate_all_respondents
        output = { participants: [] }

        if ids_only
          output[:participants] = survey.respondents.ids
        else
          survey.respondents.each do |respondent|
            output[:participants] << serialize_survey_participant(respondent)
          end
        end
        output
      end

      def get_non_respondents
        # These are all the employees who are not added to the survey
        output = { non_participants: [] }
        respondent_ids = survey.respondents.pluck(:id)
        non_respondents = Employee.includes(:user).where(
          account_id: params[:account_id], billing_status: 'active', date_of_exit: nil
        ).where.not(id: respondent_ids)
        non_respondents.each do |non_respondent|
          output[:non_participants] << { id: non_respondent.id, full_name: non_respondent.full_name, email: non_respondent.user.email }
        end
        output
      end

      def get_non_respondents_ids
        non_respondents_query.ids
      end

      def non_respondents_query
        respondent_ids = survey.respondents.pluck(:id)
        Employee.where(account_id: params[:account_id], billing_status: 'active', date_of_exit: nil)
          .where.not(id: respondent_ids)
      end

      def build_base_query(employee_ids)
        query = account.employees.active.where(id: employee_ids).joins(:user)
        query = query.left_joins(:department) if include_department?
        query
      end
    end
  end
end
