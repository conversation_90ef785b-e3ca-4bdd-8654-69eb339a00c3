# PerformanceReview::CreateReviewCyclePhases.call

module PerformanceReview
  class CreateReviewCyclePhases < ApplicationService
    include ActionView::Helpers::DateHelper
    attr_reader :review_cycle, :add_competency
    attr_accessor :default_phases, :cycle_custom_phases, :phases

    def initialize(review_cycle_id, add_competency = false)
      @review_cycle = ReviewCycle.find(review_cycle_id)
      @default_phases = []
      @cycle_custom_phases = []
      @phases = []
      @add_competency = add_competency
      @custom_reviewers_count = 0
    end

    def call
      return if review_cycle.blank?

      fetch_default_phases
      custom_phases
      @cycle_custom_phases = [] if @cycle_custom_phases.nil?
      @default_phases = [] if @default_phases.nil?
      @phases = @cycle_custom_phases | @default_phases
      return if phases.blank?

      create_phase
    end

    private

    def create_phase
      # create review phases, use the postion to set the order
      (0..8).to_a.sort.each do |position|
        position_range = (position...(position + 1)).step(0.1).to_a

        selected_phases = phases.select { |phase_data| position_range.include?(phase_data[:position].to_f) }
        next if selected_phases.blank?

        selected_phases.each do |selected_phase|
          phase_attributes = {
            review_cycle_id: review_cycle.id,
            position: selected_phase[:position],
          }

          next if [:competency_approval, :competency_selection].include?(selected_phase[:phase_type]) && !add_competency

          review_cycle_phase = review_cycle.review_cycle_phases.with_discarded.find_or_create_by!(
            phase_type: selected_phase[:phase_type],
            review_cycle: review_cycle,
            reviewer_type_id: selected_phase[:reviewer_type_id] || nil,
          )

          if selected_phase[:add] == true
            review_cycle_phase.undiscard! if review_cycle_phase.discarded?
            review_cycle_phase.update(phase_attributes)
            review_cycle_phase.update(name: selected_phase[:name])
            update_reviewee_status(selected_phase[:name].strip)
            update_phase_start_time_for_system_reviewer_types
          end
          next unless selected_phase[:add] == false && !review_cycle_phase.discarded?

          review_cycle_phase.update(name: selected_phase[:name])
          update_reviewee_status(selected_phase[:name].strip)
          review_cycle_phase.discard
        end
      end
    end

    def update_phase_start_time_for_system_reviewer_types
      system_phases = review_cycle.review_cycle_phases.where(reviewer_type_id: review_cycle.reviewer_types.system)
      if system_phases.present?
        system_phases.each do |phase|
          # get parent reviewer type from the reviewer type, by removing the former prifix at its start
          parent_reviewer_type = phase.reviewer_type.reviewer_type.sub('previous_', '')
          parent_phase = review_cycle.review_cycle_phases.find_by(phase_type: phase.phase_type,
                                                                  reviewer_type: parent_reviewer_type) ||
            review_cycle.review_cycle_phases.find_by(phase_type: phase.phase_type, reviewer_type_id: nil)
          phase.update(start_time: parent_phase.start_time) if parent_phase.present?
        end
      end
    end

    def update_reviewee_status(name)
      @review_cycle.reviewees.update(goal_selection_done: true) if name == 'Goal Selection and Approval'
    end

    def custom_phases
      # contruct phases that can change depending on the review cycle
      @cycle_custom_phases = ['Goal Approval', 'Goal Selection',
                              'Peer Approval', 'Peer Selection',
                              'Competency Selection', 'Competency Approval'].each_with_object([]) do |phase_name, store|
        case phase_name
        when 'Goal Approval'
          store << if @review_cycle.reviewer_types.exists?(goal_approval_required: true)
                     name = if @review_cycle.reviewer_types.exists?(goal_approval_required: true, reviewer_type: 'self')
                              'Goal Approval'
                            elsif @review_cycle.reviewer_types.exists?(goal_approval_required: true,
                                                                       reviewer_type: 'manager', define_goals: true)
                              'Goal Selection and Approval'

                            end

                     {
                       name: name,
                       phase_type: :goal_approval,
                       position: 1,
                       add: true,
                     }
                   else
                     {
                       name: phase_name,
                       phase_type: :goal_approval,
                       position: 1,
                       add: false,
                     }
                   end
        when 'Competency Approval'
          store << if @review_cycle.reviewer_types.where(reviewer_type: ReviewerType::SINGLE_REVIEWER_TYPES).select(&:has_competencies).present?
                     name = if @review_cycle.reviewer_types.where(reviewer_type: ReviewerType::SINGLE_REVIEWER_TYPES).select(&:has_competencies).present?
                              'Competency Approval'
                              # elsif @review_cycle.reviewer_types.find_by(reviewer_type: 'manager').has_competencies?
                              #   'Competency Selection and Approval'

                            end

                     {
                       name: name,
                       phase_type: :competency_approval,
                       position: 1,
                       add: true,
                     }
                   else
                     {
                       name: phase_name,
                       phase_type: :competency_approval,
                       position: 1,
                       add: false,
                     }
                   end
        when 'Goal Selection'
          store << if @review_cycle.reviewer_types.exists?(define_goals: true, reviewer_type: 'self')
                     {
                       name: phase_name,
                       phase_type: :goal_selection,
                       position: 0,
                       add: true,

                     }
                   else
                     {
                       name: phase_name,
                       phase_type: :goal_selection,
                       position: 0,
                       add: false,

                     }

                   end
        when 'Competency Selection'
          store << if @review_cycle.reviewer_types.where(reviewer_type: ReviewerType::SINGLE_REVIEWER_TYPES).select(&:has_competencies).present?
                     {
                       name: phase_name,
                       phase_type: :competency_selection,
                       position: 0,
                       add: true,

                     }
                   else
                     {
                       name: phase_name,
                       phase_type: :competency_selection,
                       position: 0,
                       add: false,

                     }

                   end
        when 'Peer Approval'
          store << if @review_cycle.reviewer_types.exists?(approval_required: true)
                     {
                       name: phase_name,
                       phase_type: :peer_approval,
                       position: 3,
                       add: true,

                     }
                   else
                     {
                       name: phase_name,
                       phase_type: :peer_approval,
                       position: 3,
                       add: false,

                     }
                   end

        when 'Peer Selection'
          store << if @review_cycle.reviewer_types.exists?(reviewer_type: 'peer') ||
              @review_cycle.peer_config&.selection_required
                     {
                       name: phase_name,
                       phase_type: :peer_selection,
                       position: 2,
                       add: true,

                     }
                   else
                     {
                       name: phase_name,
                       phase_type: :peer_selection,
                       position: 2,
                       add: false,

                     }
                   end

        end
      end
    end

    def fetch_default_phases
      # construct defualt phases for any review cycle
      phases_names = ['Write Reviews', 'Manager Summary', 'Release Reviews']

      @default_phases = phases_names.each_with_object([]) do |phase_name, store|
        case phase_name
        when 'Write Reviews'
          reviewer_types = @review_cycle.reviewer_types.where.not(reviewer_type: 'manager')
          next unless reviewer_types.exists?

          if reviewer_types.exists?(standalone_launch: true)
            reviewer_types.where(standalone_launch: true).each do |reviewer_type|
              store << {
                name: "#{reviewer_type.first_person} Reviews".titleize,
                phase_type: :write_reviews,
                position: write_reviews_phase_position(reviewer_type),
                add: true,
                reviewer_type_id: reviewer_type.id,
              }
            end
          end

          standalone_phases = review_cycle.review_cycle_phases.where(phase_type: 'write_reviews').where.not(reviewer_type_id: nil)
          standalone_phases.pluck(:reviewer_type_id).each do |reviewer_type_id|
            reviewer_type = review_cycle.reviewer_types.find_by(id: reviewer_type_id)
            next if reviewer_type.present?
            next if reviewer_type&.standalone_launch

            store << {
              name: "#{reviewer_type&.first_person} Reviews".titleize,
              phase_type: :write_reviews,
              position: 4,
              add: false,
              reviewer_type_id: reviewer_type_id,
            }
          end

          store << if reviewer_types.exists?(standalone_launch: false)
                     {
                       name: phase_name,
                       phase_type: :write_reviews,
                       position: 4,
                       add: true,
                       reviewer_type_id: nil,
                     }
                   else
                     {
                       name: phase_name,
                       phase_type: :write_reviews,
                       position: 4,
                       add: false,
                       reviewer_type_id: nil,
                     }
                   end
        when 'Manager Summary'
          store << {
            name: phase_name,
            phase_type: :manager_summary,
            position: 5,
            add: @review_cycle.reviewer_types.exists?(reviewer_type: 'manager'),
          }
        when 'Release Reviews'
          store << {
            name: phase_name,
            phase_type: :release_reviews,
            position: 7,
            add: true,
          }
        end
      end
    end

    # 4.0 - write reviews
    # 4.1 - self review
    # 4.2 - peer review
    # 4.3 - direct review
    # custom review starts from 6
    def write_reviews_phase_position(reviewer_type)
      case reviewer_type.reviewer_type
      when 'self'
        4.1
      when 'peer'
        4.2
      when 'direct_report'
        4.3
      else
        custom_reviewer_position
      end
    end

    def custom_reviewer_position
      position = 6 + (@custom_reviewers_count / 10.to_f)
      @custom_reviewers_count += 1
      # rounding is done to tackle floating point precision issue
      position.round(1)
    end
  end
end
