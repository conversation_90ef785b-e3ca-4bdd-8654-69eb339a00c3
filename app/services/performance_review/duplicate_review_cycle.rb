# frozen_string_literal: true

# PerformanceReview::DuplicateReviewCycle.call
# FIXME: Use deepclone
module PerformanceReview
  class DuplicateReviewCycle < ApplicationService
    attr_reader :source_review_cycle, :account, :target_review_cycle, :title

    def initialize(source_review_cycle, title)
      @source_review_cycle = source_review_cycle
      @account = @source_review_cycle.account
      @target_review_cycle = target_review_cycle
      @title = title
    end

    def call
      create_review_cycle
      create_goal_cycles
      create_reviewer_types
      create_user_roles_and_peer_config if @source_review_cycle.peer_config.present?
      create_reviewees
      create_reviewers
      create_phases
      create_review_copy_messages unless @target_review_cycle.review_cycle_phases.count.zero?

      @target_review_cycle
    end

    private

    def create_review_cycle
      @target_review_cycle                 = @source_review_cycle.dup
      @target_review_cycle.title           = title
      @target_review_cycle.status          = 'draft'
      @target_review_cycle.duplicated_from = @source_review_cycle.id
      @target_review_cycle.default_channel = nil
      @target_review_cycle.automation_enabled = false
      @target_review_cycle.start_date      = nil
      @target_review_cycle.end_date        = nil
      @target_review_cycle.last_synced_at  = nil
      @target_review_cycle.gsheet_info     = nil
      @target_review_cycle.save!
      @source_review_cycle.pdf_configurations.each do |pdf_configuration|
        target_pdf_configuration = pdf_configuration.dup
        target_pdf_configuration.configurable = @target_review_cycle
        target_pdf_configuration.save!
      end
    end

    def create_user_roles_and_peer_config
      # for now we only use the default role - self and manager
      new_peer_config = @source_review_cycle.peer_config.dup
      new_peer_config.peer_selection_role = nil
      new_peer_config.peer_approval_role = nil
      new_peer_config.review_cycle_id = @target_review_cycle.id
      if @source_review_cycle.peer_config.peer_selection_role.present?
        # if the source has selection role,link new review to the same selection role

        new_peer_selection_role = @target_review_cycle.review_cycle_user_roles.with_discarded.find_by(
          role_type: @source_review_cycle.peer_config.peer_selection_role.role_type,
        )
        new_peer_selection_role.undiscard! if new_peer_selection_role.discarded?
        new_peer_config.peer_selection_role = new_peer_selection_role
      end

      if @source_review_cycle.peer_config.peer_approval_role.present?
        # if the source has approval role,link new review to the same approval role

        new_peer_approval_role = @target_review_cycle.review_cycle_user_roles.with_discarded.find_by(
          role_type: @source_review_cycle.peer_config.peer_approval_role.role_type,
        )
        new_peer_approval_role.undiscard! if new_peer_approval_role.discarded?
        new_peer_config.peer_approval_role = new_peer_approval_role
      end
      new_peer_config.save!
    end

    # below function commented because we calling seperate service class from controller
    def create_phases
      PerformanceReview::CreateReviewCyclePhases.call(@target_review_cycle.id)
    end

    def create_goal_cycles
      @source_review_cycle.review_cycle_goal_cycles.each do |goal_cycle|
        target_goal_cycle = goal_cycle.dup
        target_goal_cycle.review_cycle = @target_review_cycle
        target_goal_cycle.save!
      end
    end

    def create_reviewer_types
      @source_review_cycle.reviewer_types.normal.includes([:review_cycle_templates]).each do |source_reviewer_type|
        target_reviewer_type = source_reviewer_type.dup
        target_reviewer_type.review_cycle = @target_review_cycle
        target_reviewer_type.save!
        create_review_cycle_template(source_reviewer_type, target_reviewer_type)
      end
    end

    def create_reviewees
      @source_review_cycle.reviewees.each do |reviewee|
        next unless reviewee.employee.active?

        target_reviewee = reviewee.dup
        target_reviewee.review_cycle = @target_review_cycle
        target_reviewee.self_review_done = false
        target_reviewee.peer_selection_done = false
        target_reviewee.peer_approval_done = false
        target_reviewee.review_writing_percent = 0.0
        target_reviewee.manager_summary_done = false
        target_reviewee.release_review_done = false
        target_reviewee.goal_approval_done = false
        target_reviewee.goal_selection_done = false
        target_reviewee.competency_selection_done = false
        target_reviewee.competency_approval_done = false
        target_reviewee.one_on_one_id = nil
        target_reviewee.custom_variables = nil
        Reviewee.import [target_reviewee]
      end
    end

    def create_reviewers
      @source_review_cycle.reviewers.each do |reviewer|
        next if reviewer.reviewer_type.reviewer_type == 'peer'
        next if reviewer.reviewer_type.system?
        next unless reviewer.employee.active?
        next if reviewer.reviewee.nil?
        next unless reviewer.reviewee.employee.active?
        next if @target_review_cycle.reviewees.find_by(employee: reviewer.reviewee.employee).blank?

        target_reviewer = reviewer.dup
        target_reviewer.review_cycle = @target_review_cycle
        target_reviewer.review_submitted = false
        target_reviewer.reviewer_type = @target_review_cycle.reviewer_types.find_by(reviewer_type: reviewer&.reviewer_type&.reviewer_type)
        target_reviewer.reviewee = @target_review_cycle.reviewees.find_by(employee: reviewer.reviewee.employee)
        target_reviewer.save!
      end
    end

    def create_review_cycle_template(source_reviewer_type, target_reviewer_type)
      source_reviewer_type.review_cycle_templates.each do |source_template|
        target_template = source_template.dup
        target_template.reviewer_type = target_reviewer_type
        target_template.save!
        create_review_cycle_template_questions(source_template, target_template)
      end
    end

    def create_review_cycle_template_questions(source_template, target_template)
      source_template.review_cycle_template_questions.includes([:question]).each do |source_block|
        next if source_block.block_type == 'calculated_question'

        target_block = source_block.dup
        target_block.review_cycle_template = target_template

        if %w[question competency].include?(target_block.block_type) && target_block.question.present?
          source_question = source_block.question
          target_question = source_question.dup
          target_question.serviceable = target_review_cycle
          source_question.options.each do |source_option|
            target_option = source_option.dup
            target_question.options << target_option
          end
          target_block.question = target_question
        end

        target_block.save!
      end
    end

    def create_review_copy_messages
      PerformanceReview::DuplicateReviewCopyMessage.call(@source_review_cycle.id, @target_review_cycle.id)
    end
  end
end
