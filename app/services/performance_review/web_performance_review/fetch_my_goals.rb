# {
#   data: {
#     1: {
#       parent: null,
#     },
#     2: {
#       parent: null,
#     },
#     3: {
#       parent: null,
#     },
#     4: {
#       parent: null,
#     },
#     11: {
#       parent: 1,
#     },
#     12: {
#       parent: 1,
#     },
#     21: {
#       parent: 2,
#     },
#     22: {
#       parent: 2,
#     },
#     31: {
#       parent: 3,
#     },
#     41: {
#       parent: 4,
#     },
#     42: {
#       parent: 4,
#     },
#     43: {
#       parent: 4,
#     },
#     221: {
#       parent: 22,
#     },
#     222: {
#       parent: 22,
#     },
#   },
#   main: [1, 2, 3, 4],
#   children: {
#     1: [11, 12],
#     2: [21, 22],
#     3: [31],
#     4: [41, 42, 43],
#     22: [221, 222],
#   },
# };
module PerformanceReview
  module WebPerformanceReview
    class FetchMyGoals < ApplicationService
      include RevieweeGoalConcern

      attr_reader :review_cycle, :reviewee, :goal_cycle_ids, :employee, :action_type, :manager_id,
                  :manager_is_admin, :goal_config
      attr_accessor :data

      def initialize(review_cycle_id, reviewee_id, action_type = nil)
        @review_cycle = ReviewCycle.find_by(id: review_cycle_id)
        @reviewee = review_cycle.reviewees.find_by(id: reviewee_id)
        @goal_config = review_cycle.goal_config_json
        @manager_id = @reviewee.manager_id
        @manager_is_admin = @reviewee.manager.admin?
        @goal_cycle_ids = @review_cycle.goal_cycles.pluck(:id)
        @employee = reviewee.employee
        @action_type = action_type
        @data = set_default_data
      end

      def call
        return data if @review_cycle.nil? || @reviewee.nil?

        process_main_goals

        data
      end

      private

      def set_default_data
        {
          data: {},
          main: [],
          children: {},
          goal_configs: {
            minimum_goal_weightage: goal_config&.minimum_goal_weightage || ReviewCycle::DEFAULT_GOAL_CONFIG[:minimum_goal_weightage],
            maximum_goal_weightage: goal_config&.maximum_goal_weightage || ReviewCycle::DEFAULT_GOAL_CONFIG[:maximum_goal_weightage],
            allow_zero_weightage_goals: goal_config.allow_zero_weightage_goals,
          },
          previous_manager_actions: reviewee.previous_manager_actions,
        }
      end

      def process_main_goals
        goals.each do |parent_goal|
          next if parent_goal.nil?

          parent_goal_id = parent_goal.id
          is_parent_visible_to_manager = add_visibility_flag(parent_goal, manager_id, review_cycle)

          if action_type == 'create' || (action_type == 'approve' && is_parent_visible_to_manager)
            data[:data][parent_goal_id.to_s] = build_parent_data(parent_goal, is_parent_visible_to_manager)
            data[:main] << parent_goal_id
          end

          process_goal_hirarchy(parent_goal, is_parent_visible_to_manager) if children_goal_exists?(parent_goal)
        end
      end

      def process_goal_hirarchy(parent_goal, is_parent_visible_to_manager)
        children = (goal_key_results_and_projects(parent_goal) + goal_aligned_goals(parent_goal)).flatten.uniq

        children.each do |child_goal|
          reviewee_is_owner = child_goal.employee_goals.exists?(employee_id: employee.id)
          is_child_visible_to_manager = add_visibility_flag(child_goal, manager_id, review_cycle)

          if reviewee_is_owner
            case action_type
            when 'create'
              data[:data][child_goal.id] = build_child_data(
                parent_goal.id, child_goal, is_child_visible_to_manager, is_parent_visible_to_manager
              )
              add_children(parent_goal.id, child_goal.id)
            when 'approve'
              if is_child_visible_to_manager
                data[:data][child_goal.id.to_s] = build_child_data(
                  parent_goal.id, child_goal, is_child_visible_to_manager, is_parent_visible_to_manager
                )
                if is_parent_visible_to_manager
                  add_children(parent_goal.id, child_goal.id)
                else
                  data[:main] << child_goal.id
                end
              end
            end
          end

          process_goal_hirarchy(child_goal, is_child_visible_to_manager) if children_goal_exists?(child_goal)
        end
      end

      def goal_key_results_and_projects(goal)
        goal.child_key_results_and_projects.joins(:employee_goals).where(
          employee_goals: { employee_id: employee.id },
        ).where(goal_cycle_id: goal_cycle_ids).order(created_at: :asc)
      end

      def goal_aligned_goals(goal)
        goal.aligned_goals.joins(:employee_goals).where(
          employee_goals: { employee_id: employee.id },
        ).where(goal_cycle_id: goal_cycle_ids).order(created_at: :asc)
      end

      def children_goal_exists?(goal)
        goal.descendants.joins(:employee_goals)
          .where(employee_goals: { employee_id: employee.id })
          .exists?(goal_cycle_id: goal_cycle_ids)
      end

      def build_parent_data(parent_goal, is_parent_visible_to_manager = true)
        res = {
          parent: nil, id: parent_goal.id, title: parent_goal.title, description: parent_goal.description,
          is_visible_to_manager: is_parent_visible_to_manager
        }
      end

      def build_child_data(parent_goal_id, child_goal, is_child_visible_to_manager = true, is_parent_visible_to_manager = true)
        res = {
          parent: action_type == 'approve' && !is_parent_visible_to_manager ? nil : parent_goal_id,
          id: child_goal.id, title: child_goal.title, description: child_goal.description,
          is_visible_to_manager: is_child_visible_to_manager
        }
      end

      def add_children(parent_goal_id, child_goal_id)
        if data[:children][parent_goal_id].present?
          data[:children][parent_goal_id] << child_goal_id unless data[:children][parent_goal_id].include?(child_goal_id)
        else
          data[:children][parent_goal_id] = [child_goal_id]
        end
      end

      def goals
        @_goals ||= begin
          top_level_objectives_only = my_goals.dig(:goal_children)&.keys || []
          Goal.where(id: top_level_objectives_only)
            .joins(:employee_goals)
            .where(employee_goals: { employee_id: employee.id })
            .where(goal_cycle_id: goal_cycle_ids).order(created_at: :asc)
        end
      end

      def my_goals
        @_my_goals ||= CoreOkr::MyGoals::Index.call(review_cycle.account, employee,
                                                    build_goal_params.with_indifferent_access, 1000).as_json.with_indifferent_access
      end

      def build_goal_params
        {
          goal_types: ['individual'],
          cycles: goal_cycle_ids,
          view_type: false,
          owners: [employee],
          account_id: employee.account_id,
          slack_payload: true,
        }.with_indifferent_access
      end
    end
  end
end
