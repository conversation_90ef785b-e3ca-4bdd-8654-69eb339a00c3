# PerformanceReview::WebPerformanceReview::CreateReviewerTypes
module PerformanceReview
  module WebPerformanceReview
    class CreateReviewerTypes < ApplicationService
      prepend SimpleCommand
      attr_reader :review_cycle, :reviewer_types

      include ::PerformanceReview::Utils
      def initialize(review_cycle_id, reviewer_types_params)
        @review_cycle = ReviewCycle.find_by(id: review_cycle_id)
        @reviewer_types = reviewer_types_params
      end

      def call
        return if review_cycle.nil?

        verify = verify_attributes

        return if verify.nil?

        if review_cycle.status != 'live'
          process_self_review_types
          process_peer_review_types
          process_direct_review_types
          process_manager_summary
          process_custom_reviewer_types
          process_competency_config
          process_goal_config
        end

        process_auto_create_one_on_one
        process_enforce_manager_change
        process_release_reviews
        process_review_visibility if review_cycle.status == 'live'
        process_review_phases if review_cycle.status != 'live'
        process_three_sixty_forms if review_cycle.three_sixty_review? && review_cycle.status == 'draft'
        process_calibration_config
        process_tracking
      end

      private

      def process_tracking
        properties = review_cycle.reviewer_types.each_with_object([]) do |type, store|
          # for now we are tracking peer review flow only
          next unless type.reviewer_type == 'peer'

          if review_cycle.peer_config&.selection_required
            selection_label = review_cycle.peer_selection_role == 'self' ? 'Peers Chosen by Reviewees' : 'Peers Assigned by Manager'
            store << { peer_selection_flow: selection_label }
          end

          if type.approval_required || review_cycle.peer_config&.approval_required
            store << { peer_approval_flow: 'Peer Approval by Manager' }
          end
          store << { account_id: review_cycle.account_id, review_cycle_id: review_cycle.id }
        end

        Ahoy::Event.delay(queue: 'low').create!(
          name: 'selected_peer_flow',
          properties: properties,
          user_id: review_cycle.creator.user.id, time: Time.zone.now
        )
      end

      def process_review_phases
        PerformanceReview::CreateReviewCyclePhases.call(review_cycle.id) if review_cycle.present?
      end

      def verify_attributes
        return true if valid_attributes?

        errors.add :params, 'The right attributes were not passed for processing, Try again'
        nil
      end

      def valid_attributes?
        !missing_attributes?
      end

      def missing_attributes?
        missing_attributes = [
          :write_manager_summary_review,
          :write_direct_report_review,
          :goals_selection_by_reviewee,
          :goals_approval_by_manager,
          :write_self_review,
          :peer_review,
          :write_peer_review,
          :goal_in_self_review,
          :goal_in_manager_review,
          :goals_selection_and_approval_by_manager,
          :auto_create_one_on_one,
          :review_visibility,
          :define_goal_weights,
          :release_reviews,
          :custom_reviewer_types,
          :goal_in_custom_review,
          :competency_configs,
          :goal_configs,
          :enforce_manager_change,
        ]

        missing_attributes.any? { |attribute| !reviewer_types.key?(attribute) }
      end

      def process_release_reviews
        release_reviews = if reviewer_types[:release_reviews]['enabled']
                            'release_summary'
                          else
                            'choose_peers'
                          end
        @review_cycle.update(review_cycle_phase: release_reviews) if release_reviews.present?
      end

      def process_competency_config
        review_cycle_competency_attibute = {
          is_enabled: reviewer_types[:competency_configs]['enabled'],
          is_weights_enabled: reviewer_types[:competency_configs]['is_weights_enabled'],
        }
        review_cycle.update(competency_config_json:
          ReviewCycleConfig::CompetencyConfig.new(review_cycle_competency_attibute))
      end

      def fetch_goal_config_value(key)
        reviewer_types[:goal_configs][key]
      end

      def process_goal_config
        goal_config_keys = %w[
          goal_visibility
          review_cycle_goal_type
          minimum_goal_weightage
          maximum_goal_weightage
          allow_zero_weightage_goals
        ]

        review_cycle_goal_attribute = {}
        goal_config_keys.each do |key|
          review_cycle_goal_attribute[key.to_sym] = fetch_goal_config_value(key)
        end

        review_cycle.update(goal_config_json:
          ReviewCycleConfig::GoalConfig.new(review_cycle_goal_attribute))

        case review_cycle.reload.goal_config_json.review_cycle_goal_type
        when 'goals_with_key_results_only'
          review_cycle.update(show_key_results: true)
        when 'goals_with_objectives_only', 'goals_with_objectives_and_key_results'
          review_cycle.update(show_key_results: false)
        end
      end

      def process_review_visibility
        manager_visibility = reviewer_types[:review_visibility]['manager_review']['enabled']
        direct_report_visibility = reviewer_types[:review_visibility]['direct_report_review']['enabled']
        self_visibility = reviewer_types[:review_visibility]['self_review']['enabled']
        peer_visibility = reviewer_types[:review_visibility]['peer_review']['enabled']
        custom_reviewer_visibility_list = reviewer_types[:review_visibility]['custom_review']

        if review_cycle.reviewer_types.exists?(reviewer_type: 'self')
          review_cycle.reviewer_types.find_by(reviewer_type: 'self').update(include_in_release_review: self_visibility == true ? 1 : 0)
        end

        if review_cycle.reviewer_types.exists?(reviewer_type: 'manager')
          review_cycle.reviewer_types.find_by(reviewer_type: 'manager').update(include_in_release_review: manager_visibility == true ? 1 : 0)
        end

        if review_cycle.reviewer_types.exists?(reviewer_type: 'peer')
          review_cycle.reviewer_types.find_by(reviewer_type: 'peer').update(include_in_release_review: peer_visibility == true ? 1 : 0)
        end

        if review_cycle.reviewer_types.exists?(reviewer_type: 'direct_report')
          review_cycle.reviewer_types.find_by(reviewer_type: 'direct_report').update(include_in_release_review: direct_report_visibility == true ? 1 : 0)
        end

        custom_rt_enable_ids = custom_reviewer_visibility_list.select { |cr| cr['enabled'] }.pluck('reviewer_type_id')
        custom_rt_disable_ids = custom_reviewer_visibility_list.reject { |cr| cr['enabled'] }.pluck('reviewer_type_id')
        custom_rt_query = lambda do |ids|
          review_cycle.reviewer_types.normal.where(id: ids).where.not(
            reviewer_type: ReviewerType::DEFAULT_REVIEWER_TYPES,
          )
        end
        custom_rt_query.call(custom_rt_enable_ids).update(include_in_release_review: 1)
        custom_rt_query.call(custom_rt_disable_ids).update(include_in_release_review: 0)
      end

      def process_auto_create_one_on_one
        auto_create_one_on_one = reviewer_types[:auto_create_one_on_one]['enabled']
        @review_cycle.update(auto_create_one_on_one: auto_create_one_on_one) if auto_create_one_on_one.present?
      end

      def process_enforce_manager_change
        enforce_manager_change = reviewer_types[:enforce_manager_change]['enabled']
        end_date = reviewer_types[:enforce_manager_change]['end_date']
        if enforce_manager_change && end_date.present?
          parse_time = ActiveSupport::TimeZone[review_cycle.account.timezone].parse(reviewer_types[:enforce_manager_change]['end_date']).end_of_day
          @review_cycle.update(enforce_system_manager_mapping: parse_time)
        else
          @review_cycle.update(enforce_system_manager_mapping: nil)
        end
      end

      def process_manager_summary
        manager_summary = reviewer_types[:write_manager_summary_review]['enabled']
        goal_in_manager_review = reviewer_types[:goal_in_manager_review]['enabled']
        goals_selection_and_approval_by_manager = reviewer_types[:goals_selection_and_approval_by_manager]['enabled']
        manager_visibility = reviewer_types[:review_visibility]['manager_review']['enabled']
        define_goal_weights = reviewer_types[:define_goal_weights]['enabled']

        if manager_summary || goal_in_manager_review || goals_selection_and_approval_by_manager
          reviewer_type = @review_cycle.reviewer_types.unscoped.find_or_create_by!(reviewer_type: 'manager',
                                                                                   review_cycle: @review_cycle)
          reviewer_type.undiscard! if reviewer_type.discarded?
          additional_params = {}
          additional_params = additional_params.merge(
            include_goals: goal_in_manager_review,
            can_read_reviews: true,
            enable_calibration_view: true,
            define_goals: goals_selection_and_approval_by_manager,
            goal_approval_required: goals_selection_and_approval_by_manager || check_self_review_goal_status,
            define_goal_weights: define_goal_weights,
            include_in_release_review: manager_visibility == true ? 1 : 0,
          )

          if reviewer_type.present?
            additional_params = additional_params.merge(ReviewerType::REVIEWER_TYPE_META[reviewer_type.reviewer_type])
          end

          reviewer_type.update(additional_params) if reviewer_type.present?
        end

        return unless manager_summary == false

        @review_cycle.reviewer_types.where(reviewer_type: 'manager', review_cycle: @review_cycle).discard_all
      end

      def check_self_review_goal_status
        @review_cycle.reviewer_types.where(reviewer_type: 'self', goal_approval_required: true).present?
      end

      def process_direct_review_types
        direct_review = reviewer_types[:write_direct_report_review]['enabled']
        direct_report_visibility = reviewer_types[:review_visibility]['direct_report_review']['enabled']
        goal_in_direct_report_review = reviewer_types.dig(:goal_in_direct_report_review, 'enabled') || false
        limit_to_participants = reviewer_types[:write_direct_report_review]['limit_to_participants']

        if direct_review || goal_in_direct_report_review
          reviewer_type = @review_cycle.reviewer_types.unscoped.find_or_create_by!(reviewer_type: 'direct_report',
                                                                                   review_cycle: @review_cycle)
          reviewer_type.undiscard! if reviewer_type.discarded?
          additional_params = {
            include_in_release_review: direct_report_visibility == true ? 1 : 0,
            define_goals: false,
            define_goal_weights: false,
            goal_approval_required: false,
            include_goals: goal_in_direct_report_review,
            limit_to_participants: limit_to_participants,
            standalone_launch: reviewer_types['write_direct_report_review']['standalone_launch'] || false,
          }
          additional_params = additional_params.merge(ReviewerType::REVIEWER_TYPE_META[reviewer_type.reviewer_type]) if reviewer_type.present?
          reviewer_type.update(additional_params) if reviewer_type.present?
        end

        return unless direct_review == false

        @review_cycle.reviewer_types.where(reviewer_type: 'direct_report',
                                           review_cycle: @review_cycle).discard_all
      end

      def process_self_review_types
        goal_selection = reviewer_types[:goals_selection_by_reviewee]['enabled']
        goal_approval = reviewer_types[:goals_approval_by_manager]['enabled']
        write_self_review = reviewer_types[:write_self_review]['enabled']
        goal_in_self_review = reviewer_types[:goal_in_self_review]['enabled']
        self_visibility = reviewer_types[:review_visibility]['self_review']['enabled']
        define_goal_weights = reviewer_types[:define_goal_weights]['enabled']

        if goal_selection == true || goal_approval == true || write_self_review == true || goal_in_self_review
          reviewer_type = @review_cycle.reviewer_types.unscoped.find_or_create_by!(reviewer_type: 'self',
                                                                                   review_cycle: @review_cycle)
          reviewer_type.undiscard! if reviewer_type.discarded?
          additional_params = {}
          additional_params = additional_params.merge(
            {
              define_goals: goal_selection,
              define_goal_weights: define_goal_weights,
              goal_approval_required: goal_approval,
              include_goals: goal_in_self_review,
              include_in_release_review: self_visibility == true ? 1 : 0,
            },
          )
          additional_params = additional_params.merge(ReviewerType::REVIEWER_TYPE_META[reviewer_type.reviewer_type]) if reviewer_type.present?
          reviewer_type.update(additional_params) if reviewer_type.present?
        end
        return unless goal_selection == false && goal_approval == false && write_self_review == false && goal_in_self_review == false

        @review_cycle.reviewer_types.where(reviewer_type: 'self',
                                           review_cycle: @review_cycle).discard_all
      end

      def process_custom_reviewer_types
        delete_unused_custom_reviewer
        custom_reviewer_types = reviewer_types[:custom_reviewer_types]
        goal_in_custom_review_list = reviewer_types[:goal_in_custom_review]
        custom_visibility_list = reviewer_types[:review_visibility]['custom_review'] || []

        return if custom_reviewer_types.blank?

        custom_reviewer_types.each do |custom_reviewer_type|
          reviewer_type_value = parameterized_text(custom_reviewer_type['reviewer_label'])
          reviewer_label = remove_special_chars_and_numbers(custom_reviewer_type['reviewer_label'])
          reviewee_label = remove_special_chars_and_numbers(custom_reviewer_type['reviewee_label'])
          next if ReviewerType::DEFAULT_REVIEWER_TYPES.include?(reviewer_type_value)

          reviewer_type = if custom_reviewer_type['reviewer_type_id'].present?
                            @review_cycle.reviewer_types.normal.with_discarded.find(custom_reviewer_type['reviewer_type_id'])
                          else
                            @review_cycle.reviewer_types.normal.with_discarded.find_or_create_by!(reviewer_type: reviewer_type_value,
                                                                                                  review_cycle: @review_cycle)
                          end
          reviewer_type.undiscard! if reviewer_type.discarded?
          next if reviewer_type.blank?

          goal_in_custom_review = goal_in_custom_review_list.map do |itr|
            itr['enabled'] if itr['reviewer_type_id'] == reviewer_type.id
          end.compact.first || false
          custom_visibility = custom_visibility_list.map do |itr|
            itr['enabled'] if itr['reviewer_type_id'] == reviewer_type.id
          end.compact.first
          reviewer_type.update(
            {
              reviewer_type: reviewer_type_value,
              first_person: reviewer_label,
              second_person: reviewee_label,
            },
          )

          additional_params = {}
          additional_params = additional_params.merge(
            {
              define_goals: false,
              define_goal_weights: false,
              goal_approval_required: false,
              include_goals: goal_in_custom_review,
              include_in_release_review: custom_visibility == false ? 0 : 1,
              can_read_reviews: custom_reviewer_type['can_read_reviews'] || false,
              can_review_anyone: custom_reviewer_type['can_review_anyone'] || false,
              standalone_launch: custom_reviewer_type['standalone_launch'] || false,
            },
          )
          reviewer_type.update(additional_params)
        end
      end

      # The current logic here incorrectly attempts to subtract an array of integers from an array of ActiveRecord objects,
      # which results in Ruby's `-` operator failing to recognize equality (since object equality checks references, not values).
      #
      # For example:
      #   fetch_custom_reviewer_type.select(:id) returns an array of ReviewerType objects (e.g., [#<ReviewerType id: 6628>]).
      #   reviewer_types[:custom_reviewer_types].map { |rt| rt['reviewer_type_id'] } returns an array of integers (e.g., [6628]).
      #   When subtracting, Ruby compares object references, so no subtraction occurs, and all records remain.
      #
      # To fix this:
      #   - Convert fetch_custom_reviewer_type to an array of ids using `pluck(:id)`.
      #   - Then subtract the array of reviewer_type_ids already in use.
      #
      # The corrected logic:
      #   custom_rt_ids = fetch_custom_reviewer_type.pluck(:id)
      #   custom_rts_in_use = reviewer_types[:custom_reviewer_types].map { |obj| obj[:reviewer_type_id] }
      #   unused_custom_rt_ids = custom_rt_ids - custom_rts_in_use
      def delete_unused_custom_reviewer
        unused_custom_rt_ids =
          fetch_custom_reviewer_type.select(:id) - reviewer_types[:custom_reviewer_types].map do |rt|
                                                     rt['reviewer_type_id']
                                                   end

        unused_custom_reviewer_types = review_cycle.reviewer_types.where(id: unused_custom_rt_ids)
        standalone_unused_custom_rt_ids = unused_custom_reviewer_types.where(standalone_launch: true).ids
        review_cycle.review_cycle_phases.where(reviewer_type_id: standalone_unused_custom_rt_ids).discard_all
        unused_custom_reviewer_types.discard_all
      end

      def fetch_custom_reviewer_type
        review_cycle.reviewer_types.normal.where.not(reviewer_type: ReviewerType::DEFAULT_REVIEWER_TYPES)
      end

      # In case of three_sixty_review, we do not have employee_attributes,
      # hence, each reviewer type has only one template.
      # The reference template_id is same for all review_cycle_templates.
      def process_three_sixty_forms
        review_cycle_templates = review_cycle.reload.review_cycle_templates
        return if review_cycle_templates.blank?

        template_id = review_cycle_templates.where.not(template_id: nil).first&.template_id
        blank_reviewer_types = review_cycle.reviewer_types.left_joins(:review_cycle_templates).where(
          review_cycle_templates: { id: nil },
        )
        if blank_reviewer_types.present?
          blank_reviewer_types.each { |rt| rt.review_cycle_templates.find_or_create_by(template_id: template_id) }
        else
          review_cycle_templates.each do |review_cycle_template|
            next if review_cycle_template.template_id == template_id

            ReviewCycleQuestionVisibilities::DiscardQuestionVisibilityRecords.call('ReviewCycleTemplate',
                                                                                   review_cycle_template.id)
            review_cycle_template.update(template_id: template_id)
            ReviewCycleQuestionVisibilities::CreateQuestionVisibilityRecords.call('ReviewCycleTemplate',
                                                                                  review_cycle_template.id)
          end
        end
      end

      def process_calibration_config
        return if reviewer_types[:calibration_view].blank?

        reviewer_types[:calibration_view].except(:custom_reviewers).each do |reviewer_type, calibration_config|
          next if reviewer_type == 'self'

          next unless review_cycle.reviewer_types.exists?(reviewer_type: reviewer_type)

          review_cycle.reviewer_types.find_by(reviewer_type: reviewer_type).update(
            enable_calibration_view: calibration_config[:enable_calibration_view],
            enable_nine_box: calibration_config[:enable_nine_box],
          )
        end

        # Custom reviewers
        return if reviewer_types[:calibration_view][:custom_reviewers].blank?

        reviewer_types[:calibration_view][:custom_reviewers].each do |custom_reviewer|
          next if custom_reviewer[:reviewer_type_id].blank?

          reviewer_type = review_cycle.reviewer_types.find_by(reviewer_type_id: custom_reviewer[:reviewer_type_id])
          next if reviewer_type.blank?

          reviewer_type.update(
            enable_calibration_view: custom_reviewer[:enable_calibration_view],
            enable_nine_box: custom_reviewer[:enable_nine_box],
          )
        end
      end

      def process_peer_review_types
        peer_review = reviewer_types.dig(:peer_review) || {}
        write_peer_review = reviewer_types.dig(:write_peer_review) || {}
        review_visibility = reviewer_types.dig(:review_visibility, 'peer_review') || {}
        goal_in_peer_review = reviewer_types.dig(:goal_in_peer_review, 'enabled') || false

        is_peer_review_enabled = peer_review['enabled'] || false
        return disable_peer_reviews unless is_peer_review_enabled || goal_in_peer_review || write_peer_review['enabled']

        peer_config = PeerConfig.with_discarded.find_or_create_by!(review_cycle: review_cycle)
        peer_config.undiscard! if peer_config.discarded?
        @review_cycle.create_review_cycle_user_roles

        peer_config.update(
          selection_required: peer_review['selection_required'] || false,
          approval_required: peer_review['approval_required'] || false,
          peer_selection_role: find_user_role(peer_review['selection_role']),
          peer_approval_role: find_user_role(peer_review['approval_role']),
          min_reviewers: peer_review['min_reviewers'],
          max_reviewers: peer_review['max_reviewers'],
        )

        reviewer_type = @review_cycle.reviewer_types.with_discarded.find_or_create_by!(reviewer_type: 'peer')
        reviewer_type.update(build_reviewer_params(peer_review, write_peer_review, review_visibility,
                                                   goal_in_peer_review))
        reviewer_type.undiscard! if reviewer_type.discarded?
      end

      def find_user_role(role_type)
        @review_cycle.review_cycle_user_roles.find_by(role_type: role_type || 'admin')
      end

      def build_reviewer_params(peer_review, write_peer_review, review_visibility, goal_in_peer_review)
        {
          approval_required: peer_review['approval_required'] || false,
          min_reviewers: peer_review['min_reviewers'],
          max_reviewers: peer_review['max_reviewers'],
          include_in_release_review: review_visibility['enabled'] ? 1 : 0,
          define_goals: false,
          define_goal_weights: false,
          goal_approval_required: false,
          include_goals: goal_in_peer_review,
          limit_to_participants: peer_review['limit_to_participants'] || false,
          can_review_anyone: write_peer_review['can_review_anyone'] || false,
          standalone_launch: write_peer_review['standalone_launch'] || false,
        }.merge(ReviewerType::REVIEWER_TYPE_META['peer'] || {})
      end

      def disable_peer_reviews
        @review_cycle.reviewer_types.kept.where(reviewer_type: 'peer').discard_all
        @review_cycle.peer_config.discard if @review_cycle.peer_config.present?
      end
    end
  end
end
