module PerformanceReview
  module WebPerformanceReview
    class FetchRevieweeList < ApplicationService
      prepend SimpleCommand
      include RevieweeDataConcern

      attr_reader :current_user, :scoped_reviewees, :reviewee_scope, :params

      def initialize(current_user:, scoped_reviewees:, reviewee_scope:, params:)
        @current_user = current_user
        @scoped_reviewees = scoped_reviewees
        @reviewee_scope = reviewee_scope
        @reviewees = filter_reviewees_by_scope(reviewee_scope, scoped_reviewees)
        @params = params
      end

      def call
        return @reviewees if reviewee_scope == 'your_org'
        return serialize(@reviewees) if reviewee_scope.nil?

        apply_filters
        apply_ordering
        paginate_reviewees

        grouped_reviewees = group_reviewees
        grouped_reviewees = serialize(@reviewees) if grouped_reviewees.empty?

        build_response(grouped_reviewees)
      end

      private

      def apply_filters
        filter_by(:managers) { |ids| @reviewees = @reviewees.joins(:manager).where(manager_id: ids) }
        filter_by(:departments) do |ids|
          @reviewees = @reviewees.joins(employee: :department)
                                 .where(employees: { departments: { id: ids } })
        end
        filter_by_status(:goals, {
          'not_started' => -> { @reviewees.where(goal_selection_done: false, goal_approval_done: false) },
          'goal_selection' => -> { @reviewees.where(goal_selection_done: true, goal_approval_done: false) },
          'goal_approval' => -> { @reviewees.where(goal_approval_done: true) }
        })

        filter_by_status(:competency, {
          'not_started' => -> { @reviewees.where(competency_selection_done: false, competency_approval_done: false) },
          'competency_selection' => -> { @reviewees.where(competency_selection_done: true, competency_approval_done: false) },
          'competency_approval' => -> { @reviewees.where(competency_approval_done: true) }
        })

        filter_by_status(:peers, {
          'not_started' => -> { @reviewees.where(peer_selection_done: false, peer_approval_done: false) },
          'peer_selection' => -> { @reviewees.where(peer_selection_done: true, peer_approval_done: false) },
          'peer_approval' => -> { @reviewees.where(peer_approval_done: true) }
        })

        filter_by_status(:manager_summary, {
          'pending' => -> { @reviewees.where(manager_summary_done: false) },
          'manager_summary' => -> { @reviewees.where(manager_summary_done: true) }
        })

        filter_by_status(:self_review, {
          'pending' => -> { @reviewees.where(self_review_done: false) },
          'self' => -> { @reviewees.where(self_review_done: true) }
        })

        filter_by_status(:release_reviews, {
          'pending' => -> { @reviewees.where(release_review_done: false) },
          'release_reviews' => -> { @reviewees.where(release_review_done: true) }
        })

        if params[:search_text].present?
          @reviewees = @reviewees.joins(:employee).where('employees.full_name LIKE ?', "%#{params[:search_text]}%")
        end
      end

      def filter_by(key)
        return unless params[key].present?

        ids = JSON.parse(params[key])
        yield(ids)
      end

      def filter_by_status(key, conditions_map)
        return unless params[key].present?

        list = JSON.parse(params[key])
        list.each do |status|
          scope = conditions_map[status]
          @reviewees = scope.call if scope
        end
      end

      # Order in which reviewees will be rendered.
      def apply_ordering
        case params[:group_by]
        when 'manager'
          @reviewees = @reviewees
            .joins('LEFT JOIN employees managers ON managers.id = reviewees.manager_id')
            .order('managers.full_name IS NULL, managers.full_name ASC, employees.full_name ASC')
        when 'department'
          @reviewees = @reviewees
            .joins(:employee)
            .joins('LEFT JOIN departments ON departments.id = employees.department_id')
            .order('departments.name IS NULL, departments.name ASC, employees.full_name ASC')
        else
          @reviewees = @reviewees.joins(:employee).order('employees.full_name')
        end
      end

      def paginate_reviewees
        @reviewees = @reviewees.paginate(
          page: params[:page] || 1,
          per_page: params[:page_size] || 8
        )
      end

      def group_reviewees
        return [] unless %w[manager department].include?(params[:group_by])

        group_map = case params[:group_by]
                    when 'manager'
                      @reviewees.joins(:employee).group_by { |r| r.manager&.full_name || 'Unknown' }
                    when 'department'
                      @reviewees.joins(:employee).group_by { |r| r.employee.department&.name || 'Unknown' }
                    end

        group_map.flat_map do |name, group_items|
          [{
            row_type: 'group',
            group_data: {
              name: name,
              profile_picture: nil
            }
          }] + group_items.map { |r| RevieweeManageSerializer.new(r, scope: reviewee_scope).as_json }
        end
      end

      def serialize(records)
        ActiveModelSerializers::SerializableResource.new(
          records, each_serializer: RevieweeManageSerializer, scope: reviewee_scope
        ).as_json
      end

      def build_response(grouped_reviewees)
        {
          current_page: @reviewees.current_page,
          per_page: @reviewees.per_page,
          next_page: @reviewees.next_page,
          previous_page: @reviewees.previous_page,
          total_entries: @reviewees.total_entries,
          reviewees: grouped_reviewees
        }
      end
    end
  end
end
