module PerformanceReview
  module WebPerformanceReview
    class ListReviewers < ApplicationService
      attr_reader :review_cycle, :result_for

      def initialize(review_cycle_id, result_for = nil)
        @review_cycle = ReviewCycle.find(review_cycle_id)
        @result_for = result_for
        @data = {}
      end

      def call
        return if review_cycle.blank?

        validate_default_reviewers_creation
        fetch_reviewers_list

        @data
      end

      private

      def validate_default_reviewers_creation
        @review_cycle.reviewees.each(&:create_default_reviewers)
      end

      def fetch_reviewers_list
        employee_ids = (all_reviewers.map(&:employee_id) + all_reviewers.map(&:reviewee_employee_id)).uniq
        employee_list = Employee.where(id: employee_ids)
          .left_joins(:department)
          .joins(:user)
          .select(:id, :full_name, :profile_picture, 'departments.name AS department_name', 'users.email AS email')

        reviewer_types.each_with_object([]) do |reviewer_type, _store|
          reviewer_based_on_type = all_reviewers.select { |reviewer| reviewer.reviewer_type_id == reviewer_type.id }
          reviewer_based_on_type = reviewer_based_on_type&.map do |reviewer|
            reviewer_employee = employee_list.select { |employee| employee.id == reviewer.employee_id }.first
            reviewee_employee = employee_list.select { |employee| employee.id == reviewer.reviewee_employee_id }.first
            reviewer_data = {
              reviewer: {
                id: reviewer.id,
                employee: reviewer_employee,
              },
              reviewee: {
                id: reviewer.reviewee_id,
                employee: reviewee_employee,
              },
            }
            reviewer_data
          end
          if result_for == 'csv'
            type_reviewer = reviewer_type.reviewer_type
          else
            type_reviewer = reviewer_type.reviewer_type.gsub('_', ' ').upcase
            type_reviewer += ' (CUSTOM)' unless ReviewerType::DEFAULT_REVIEWER_TYPES.include?(reviewer_type.reviewer_type)
          end
          @data[type_reviewer] = reviewer_based_on_type
        end
      end

      def reviewer_types
        @_reviewer_types ||= ReviewerType.where(review_cycle: review_cycle).where.not(reviewer_type: :self).select(:id,
                                                                                                                   :reviewer_type, :first_person, :second_person)
      end

      def all_reviewers
        @_all_reviewers ||= Reviewer.where(review_cycle: review_cycle)
          .joins(:reviewee)
          .select(:id, :reviewer_type_id, :reviewee_id,
                  :employee_id, 'reviewees.employee_id AS reviewee_employee_id')
      end
    end
  end
end
