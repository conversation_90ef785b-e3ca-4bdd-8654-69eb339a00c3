module PerformanceReview
  module WebPerformanceReview
    class FetchRevieweeGoalsWeb < ApplicationService
      attr_reader :review_cycle, :reviewee, :goal_cycles, :goal_status, :action_type, :manager_id,
                  :manager_is_admin, :reviewee_goals, :employee, :parents_not_visible, :goal_config
      attr_accessor :my_goals, :result

      include RevieweeGoalConcern
      include PerformanceReview::Reviewees::Goals::EventLogs::Helper

      def initialize(review_cycle_id, reviewee_id, goal_status = nil, action_type = nil)
        @review_cycle = ReviewCycle.find(review_cycle_id)
        @reviewee = Reviewee.find(reviewee_id)
        @manager_id = reviewee.manager_id
        @manager_is_admin = reviewee.manager&.admin?
        @parents_not_visible = []
        @goal_cycles = review_cycle.goal_cycles
        @goal_config = review_cycle.goal_config_json
        @goal_status = goal_status
        @employee = reviewee.employee
        @action_type = action_type
        @result = set_default_data
      end

      def call
        return review_goal_status if review_cycle.review_cycle_goal_type[:goal_type] == 'goals_with_objectives_and_key_results'

        if action_type.present?
          case action_type
          when 'create'
            create_reviewee_goals unless @reviewee.goal_selection_done
          when 'approve'
            create_reviewee_goals unless @reviewee.goal_approval_done
          end
        end
        if goal_status
          review_goal_status
        else
          build_result
        end
      end

      private

      def set_default_data
        {
          data: {},
          parent_goal_ids: [],
          approved: reviewee.goal_approval_done,
          goal_selection_done: reviewee.goal_selection_done,
          auto_approve_goals_after_edit: reviewee.auto_approve_goals_after_edit,
          goal_configs: {
            minimum_goal_weightage: goal_config&.minimum_goal_weightage || ReviewCycle::DEFAULT_GOAL_CONFIG[:minimum_goal_weightage],
            maximum_goal_weightage: goal_config&.maximum_goal_weightage || ReviewCycle::DEFAULT_GOAL_CONFIG[:maximum_goal_weightage],
            allow_zero_weightage_goals: goal_config.allow_zero_weightage_goals,
          },
          previous_manager_actions: reviewee.previous_manager_actions,
        }
      end

      def review_goal_status
        @reviewee.reviewee_goals
          .where(review_cycle: @review_cycle)
          .joins(:goal)
          .where(goals: { goal_cycle_id: goal_cycles.map(&:id) })
          .order('goals.created_at DESC')
      end

      def goals
        @_goals ||= begin
          top_level_objective_ids = my_goals.dig(:goal_children)&.keys || []

          Goal.where(id: top_level_objective_ids)
            .joins(:employee_goals)
            .where(employee_goals: { employee_id: reviewee.employee.id })
            .where(goal_cycle_id: goal_cycles.pluck(:id)).order(created_at: :asc)
        end
      end

      def my_goals
        @_my_goals ||= CoreOkr::MyGoals::Index
          .call(review_cycle.account, reviewee.employee, build_goal_params(reviewee.employee), 1000)
          .as_json
          .with_indifferent_access
      end

      def build_goal_params(employee)
        {
          goal_types: ['individual'],
          cycles: goal_cycles.pluck(:id),
          view_type: false,
          owners: [employee],
          account_id: employee.account_id,
          slack_payload: true,
        }.with_indifferent_access
      end

      def create_reviewee_goals
        if @review_cycle.show_key_results
          @key_results = []
          goals.each do |goal|
            next if goal.nil?
            next if my_goals[:goal_children][goal.id.to_s].blank?

            @key_results.concat(
              Goal.where(id: my_goals[:goal_children][goal.id.to_s],
                         goal_cycle_id: goal_cycles.map(&:id)).joins(:employee_goals).where(
                employee_goals: { employee_id: @reviewee.employee_id },
              ),
            )
          end
          if @key_results.length.positive?
            @key_results = filter_out_existing_goals(@key_results)
            @key_results.each do |key_result|
              is_child_visible_to_manager = add_visibility_flag(key_result, manager_id, review_cycle)
              next if action_type == 'approve' && !is_child_visible_to_manager

              is_parent_visible_to_manager = add_visibility_flag(key_result&.parents&.first, manager_id, review_cycle)
              exclude = !(is_parent_visible_to_manager && is_child_visible_to_manager)
              new_reviewee_goal = create_reviewee_goal(@reviewee, key_result&.id, review_cycle)
              next if new_reviewee_goal.blank?

              new_reviewee_goal.update(exclude: exclude) if exclude
            end
          end
        else
          @new_goals = filter_out_existing_goals(goals)
          @new_goals.each do |goal|
            is_visible_to_manager = add_visibility_flag(goal, manager_id, review_cycle)
            next if action_type == 'approve' && !is_visible_to_manager

            exclude = !is_visible_to_manager
            new_reviewee_goal = create_reviewee_goal(@reviewee, goal&.id, review_cycle)
            next if new_reviewee_goal.blank?

            new_reviewee_goal.update(exclude: exclude)
          end
        end
      end

      def filter_out_existing_goals(goal_arr)
        existing_goals = @reviewee.reviewee_goals.includes(:goal).map(&:goal)
        goal_arr - existing_goals
      end

      def build_result
        @reviewee_goals = fetch_reviewee_goals

        if @reviewee.goal_approval_done
          @reviewee_goals.where(approver_id: nil).update_all(exclude: true, discarded_at: Time.current)
        end

        reviewee_goals.each do |reviewee_goal|
          goal = reviewee_goal.goal

          unless goal.employee_goals.exists?(employee_id: employee.id)
            reviewee_goal.discard unless @reviewee.goal_approval_done
            next
          end

          is_visible_to_manager = add_visibility_flag(goal, manager_id, review_cycle)
          if !is_visible_to_manager && !reviewee_goal.exclude && !@reviewee.goal_approval_done
            reviewee_goal.update!(exclude: true, weightage: nil)
          end

          next if action_type == 'approve' && !is_visible_to_manager

          if @review_cycle.show_key_results

            # FIXME: We need to address the issue of only using the first parent and incorporate support for multiple parents.
            parent_goal = goal.parents&.first
            handle_key_results_only(reviewee_goal, parent_goal, is_visible_to_manager)

            if @review_cycle.key_result_depth > 1
              process_nested_key_results(reviewee_goal.goal, @review_cycle.key_result_depth - 1, reviewee_goal.id.to_s)
            end
          else
            handle_objectives_only(reviewee_goal, is_visible_to_manager)
          end
        end
        if @review_cycle.show_key_results && action_type == 'approve' && !@reviewee.goal_approval_done
          process_parent_goal_visibility_for_approve_action
        end

        if both_goal_selection_and_approval_exists?
          result.merge!(
            requested_changes: requested_changes?,
            last_event_log: last_goal_event,
            requested_changes_enabled: true,
          )
        end
        result
      end

      def fetch_reviewee_goals
        if @reviewee.goal_approval_done
          @reviewee.reviewee_goals
            .where(review_cycle: @review_cycle)
            .joins(:goal)
            .where(goals: { goal_cycle_id: goal_cycles.map(&:id) })
            .where(exclude: false)
            .includes(goal: [:parents, :employee_goals])
        else
          @reviewee.reviewee_goals
            .where(review_cycle: @review_cycle)
            .joins(:goal)
            .where(goals: { goal_cycle_id: goal_cycles.map(&:id) })
            .includes(goal: [:parents, :employee_goals])
        end
      end

      def handle_objectives_only(reviewee_goal, is_visible_to_manager)
        result[:data][reviewee_goal.id.to_s] =
          reviewee_goal.as_json(only: [:id, :weightage, :exclude, :goal_id])
            .merge!(
              children: [],
              title: reviewee_goal.goal.title,
              description: reviewee_goal&.goal&.description,
              is_visible_to_manager: is_visible_to_manager,
            )

        result[:parent_goal_ids].push(reviewee_goal.id.to_s)
      end

      def handle_key_results_only(reviewee_goal, parent_goal, is_child_visible_to_manager)
        if result[:data].key?(parent_goal.id.to_s)
          result[:data][parent_goal.id.to_s][:children].push(reviewee_goal.id)
        else
          result[:data][parent_goal.id.to_s] = build_parent_data(parent_goal, reviewee_goal.id)
          result[:parent_goal_ids].push(parent_goal.id)
        end

        result[:data][reviewee_goal.id.to_s] =
          build_child_data(reviewee_goal, reviewee_goal.goal, parent_goal.id, is_child_visible_to_manager)
      end

      def build_parent_data(parent_goal, reviewee_goal_id)
        is_parent_visible_to_manager = add_visibility_flag(parent_goal, manager_id, review_cycle)
        parents_not_visible << parent_goal.id unless is_parent_visible_to_manager

        res = {
          id: parent_goal.id,
          goal_id: parent_goal.id,
          title: parent_goal.title,
          description: parent_goal&.description,
          children: [reviewee_goal_id],
          read_only: true,
          parent_id: nil,
          is_visible_to_manager: is_parent_visible_to_manager,
        }
      end

      def build_child_data(reviewee_goal, goal, parent_goal_id, is_child_visible_to_manager)
        res = reviewee_goal.as_json(
          only: [:id, :weightage, :exclude, :goal_id],
        ).merge!(
          title: goal&.title,
          description: goal&.description,
          children: [], read_only: false,
          parent_id: parent_goal_id,
          is_visible_to_manager: is_child_visible_to_manager
        )
      end

      def process_parent_goal_visibility_for_approve_action
        parents_not_visible.each do |parent_goal_id|
          parent_data = result[:data][parent_goal_id.to_s]

          # Mark parent as not eligible if no eligible child exists
          next if @reviewee.reviewee_goals.exists?(id: parent_data[:children], exclude: false)

          parent_data = result[:data].delete(parent_goal_id.to_s)
          parent_data[:children].each { |child_id| result[:data].delete(child_id.to_s) }
          result[:parent_goal_ids].delete(parent_goal_id)
        end
      end

      def process_nested_key_results(parent_goal, depth, initial_call_reviewee_goal_id = nil)
        return if depth.zero? || depth.negative?

        children = (goal_key_results_and_projects(parent_goal) + goal_aligned_goals(parent_goal)).flatten.uniq
        children.each do |child_goal|
          next unless child_goal.employee_goals.exists?(employee_id: employee.id)

          result[:data][child_goal.id.to_s] = {
            id: child_goal.id,
            goal_id: child_goal.id,
            title: child_goal.title,
            description: child_goal&.description,
            children: [],
            read_only: true,
          }

          if action_type == 'create'
            is_visible_to_manager = add_visibility_flag(child_goal, manager_id, review_cycle)
            result[:data][child_goal.id.to_s].merge!(is_visible_to_manager: is_visible_to_manager)
          end

          process_nested_key_results(child_goal, depth - 1, nil)
        end
        result[:data][initial_call_reviewee_goal_id.presence || parent_goal.id.to_s][:children] += children.pluck(:id)
      end

      def goal_key_results_and_projects(goal)
        goal.child_key_results_and_projects.joins(:employee_goals).where(
          employee_goals: { employee_id: @reviewee.employee.id },
        ).where(goal_cycle_id: goal_cycles.map(&:id))
      end

      def goal_aligned_goals(goal)
        goal.aligned_goals.joins(:employee_goals)
          .where(goal_cycle_id: goal_cycles.map(&:id)).where(
          employee_goals: { employee_id: @reviewee.employee.id },
        ).order(created_at: :asc)
      end
    end
  end
end
