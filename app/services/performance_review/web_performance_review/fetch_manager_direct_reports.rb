module PerformanceReview
  module WebPerformanceReview
    class FetchManagerDirectReports < ApplicationService
      attr_reader :employee, :review_cycle, :type

      def initialize(employee, review_cycle, type)
        @employee = employee
        @review_cycle = review_cycle
        @type = type
      end

      def call
        return if employee.blank?
        return if review_cycle.blank?

        case type
        when 'direct_reports'
          fetch_direct_reports
        when 'direct_reports_goals'
          @reviewee_goals = []
          create_reviewee_goals
          fetch_direct_reports_goals_status

        end
      end

      private

      def create_reviewee_goals
        @review_cycle.reviewees.left_joins(employee: :department)
          .where(manager_id: @employee)
          .map do |reviewee|
            if review_cycle.goal_with_objectives_and_key_results?
              @reviewee_goals << {
                reviewee: reviewee.id,
                reviewee_goals: reviewee.reviewee_goals,
                employee_id: reviewee.employee_id,
              }
            else
              reviewee_goals_data = PerformanceReview::WebPerformanceReview::FetchRevieweeGoalsWeb.call(review_cycle.id,
                                                                                                        reviewee.id, true).map(&:id)

              @reviewee_goals << { reviewee: reviewee.id, reviewee_goals: reviewee_goals_data,
                                   employee_id: reviewee.employee_id }
            end
          end
      end

      def fetch_direct_reports_goals_status
        @reviewee_goals.each_with_object([]) do |reviewee_data, store|
          employee_data = Employee.left_joins(:department).where(id: reviewee_data[:employee_id]).select(
            :full_name, 'departments.name AS department_name', :profile_picture, :id
          ).first
          reviewee = Reviewee.find(reviewee_data[:reviewee])
          reviewee_goal_approval_status = reviewee.goal_approval_done
          reviewee_goal_selection_status = reviewee.goal_selection_done

          if reviewee_data[:reviewee_goals].blank?
            pending = if reviewee_goal_selection_status
                        false
                      else
                        !review_cycle.reviewer_types.exists?(goal_approval_required: true, reviewer_type: 'manager',
                                                             define_goals: true)
                      end
            store << {
              reviewee_id: reviewee_data[:reviewee],
              pending: pending,
              created: false,
              approved: false,
              employee: employee_data,
              goal_selection_done: reviewee_goal_selection_status,
              requested_changes: reviewee.goal_changes_requested?,
              previous_manager_actions: reviewee.previous_manager_actions,
            }
          else
            data =
              {
                reviewee_id: reviewee_data[:reviewee],
                pending: false,
                created: true,
                approved: reviewee_goal_approval_status,
                employee: employee_data,
                goal_selection_done: reviewee_goal_selection_status,
                requested_changes: reviewee.goal_changes_requested?,
                previous_manager_actions: reviewee.previous_manager_actions,
              }
            store.push(data)
          end
        end
      end

      def fetch_direct_reports
        @review_cycle.reviewees.left_joins(:employee, employee: :department)
          .where(manager: @employee)
          .map do |reviewee|
          reviewee_data = Reviewee.where(id: reviewee.id).select(:self_review_done, :peer_selection_done,
                                                                 :peer_approval_done, :review_writing_percent,
                                                                 'reviewees.id AS reviewee_id').first.as_json(except: :id)
          employee_data = Employee.left_joins(:department).where(id: reviewee.employee_id).select(:full_name, 'departments.name AS department_name',
                                                                                                  :profile_picture, :id).first

          reviewee_data.merge!(employee: employee_data).merge!({ previous_manager_actions: reviewee.previous_manager_actions })
        end
      end
    end
  end
end
