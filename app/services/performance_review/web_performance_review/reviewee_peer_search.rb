module PerformanceReview
  module WebPerformanceReview
    class RevieweePeerSearch < ApplicationService
      include Employee::SelectLists::EmployeesListHelper
      attr_reader :account, :employee, :reviewee, :is_current_emp_manager, :is_current_emp_admin,
                  :reviewee_employee, :intent, :ids_only, :peer_selection_list, :employee_ids

      def initialize(account, employee, reviewee, params = {})
        @account = account
        @employee = employee

        @reviewee = reviewee
        @reviewee_employee = reviewee&.employee
        @params = params
        @search = params[:search_text]
        @intent = params[:intent]
        @groups = params[:groups]
        @limit = params[:limit].to_i.positive? ? params[:limit].to_i : 20
        @ids_only = params[:ids_only].present?
        @peer_selection_list = ids_only ? [] : { department: [], others: [] }
        @is_current_emp_manager = employee&.id == reviewee&.manager&.id
        @is_current_emp_admin = (employee&.id != reviewee&.employee_id) && employee&.admin?
      end

      def call
        return peer_selection_list if account.blank? || employee.blank? || reviewee.blank?

        generate_peer_selection_list
      end

      private

      def generate_peer_selection_list
        fetch_eligible_employee_ids
        return employee_ids if ids_only

        fetch_employees
        peer_selection_list
      end

      def fetch_eligible_employee_ids
        @employee_ids = filter_eligible_peers(fetch_employee_ids_to_exclude)
      end

      def fetch_employee_ids_to_exclude
        exclude_emp_ids = []
        exclude_emp_ids << employee.try(:id)
        exclude_emp_ids << reviewee.reviewers.where(rejected_by_id: nil).pluck(:employee_id)
        exclude_emp_ids << reviewee_employee.direct_reports.pluck(:id)
        exclude_emp_ids << reviewee.employee_id if is_current_emp_manager || is_current_emp_admin
        exclude_emp_ids.flatten.compact.uniq
      end

      def filter_eligible_peers(exclude_emp_ids)
        eligible_peers = filter_review_participants(account.employees.active.where.not(id: exclude_emp_ids))
        @employees = build_base_query(eligible_peers)

        if search.present?
          apply_search
        else
          @employees = employees.order(Arel.sql('RAND()'))
        end
        return employees.limit(limit).ids if groups.blank?

        build_grouped_employee_ids
      end

      def filter_review_participants(eligible_peers)
        review_cycle = reviewee.review_cycle
        return eligible_peers unless limit_peers_to_participants?(review_cycle)

        eligible_peers.where(
          id: review_cycle.reviewers.pluck(:employee_id) + review_cycle.reviewees.pluck(:employee_id),
        )
      end

      def limit_peers_to_participants?(review_cycle)
        review_cycle.reviewer_types.exists?(reviewer_type: 'peer', limit_to_participants: true) &&
          (%w[approve_peers choose_direct_report_peers].include?(intent) || !is_current_emp_admin)
      end

      def build_base_query(eligible_peers)
        query = eligible_peers.joins(:user)
        query = query.left_joins(:department) if include_department?
        query
      end

      def fetch_employees
        employees = Employee.where(id: employee_ids).left_joins(:department).joins(:user).select(
          :id, :full_name, :profile_picture, :department_id,
          'users.email AS email', 'departments.name AS department_name'
        )

        department_employees = employees.where(
          ':dept_id IS NOT NULL AND employees.department_id = :dept_id', { dept_id: reviewee_employee.department_id }
        )
        @peer_selection_list[:department] = department_employees.as_json

        @peer_selection_list[:others] = employees.where.not(id: department_employees.pluck(:id)).as_json
      end
    end
  end
end
