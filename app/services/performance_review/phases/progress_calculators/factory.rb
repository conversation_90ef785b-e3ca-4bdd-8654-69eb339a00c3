# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      class Factory
        RECEIVING_FEEDBACK_CALCULATORS = {
          'goal_selection' => ReceivingFeedback::GoalSelectionProgressCalculator,
          'goal_approval' => ReceivingFeedback::GoalApprovalProgressCalculator,
          'competency_selection' => ReceivingFeedback::CompetencySelectionProgressCalculator,
          'competency_approval' => ReceivingFeedback::CompetencyApprovalProgressCalculator,
          'peer_selection' => ReceivingFeedback::PeerSelectionProgressCalculator,
          'peer_approval' => ReceivingFeedback::PeerApprovalProgressCalculator,
          'release_reviews' => ReceivingFeedback::ReleaseReviewsProgressCalculator,
          'write_reviews' => ReceivingFeedback::WriteReviewsProgressCalculator,
          'manager_summary' => ReceivingFeedback::ManagerSummaryProgressCalculator,
          'hierarchical_calibration' => ReceivingFeedback::HierarchicalCalibrationProgressCalculator
        }.freeze

        def self.for(phase_type)
          raise ArgumentError, "phase_type cannot be nil" if phase_type.nil?

          calculator_class = RECEIVING_FEEDBACK_CALCULATORS[phase_type]

          if calculator_class.nil?
            Rails.logger.warn("Unknown phase_type: #{phase_type}. Using default calculator.")
            return DefaultProgressCalculator.new
          end

          calculator_class.new
        rescue StandardError => e
          Rails.logger.error("Error creating progress calculator for phase_type: #{phase_type}. Error: #{e.message}")
          DefaultProgressCalculator.new
        end
      end
    end
  end
end
