# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      module ReceivingFeedback
        class HierarchicalCalibrationProgressCalculator < BaseReceivingFeedbackCalculator
          def calculate(review_cycle_phase, filtered_reviewees, review_cycle, phase_config = nil)
            completed = review_cycle_phase.calibrators.where(status: 'submitted').count
            total = review_cycle_phase.calibrators.count

            if phase_config&.dig('progress_bar', 'tooltip')
              tooltip_text = "#{completed}/#{total} #{phase_config['progress_bar']['tooltip']}"
              [completed, total, tooltip_text]
            else
              [completed, total]
            end
          end
        end
      end
    end
  end
end
