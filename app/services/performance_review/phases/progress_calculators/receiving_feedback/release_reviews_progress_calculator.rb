# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      module ReceivingFeedback
        class ReleaseReviewsProgressCalculator < BaseReceivingFeedbackCalculator
          def calculate(review_cycle_phase, filtered_reviewees, review_cycle, phase_config = nil)
            # Release reviews uses a specific column name and custom tooltip logic
            completed = filtered_reviewees.where(release_review_done: true).count
            total = filtered_reviewees.count

            if phase_config&.dig('progress_bar', 'tooltip')
              tooltip_text = format(phase_config['progress_bar']['tooltip'], completed: completed, total: total)
              [completed, total, tooltip_text]
            else
              [completed, total]
            end
          end
        end
      end
    end
  end
end
