# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      module ReceivingFeedback
        class GoalApprovalProgressCalculator < BaseReceivingFeedbackCalculator
          def calculate(review_cycle_phase, filtered_reviewees, review_cycle, phase_config = nil)
            return if review_cycle_phase&.phase_type.nil?

            column_name = "#{review_cycle_phase.phase_type}_done"

            manager_defined_goals = review_cycle.reviewer_types.exists?(
               goal_approval_required: true,
               reviewer_type: 'manager',
               define_goals: true,
             )
            tooltip_key = manager_defined_goals ? 'define_goals_tooltip' : 'approve_goals_tooltip'
            tooltip_message = phase_config&.dig('progress_bar', 'receiving_feedback', tooltip_key)

            calculate_reviewee_progress(column_name, filtered_reviewees, tooltip_message)
          end
        end
      end
    end
  end
end
