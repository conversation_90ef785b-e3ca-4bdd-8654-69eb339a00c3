# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      module ReceivingFeedback
        class WriteReviewsProgressCalculator < BaseReceivingFeedbackCalculator
          def calculate(review_cycle_phase, filtered_reviewees, review_cycle, phase_config = nil)
            # If review_cycle_phase record have an associated reviewer_type, it means its a standalone phase.
            if review_cycle_phase.reviewer_type.present?
              tooltip_message = get_tooltip_for_reviewer_type(review_cycle_phase.reviewer_type, phase_config)
              calculate_individual_reviewer_type_progress(review_cycle_phase.reviewer_type, filtered_reviewees,
                                                          review_cycle, tooltip_message)
            else
              :build_combined_stats
            end
          end

          private

          def get_tooltip_for_reviewer_type(reviewer_type, phase_config)
            return nil unless phase_config&.dig('progress_bar')

            # Map reviewer type to specific tooltip key
            tooltip_key = case reviewer_type.reviewer_type
                          when 'peer'
                            'peer_tooltip'
                          when 'self'
                            'self_tooltip'
                          when 'direct_report'
                            'direct_report_tooltip'
                          else
                            'default_tooltip'
                          end

            phase_config.dig('progress_bar', 'receiving_feedback', tooltip_key)
          end
        end
      end
    end
  end
end
