# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      module ReceivingFeedback
        class CompetencySelectionProgressCalculator < BaseReceivingFeedbackCalculator
          def calculate(review_cycle_phase, filtered_reviewees, review_cycle, phase_config = nil)
            return if review_cycle_phase&.phase_type.nil?

            column_name = "#{review_cycle_phase.phase_type}_done"
            tooltip_message = phase_config&.dig('progress_bar', 'receiving_feedback', 'tooltip')
            calculate_reviewee_progress(column_name, filtered_reviewees, tooltip_message)
          end
        end
      end
    end
  end
end
