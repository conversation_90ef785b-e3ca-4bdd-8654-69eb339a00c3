# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      module ReceivingFeedback
        class BaseReceivingFeedbackCalculator
          def calculate(review_cycle_phase, filtered_reviewees, review_cycle, phase_config = nil)
            raise NotImplementedError, 'Subclasses must implement calculate method'
          rescue StandardError => e
            Rails.logger.error("Error calculating progress for phase: #{review_cycle_phase&.phase_type}. Error: #{e.message}")
            [0, 0] # Return safe default
          end

          private

          # Helper methods that strategies can use
          def calculate_reviewee_progress(column_name, filtered_reviewees, tooltip_message = nil)
            completed = filtered_reviewees.where(column_name => true).count
            total = filtered_reviewees.count
            tooltip_text = tooltip_message ? "#{completed}/#{total} #{tooltip_message}" : nil
            [completed, total, tooltip_text].compact
          end

          def calculate_manager_progress(column_name, filtered_reviewees, tooltip_message = nil)
            completed = filtered_reviewees.where.not(manager_id: nil).where(column_name => true).count
            total = filtered_reviewees.where.not(manager_id: nil).count
            tooltip_text = tooltip_message ? "#{completed}/#{total} #{tooltip_message}" : nil
            [completed, total, tooltip_text].compact
          end

          def calculate_individual_reviewer_type_progress(reviewer_type, filtered_reviewees, review_cycle, tooltip_message = nil)
            # Get reviewers for the specific reviewer type and filtered reviewees
            filtered_reviewers = review_cycle.reviewers.joins(:reviewer_type).where(
              reviewer_types: { id: reviewer_type.id },
            ).where(
              'reviewer_types.approval_required = false AND reviewers.rejected_by_id IS NULL OR
               (reviewer_types.approval_required = true AND reviewers.approver_id IS NOT NULL)',
            ).where(reviewee: filtered_reviewees)

            total = filtered_reviewers.count
            pending = filtered_reviewers.where(review_submitted: false).count
            completed = total - pending

            if tooltip_message
              tooltip_text = "#{completed}/#{total} #{tooltip_message}"
              [completed, total, tooltip_text]
            else
              [completed, total]
            end
          end
        end
      end
    end
  end
end
