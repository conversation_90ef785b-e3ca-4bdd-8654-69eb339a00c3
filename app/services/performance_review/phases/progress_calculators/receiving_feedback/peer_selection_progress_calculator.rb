# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      module ReceivingFeedback
        class PeerSelectionProgressCalculator < BaseReceivingFeedbackCalculator
          def calculate(review_cycle_phase, filtered_reviewees, review_cycle, phase_config = nil)
            return if review_cycle_phase&.phase_type.nil?

            column_name = "#{review_cycle_phase.phase_type}_done"
            
            case review_cycle.peer_selection_role
            when 'manager'
              tooltip_message = phase_config&.dig('progress_bar', 'receiving_feedback', 'manager_tooltip')
            when 'self'
              tooltip_message = phase_config&.dig('progress_bar', 'receiving_feedback', 'self_tooltip')
            else
              [0, 0]
            end

            calculate_reviewee_progress(column_name, filtered_reviewees, tooltip_message)
          end
        end
      end
    end
  end
end
