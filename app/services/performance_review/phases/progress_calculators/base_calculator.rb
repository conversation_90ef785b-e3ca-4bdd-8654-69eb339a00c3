# frozen_string_literal: true

module PerformanceReview
  module Phases
    module ProgressCalculators
      class BaseCalculator
        def calculate(review_cycle_phase, filtered_reviewees, review_cycle, phase_config = nil)
          raise NotImplementedError, "Subclasses must implement calculate method"
        rescue StandardError => e
          Rails.logger.error("Error calculating progress for phase: #{review_cycle_phase&.phase_type}. Error: #{e.message}")
          [0, 0] # Return safe default
        end
      end
    end
  end
end
