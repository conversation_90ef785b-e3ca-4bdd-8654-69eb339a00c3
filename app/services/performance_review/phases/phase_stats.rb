# PerformanceReview::Phases::PhaseStats

require 'ostruct'

module PerformanceReview
  module Phases
    class PhaseStats
      prepend SimpleCommand
      include RevieweeDataConcern

      attr_reader :review_cycle, :review_cycle_phases, :current_user, :current_employee,
                  :scoped_reviewee_ids, :scoped_reviewees, :filtered_reviewees, :phase_config

      def initialize(review_cycle:, reviewee_scope:, current_user:)
        @review_cycle = review_cycle
        @reviewee_scope = reviewee_scope
        @current_user = current_user
        @current_employee = current_user.employee
        @review_cycle_phases = review_cycle.review_cycle_phases.where.not(phase_type: 'hierarchical_calibration')
      end

      def call
        fetch_scoped_reviewee_ids
        set_scoped_reviewees
        set_filtered_reviewees
        set_phase_config

        stats_array = []

        @review_cycle_phases.each do |review_cycle_phase|
          phase_data = build_phase_data(review_cycle_phase)
          if phase_data.is_a?(Array)
            stats_array.concat(phase_data)
          elsif phase_data
            stats_array << phase_data
          end
        end

        { stats: stats_array }
      end

      private

      def build_phase_data(review_cycle_phase)
        phase_type = review_cycle_phase.phase_type
        phase_name = review_cycle_phase.name
        progress_data = calculate_progress(review_cycle_phase)

        # Handle special case for combined write_reviews
        if progress_data == :build_combined_stats
          return build_combined_write_reviews_stats(review_cycle_phase)
        end

        return nil unless progress_data

        build_stats_object(
          name: phase_name,
          phase_type: phase_type,
          progress_data: progress_data,
          review_cycle_phase: review_cycle_phase
        )
      end

      def get_phase_name(review_cycle_phase)
        phase_type = review_cycle_phase.phase_type

        case phase_type
        when 'goal_selection'
          'Goal Selection'
        when 'competency_selection'
          'Competency Selection'
        when 'peer_selection'
          'Peer Selection'
        when 'goal_approval'
          'Goal Approval'
        when 'competency_approval'
          'Competency Approval'
        when 'peer_approval'
          'Peer Approval'
        when 'write_reviews'
          get_write_reviews_phase_name(review_cycle_phase)
        when 'manager_summary'
          'Manager Summary'
        when 'release_reviews'
          'Release Reviews'
        when 'hierarchical_calibration'
          'Hierarchical Calibration'
        else
          phase_type.titleize
        end
      end

      def get_write_reviews_phase_name(review_cycle_phase)
        if review_cycle_phase.reviewer_type.present?
          "#{review_cycle_phase.reviewer_type.first_person} Reviews"
        else
          'Write Reviews'
        end
      end

      def build_combined_write_reviews_stats(review_cycle_phase)
        # Get all reviewer types that are not standalone (combined in this phase)
        combined_reviewer_types = review_cycle.reviewer_types
          .where.not(reviewer_type: 'manager')
          .where(standalone_launch: false)

        combined_reviewer_types.map do |reviewer_type|
          # Create a temporary phase with the reviewer_type to use the strategy
          temp_phase = OpenStruct.new(
            phase_type: 'write_reviews',
            reviewer_type: reviewer_type
          )

          progress_data = calculate_progress(temp_phase)
          next unless progress_data

          build_stats_object(
            name: "#{reviewer_type.first_person} Reviews",
            phase_type: 'write_reviews',
            progress_data: progress_data,
            review_cycle_phase: review_cycle_phase
          )
        end.compact
      end

      def build_stats_object(name:, phase_type:, progress_data:, review_cycle_phase:)
        completed_count, total_count, tooltip_text = progress_data
        progress_percentage = total_count > 0 ? ((completed_count.to_f / total_count) * 100).round : 0

        progress_status = {
          total_count: total_count,
          completed_count: completed_count,
          progress: progress_percentage
        }

        # Add tooltip_text if provided
        progress_status[:tooltip_text] = tooltip_text if tooltip_text

        {
          name: name,
          progress_status: progress_status,
          phase_type: phase_type,
          status: {
            value: determine_phase_status(review_cycle_phase, completed_count, total_count),
            sub_text: nil
          }
        }
      end

      def calculate_progress(review_cycle_phase)
        calculator = ProgressCalculators::Factory.for(review_cycle_phase.phase_type)
        phase_config = @phase_config[review_cycle_phase.phase_type]
        calculator.calculate(review_cycle_phase, @filtered_reviewees, @review_cycle, phase_config)
      end

      def determine_phase_status(review_cycle_phase, completed_count, total_count)
        # Check if phase is ended first (same logic as phase_list_details.rb)
        if (review_cycle.end_date.present? && review_cycle.end_date.to_i <= Time.now.utc.to_i) || review_cycle_phase.closed?
          return 'ENDED'
        end

        # Handle hierarchical_calibration differently (same logic as phase_list_details.rb)
        if review_cycle_phase.phase_type == 'hierarchical_calibration'
          if review_cycle_phase.calibrators.where.not(status: 'submitted').none?
            return 'COMPLETED'
          elsif review_cycle_phase.calibrators.where(status: ['pending', 'submitted']).none?
            return 'NOT STARTED'
          else
            return 'ONGOING'
          end
        else
          # Standard logic for other phase types
          progress_percentage = total_count > 0 ? ((completed_count.to_f / total_count) * 100).round : 0

          if progress_percentage == 100
            return 'COMPLETED'
          elsif total_count == 0 || (!notifications_launched?(review_cycle_phase) && progress_percentage == 0)
            return 'NOT STARTED'
          else
            return 'ONGOING'
          end
        end
      end

      def set_phase_config
        @phase_config = YAML.load_file('config/settings/review_phase_details.yml')
      end

      def fetch_scoped_reviewee_ids
        @scoped_reviewee_ids = Pundit.policy_scope!(current_user, review_cycle.reviewees).ids
      end

      def set_scoped_reviewees
        @scoped_reviewees = review_cycle.reviewees.where(id: scoped_reviewee_ids)
      end

      def set_filtered_reviewees
        @filtered_reviewees = filter_reviewees_by_scope(@reviewee_scope, @scoped_reviewees)
      end

      def notifications_launched?(review_cycle_phase)
        review_cycle_phase.review_cycle_notifications.exists?(task_type: %w[launch automated])
      end
    end
  end
end