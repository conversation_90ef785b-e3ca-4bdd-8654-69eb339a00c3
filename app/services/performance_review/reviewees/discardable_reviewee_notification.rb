# app/services/performance_review/reviewees/discardable_reviewee_notification.rb
module PerformanceReview
  module Reviewees
    module DiscardableRevieweeNotification
      def discard_notification_recipients
        return if review_cycle.nil?

        discard_individual_notification_recipients
        discard_manager_notification_recipients
      end

      private

      def discard_individual_notification_recipients
        individual_phases = %w[goal_selection release_reviews]
        individual_phases << 'peer_selection' if review_cycle.peer_selection_role == 'self'

        ReviewCycleNotificationRecipient
          .joins(:review_cycle_phase)
          .where(review_cycle_phases: { phase_type: individual_phases }, employee_id: employee.id)
          .discard_all
      end

      def discard_manager_notification_recipients
        return unless manager.present?

        manager_phases = %w[peer_approval goal_approval manager_summary]
        manager_phases << 'peer_selection' if review_cycle.peer_selection_role == 'manager'

        return if manager_has_other_reviewees?

        ReviewCycleNotificationRecipient
          .joins(:review_cycle_phase)
          .where(review_cycle_phases: { phase_type: manager_phases }, employee_id: manager.id)
          .discard_all
      end

      def manager_has_other_reviewees?
        review_cycle.reviewees
          .where.not(id: id)
          .where(manager: manager)
          .exists?
      end
    end
  end
end
