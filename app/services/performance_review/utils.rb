module PerformanceReview
  module Utils
    include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>
    def process_legend(legend_formula, score)
      # if the score is greater than the max value in the legend keys, return the max
      # if the score is less that the min value in the legend keys, retrin the min
      # otherwise if the score is between the min and max, use elect to get the range
      return if legend_formula.blank? || score.blank?

      legend_formula = legend_formula.transform_keys(&:to_f)

      score_stops = legend_formula.keys.sort
      return if score_stops.blank?

      score_stop = if score > score_stops.max
                     score_stops.max
                   elsif score < score_stops.min
                     score_stops.min
                   else
                     score_stops.select { |ss| score <= ss }.first
                   end
      return if score_stop.nil?

      legend_formula[score_stop]
    end

    def remove_special_chars_and_numbers(input_string)
      return if input_string.nil? || !input_string.is_a?(String)

      input_string.gsub(/[^a-zA-Z_ ]/, '').gsub(/\s+/, ' ').strip
    end

    def parameterized_text(input_string)
      cleaned = remove_special_chars_and_numbers(input_string)&.downcase
      return if cleaned.nil?

      words = cleaned.split(/\s|_+/)

      # Remove "previous" only if it's the first word and there are more after it
      if words.first == 'previous' && words.size > 1
        words.shift
      end

      words.join('_')
    end

    # This removes all HTML tags
    def sanitized_response_text(response_text, apply)
      return response_text unless apply

      ActionView::Base.full_sanitizer.sanitize(response_text)
    end

    def format_weightage(value)
      value = number_with_precision(value, precision: 2, strip_insignificant_zeros: true)
      value.nil? ? nil : "#{value}%"
    end

    def html_to_text(html)
      ActionView::Base.full_sanitizer.sanitize(html)
    end

    def template_question_table_helpers(review_template)
      tq_source = review_template.template_questions_source

      [
        tq_source,
        review_template.template_questions_klass,
        review_template.template_questions_table_name,
        ReviewResponse.template_question_fk(tq_source),
      ]
    end
  end
end
