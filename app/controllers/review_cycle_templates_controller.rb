class ReviewCycleTemplatesController < ApiController
  before_action :set_review_cycle_template, only: [:show, :update, :destroy, :create_from_template]
  before_action :load_account, :load_review_cycle
  before_action :track_template_use, only: [:create_from_template]

  def index
    authorize Review<PERSON><PERSON>Template
    render json: @review_cycle.review_cycle_templates.joins(:reviewer_type).where(reviewer_types: {creation_source: :normal})
  end

  # GET /review_cycle_templates/1
  def show
    render json: @review_cycle_template
  end

  # POST /review_cycle_templates
  def create
    authorize ReviewCycleTemplate

    create_rc_template = ReviewCycleTemplates::Create.call(
      review_cycle_template_params
    )

    if create_rc_template.success?
      # If 360 review cycle, then create this RCT for all remaining reviewer_types.
      process_for_reviewer_types
      render json: create_rc_template.result, status: :created
    else
      render_unprocessable_entity(create_rc_template.errors.full_messages)
    end
  end

  # PATCH/PUT /review_cycle_templates/1
  def update
    authorize @review_cycle_template
    if @review_cycle_template.update(review_cycle_template_params)
      process_for_reviewer_types
      render json: @review_cycle_template
    else
      render json: @review_cycle_template.errors, status: :unprocessable_entity
    end
  end

  # DELETE /review_cycle_templates/1
  def destroy
    authorize @review_cycle_template
    @review_cycle_template.discard
  end

  def create_from_template
    authorize @review_cycle_template

    create_from_template_params = params.merge!(current_employee: current_user.employee)
    create_from_template = ReviewCycleTemplates::CreateFromTemplate.call(create_from_template_params)
    if create_from_template.success?
      render json: create_from_template.result
    else
      render_unprocessable_entity(create_from_template.errors.full_messages)
    end
  end

  private

  def process_for_reviewer_types
    return unless @review_cycle.three_sixty_review?

    processed_reviewer_type_id = review_cycle_template_params[:reviewer_type_id].presence ||
      @review_cycle_template.reviewer_type_id
    @review_cycle.reviewer_types.where.not(id: processed_reviewer_type_id).pluck(:id).each do |reviewer_type_id|
      new_params = review_cycle_template_params.merge(reviewer_type_id: reviewer_type_id)
      rct = ReviewCycleTemplate.find_or_initialize_by(reviewer_type_id: reviewer_type_id)
      rct.assign_attributes(new_params)
      rct.save!
    end
  end

  def track_template_use
    return unless params.key?(:template_type) && params.key?(:template_properties)

    properties =
      case params[:template_type]
      when 'system'
        { template_properties: params[:template_properties], type: 'system template' }
      when 'user'
        { template_id: params[:template_properties], type: 'user template' }
      end

    Ahoy::Event.delay(queue: 'low').create!(
      name: 'review_cycle_template',
      properties: properties,
      user_id: current_user.id, time: Time.zone.now
    )
  end

  # Use callbacks to share common setup or constraints between actions.
  def set_review_cycle_template
    @review_cycle_template = ReviewCycleTemplate.find(params[:id])
  end

  # Only allow a trusted parameter "white list" through.
  def review_cycle_template_params
    params.require(:review_cycle_template)
      .permit(:id, :reviewer_type_id, :employee_attribute, :employee_id, :template_id)
  end

  def load_review_cycle
    @review_cycle = ReviewCycle.find(params[:review_cycle_id])
  end
end
