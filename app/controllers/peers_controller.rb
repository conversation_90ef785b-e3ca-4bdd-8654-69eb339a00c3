class PeersController < ApiController
  before_action :load_account, :load_review_cycle
  before_action :load_reviewee, :load_employee,
                only: [:search_peers, :save_selected_peers, :show, :update, :discard_peer, :unsubmit_peer_selection]
  before_action :load_manager, only: [:index]
  before_action :load_peer, only: [:discard_peer]

  def update
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee, :reviewee_or_reviewee_manager?, policy_class: PeerPolicy
    if nominated_reviewers_params.permitted?
      params_data = nominated_reviewers_params[:nominated_reviewers].map do |param|
        { reviewer_id: param[:reviewer_id], approved: param[:approved] }
      end
      ApproveReviewerNominations.call(
        @review_cycle.try(:id),
        @reviewee.try(:id),
        @current_user.employee.try(:id),
        params_data,
      )
      if nominated_reviewers_params[:manager_nominated_peers].present?
        AddReviewersByManager.call(
          @review_cycle.try(:id),
          @reviewee.try(:employee_id),
          @current_user.employee.try(:id),
          nominated_reviewers_params[:manager_nominated_peers],
        )

      end
    end
  end

  def index
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy
    direct_reports = PerformanceReview::WebPerformanceReview::FetchManagerDirectReports.call(
      @manager,
      @review_cycle,
      'direct_reports',
    )
    render json: { direct_reports: direct_reports }
  end

  def show
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee, :reviewee_or_reviewee_manager?, policy_class: PeerPolicy
    reviewee_peers = @reviewee.reviewers.joins(:reviewer_type)
      .where(reviewer_types: { reviewer_type: 'peer', nomination_required: true })
      .map do |reviewer|
      status = if reviewer.approver_id.nil? && reviewer.rejected_by_id.nil?
                 { approved: false, rejected: false }
               elsif reviewer.rejected_by_id.present?
                 { approved: false, rejected: true }
               elsif reviewer.approver_id.present?
                 { approved: true, rejected: false }
               end
      reviewer_data = { reviewer_id: reviewer.id, approved: status[:approved],
                        rejected: status[:rejected], nominator_role:  reviewer&.nominator_role&.role_type }
      reviewer_employee = @account.employees.joins('LEFT JOIN departments ON departments.id = employees.department_id')
        .joins(:user)
        .where(id: reviewer.employee.id)
        .select(:full_name, 'departments.name AS department_name', :profile_picture, :id, 'users.email').first
      reviewer_data.merge!(employee: reviewer_employee)
    end

    render json: { reviewee_peers: reviewee_peers }
  end

  def discard_peer
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee, :reviewee_or_reviewee_manager?, policy_class: PeerPolicy

    if @peer.present?
      @peer.update(rejected_by: @current_user.employee,  approver_id: nil)
      PerformanceReview::ReviewerHomePageNotificationRemovalService.call(@peer)
    end

    render status: :ok
  end

  def search_peers
    # Search peers dashboard or appmsith
    authorize @reviewee, :reviewee_or_reviewee_manager?, policy_class: PeerPolicy
    peers = PerformanceReview::WebPerformanceReview::RevieweePeerSearch.call(
      @account,
      @current_user.employee,
      @reviewee,
      {
        search_text: params[:search_text],
        intent: params[:intent],
      },
    )
    render json: { peers: peers }
  end

  def save_selected_peers
    # Add a peer reviewer via dashboard or appmsith
    authorize @reviewee, :reviewee_or_reviewee_manager?, policy_class: PeerPolicy
    peers = NominateReviewers.call(
      @review_cycle&.id, @employee&.try(:id),
      save_selected_peers_params[:employees_id],
      save_selected_peers_params[:nominator_role],
      @current_user.employee
    )
    if peers.success?
      render json: { status: 'success' }
    else
      render json: { error: 'Peer Selection Failed' }, status: :internal_server_error
    end
  end

  def unsubmit_peer_selection
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee, :reviewee_or_reviewee_manager?, policy_class: PeerPolicy
    @reviewee.peers.discard_all
    @reviewee.update!(peer_selection_done: false, peer_approval_done: false)
    render status: :ok
  end

  private

  def load_reviewee
    @reviewee = Reviewee.find_by(id: params[:id], review_cycle_id: params[:review_cycle_id])
  end

  def load_review_cycle
    @review_cycle = ReviewCycle.find(params[:review_cycle_id])
  end

  def load_employee
    @employee = @reviewee.employee
  end

  def load_manager
    @manager = Employee.find(params[:manager_id])
  end

  def load_peer
    @peer = Reviewer.find(params[:peer_id])
  end

  def nominated_reviewers_params
    params.require(:peer).permit(nominated_reviewers: [:reviewer_id, :approved], manager_nominated_peers: [])
  end

  def save_selected_peers_params
    params.require(:peer).permit(:nominator_role, employees_id: [])
  end
end
