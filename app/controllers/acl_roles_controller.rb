class AclRolesController < ApiController
  before_action :load_account
  before_action :load_acl_role, only: %i[show destroy]

  def index
    authorize :acl_role, :admin_headless_scope?
    acl_roles = AclRoles::Index.call(@account.id)

    if acl_roles.success?
      render json: acl_roles.result
    else
      render_unprocessable_entity(acl_roles.errors.full_messages)
    end
  end

  def show
    authorize @acl_role
    acl_role_service = AclRoles::Show.call(@acl_role.id)

    if acl_role_service.success?
      render json: acl_role_service.result
    else
      render_unprocessable_entity(acl_role_service.errors.full_messages)
    end
  end

  def create
    acl_role_id = acl_role_params[:id]
    @acl_role = acl_role_id.present? ? AclRole.find(acl_role_id) : AclRole.new(account_id: acl_role_params[:account_id])
    authorize @acl_role

    create_acl_roles = AclRoles::Create.call(acl_role_params)
    if create_acl_roles.success?
      render json: create_acl_roles.result
    else
      render_unprocessable_entity(create_acl_roles.errors.full_messages)
    end
  end

  def destroy
    authorize @acl_role
    @acl_role.update(discarded_by: current_user.employee)
    @acl_role.discard

    render_success('Acl Role discarded!')
  end

  private

  def acl_role_params
    permitted_params = params.require(:acl_role).permit(
      :id, :name, :description,
      acl_role_members_attributes: [
        :id, :acl_role_id, :member_type, :member_id, :_destroy,
        { member_attributes: [
          :id, :name, :description, :_destroy,
          { employee_filter_conditions_attributes: [:id, :table_attribute, :operator, :value] }
        ] }
      ],
      acls_attributes: [
        :id, :resource_type, :resource_id, :actorable_type, :actorable_id,
        :targetable_type, :targetable_id, :permission_type, :_destroy,
        { targetable_attributes: [
          :id, :name, :description, :_destroy,
          { employee_filter_conditions_attributes: [:id, :table_attribute, :operator, :value, json_value: []] },
          { goal_table_filter_conditions_attributes: [:id, :table_attribute, :operator, :value] }
        ] }
      ]
    ).merge(account_id: @account.id)

    permitted_params[:acls_attributes] = permitted_params[:acls_attributes].map do |a|
      a.merge(created_by: current_user.employee, skip_callbacks: true)
    end
    permitted_params
  end

  def load_acl_role
    @acl_role = AclRole.find(params[:id])
  end

  def load_account
    @account = Account.find(params[:account_id])
  end
end
