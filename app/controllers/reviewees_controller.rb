class RevieweesController < ApiController
  include RevieweeDataConcern

  before_action :load_account, :load_review_cycle
  before_action :load_reviewee, only: [
    :show, :update, :destroy,
    :calibrate_reviewer_type_tabs, :calibrate_review_response,
    :reset_peer_reviewers, :nominate_peer_reviewers, :change_reviewee_manager,
    :remove_reviewee, :update_reviewee_status, :get_assignable_managers_list,
    :update_calibrated_response, :addable_reviewers
  ]

  # GET method to get all reviewee from database given a review cycle
  def index
    authorize @review_cycle, :soft_load_review_cycle?,
              policy_class: ReviewCyclePolicy
    
    service = PerformanceReview::WebPerformanceReview::FetchRevieweeList.call(
      current_user: current_user,
      scoped_reviewees: scoped_reviewees,
      reviewee_scope: reviewees_api_type,
      params: params
    )

    if service.success?
      render json: service.result, status: :ok
    else
      render_unprocessable_entity(service.errors.full_messages.join(', '))
    end
  end

  # WILL GET DEPRECATED AFTER STANDARDISATION OF APIs RELEASE
  def calibrate_reviewer_type_tabs
    if params['reviewee_call_type'] == 'normal'
      authorize @reviewee, :verify_user_is_same_reviewee?
    else
      authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
      authorize @reviewee
    end

    role_params = {
      reviewee_id: @reviewee.id,
      role_type: params[:role_type]
    }

    # Identify user role using our new service class
    role_service = PerformanceReview::IdentifyReviewCycleUserRole.call(current_user&.employee, role_params)

    # Check if user role identification was successful
    return render_unprocessable_entity(role_service.errors.full_messages.join(', ')) unless role_service.success?

    # Get the result from the command
    user_role = role_service.result

    reviewer_type_tabs = PerformanceReview::WebPerformanceReview::GetReviewerTypeTabs.call(
      {
        reviewee_id: @reviewee.id,
        fetch_current_rc_only: params['reviewee_call_type'] == 'normal',
        current_employee_id: current_user.employee.id,
        user_role: user_role
      },
    )
    render json: { reviewer_types: reviewer_type_tabs }, status: :ok
  end

  def calibrate_review_response
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee

    data = calibrate_review_response_params.to_hash.transform_keys(&:to_sym)
    @reviewer_type = ReviewerType.find_by(id: data[:reviewer_type_id])
    if data.key?(:review_cycle_period) && data[:review_cycle_period] == 'past_review' && data[:passed_review_cycle_id].present?
      review_cycle_in_play = ReviewCycle.find_by(id: data[:passed_review_cycle_id])
    else
      review_cycle_in_play = @review_cycle
    end

    reviewee_employee = @reviewee.employee
    reviewee_in_cycle = review_cycle_in_play.reviewees.find_by(employee: reviewee_employee)
    if reviewee_in_cycle.nil?
      render json: { error: "#{reviewee_employee.full_name} was not receiving feedback in review cycle - #{review_cycle_in_play.title}" },
             status: :bad_request
    else
      reviewer = reviewee_in_cycle.reviewers.joins(:reviewer_type).find_by(reviewer_types: { id: @reviewer_type.id })
      if reviewer.nil?
        render json: { error: "#{reviewee_employee.full_name} did not have a #{@reviewer_type.reviewer_type} reviewer in  review cycle - #{review_cycle_in_play.title}" },
               status: :bad_request
      else
        reviewer_responses = PerformanceReview::WebPerformanceReview::ReviewResponses::AdminView.call(reviewer&.id,
                                                                                                      @reviewer_type&.id,
                                                                                                      current_user.employee&.id)
        render json: { reviewer_responses: reviewer_responses },
               status: :ok
      end

    end
  end

  # GET method to get a reviewee by id
  def show
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee
    render json: @reviewee
  end

  # POST /reviewees
  def create
    authorize Reviewee
    @reviewee = Reviewee.new(create_reviewee_params)

    if @reviewee.save
      render json: @reviewee, serializer: RevieweeManageSerializer, status: :created
    else
      render json: @reviewee.errors, status: :unprocessable_entity
    end
  end

  def update
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee

    if @reviewee.update(update_reviewee_params)
      render json: @reviewee, serializer: RevieweeManageSerializer, status: :ok
    else
      render_unprocessable_entity @reviewee.errors.full_messages
    end
  end

  # POST /reviewees/update_all
  # This would discard entries that are not there
  def update_all
    authorize Reviewee

    update_fields = [:created_at, :updated_at, :discarded_at]

    # Import all the entries shared
    Reviewee.import update_all_params.as_json,
                    on_duplicate_key_update: update_fields, validate_uniqueness: true

    # Discard entries which are not
    Reviewee.where(review_cycle: @review_cycle)
      .where.not(employee_id: update_all_params.map { |x| x['employee_id'] })
      .discard_all

    # Default reviewers, run it in delay
    scoped_reviewees.each { |x| x.delay(queue: 'immediate').create_default_reviewers }

    render json: scoped_reviewees, each_serializer: RevieweeManageSerializer
  end

  # PUT method for updating in database a reviewee based on id
  def reset_peer_reviewers
    authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee

    @peer_reviewers = @reviewee.reviewers.joins(:reviewer_type).where(reviewer_types: { reviewer_type: 'peer' })
    if @peer_reviewers.present? && @peer_reviewers.review_responses.blank?
      @peer_reviewers.destroy_all
      @reviewee.update(peer_selection_done: false)
    end
    render json: @reviewee
  end

  # POST method to add in db reviewers given array of emp_ids and a reviewee id
  def nominate_peer_reviewers
    authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee

    if @reviewee.present?
      @reviewee_employee = @reviewee&.employee
      NominateReviewers.call(@review_cycle.id, @reviewee_employee.id,
                             nominate_peers_params[:nominate_peers] || [],
                             nominate_peers_params[:nominator_role],
                             @current_user.employee
                             )
    end
    render json: @reviewee
  end

  # POST  method to change manager for a reviewee
  def change_reviewee_manager
    authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee
    if @review_cycle.enforce_system_manager_mapping.present?
      return render_unprocessable_entity('Not supported in this review!!, Only change manager at employee level') 
    end


    if @reviewee.present? && reviewee_manager_params.present?
      new_manager = @review_cycle.account.employees.where(id: reviewee_manager_params.to_i).first
      PerformanceReview::WebPerformanceReview::ChangeRevieweeManager.call(@review_cycle&.id, [@reviewee&.employee_id],
                                                                          new_manager&.id, reviewee_update_employee)
    end
    render status: :ok
  end

  # POST method to remove reviewee from a review cycle
  # Ideally this shouldn't be used. Bad code, we should just use the destroy method in Rails.
  def remove_reviewee
    authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee

    PerformanceReview::WebPerformanceReview::RemoveReviewee.call(
      @review_cycle.id, @reviewee.id
    )
  end

  # DELETE /reviewees/1
  def destroy
    authorize @reviewee
    @reviewee.discard
  end

  # PUTS update reviewee status attributes, peer, goal, approval status given reviewee id and body
  def update_reviewee_status
    authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee

    if @reviewee.update(update_reviewee_status_params)
      render status: :ok
    else
      render status: :bad_request
    end
  end

  # GET a list of employees that can be assigned a manager for a reviewee, this API is used for changing managers
  def get_assignable_managers_list
    authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee
    # all active employees can be managers
    assignable_managers = Pundit.policy_scope!(current_user, Employee.active.where(account: @review_cycle.account))
    render json: assignable_managers, each_serializer: EmployeeMiniSerializer
  end

  # Updates calibrated score of a review response for a question & reviewee
  def update_calibrated_response
    authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee

    calibrated_score_update = PerformanceReview::Calibration::UpdateCalibratedResponse.call(update_calibrated_response_params)
    return render_unprocessable_entity(calibrated_score_update.errors.full_messages) if calibrated_score_update.failure?

    render json: calibrated_score_update.result
  end

  def addable_reviewers
    authorize @review_cycle, :load_review_cycle?, policy_class: ReviewCyclePolicy
    authorize @reviewee

    employees = Pundit.policy_scope!(current_user, @review_cycle.account.employees.active)
    existing_reviewer_employee_ids = @reviewee.reviewers.where(
      reviewer_type_id: params[:reviewer_type_id],
    ).pluck(:employee_id)
    employees = employees.where.not(id: existing_reviewer_employee_ids)

    render json: employees, each_serializer: EmployeeMiniSerializer
  end

  private

  def calibrate_review_response_params
    params.permit(:review_cycle_period, :reviewer_type_id, :passed_review_cycle_id)
  end

  def update_calibrated_response_params
    params.permit(
      :id, :response_id, :calibration_column_id, :calibrated_score, :calibrated_response_text
    ).merge(current_employee_id: current_user.employee.id, review_cycle_id: @review_cycle.id)
  end

  def reviewees_api_type
    if params.key?(:reviewee_scope)
      params.require(:reviewee_scope)
    end
  end

  def update_reviewee_status_params
    params.require(:reviewee).permit(:self_review_done, :peer_approval_done, :peer_selection_done,
                                     :manager_summary_done, :release_review_done, :goal_approval_done, :confidential,
                                     :goal_selection_done)
  end

  def reviewee_manager_params
    if params.key?(:reviewee_manager)
      params[:reviewee_manager]
    end
  end

  def reviewee_update_employee
    params.key?(:update_employee) &&
      (params[:update_employee] == true || params[:update_employee] == 'true' || params[:update_employee] == '1' || params[:update_employee] == 1)
  end

  def nominate_peers_params
    params.permit(:nominator_role, nominate_peers: [])
  end

  def create_reviewee_params
    params.fetch(:reviewee).permit(:employee_id).merge({ review_cycle: @review_cycle })
  end

  def update_reviewee_params
    params.require(:reviewee)
      .permit(:custom_attribute, { custom_variables: {} }).tap do |reviewee_params|
        reviewee_params[:custom_variables] = (@reviewee.custom_variables.presence || {})
          .merge(reviewee_params[:custom_variables].presence || {}).presence
      end
  end

  def update_all_params
    params.permit(reviewees: [:employee_id])['reviewees'].map do |reviewee|
      reviewee.merge(review_cycle_id: @review_cycle.id, manager_id: Employee.find(reviewee['employee_id']).manager_id)
    end
  end

  def load_reviewee
    @reviewee = @review_cycle.reviewees.find(params[:id])
  end

  def load_review_cycle
    @review_cycle = ReviewCycle.find(params[:review_cycle_id])
  end

  def scoped_reviewees
    policy_scope(@review_cycle.reviewees)
  end
end
