class EmployeesController < ApiController
  include Calculator
  before_action :set_employee, except: :create_custom_homepage_actions
  before_action :load_account

  def slack_mapping
    authorize @account, policy_class: AccountPolicy
    slack_mapping = Slack::FetchAccountSlackMapping.call(
      {
        account_id: @account,
        search_params: params[:search] || {},
      },
    )
    render json: slack_mapping, status: :ok
  end

  def resync_slack_mapping
    authorize @account, policy_class: AccountPolicy
    FetchSlackUsersDetails.call(@account.slack_team.id) if @account.slack_team.present?
    render status: :ok
  end

  def ms_teams_mapping
    authorize @account, policy_class: AccountPolicy
    ms_teams_mapping = Teams::FetchAccountMsTeamsMapping.call(
      {
        account_id: @account&.id,
        search_params: params[:search] || {},
      },
    )
    render json: ms_teams_mapping, status: :ok
  end

  def index
    authorize @account, policy_class: GoalPolicy

    employees = @account.employees.includes(
        :user, :manager, :department, :omnichat_user, :performance_ratings,
        idp_track_position: [idp_track: :idp_team]
      ).order(created_at: :desc)

    employees = employees.where(filters_param) if params[:filters].present?
    if params[:search]
      employees = employees.where('full_name LIKE ?',
                                  "%#{params[:search]}%").limit(params[:limit].presence || 50)
    end
    employees = employees.where(date_of_joining: tenure_ranges(params[:filters][:tenure])) if params[:filters] && params[:filters][:tenure].present?
    status = params[:status] || 'active'
    employees = employees.where(billing_status: status) unless params[:status] == 'all'
    employees = employees.limit(params[:limit]) if params[:limit].present?

    if params[:source] == 'survey_audience'
      employees = employees.active
      render json: employees, each_serializer: EmployeeSurveySerializer
    else
      render json: employees
    end
  end

  def select_list
    authorize @account, :employee_list?, policy_class: AccountPolicy
    employees_data = Employee::SelectList.call(account: @account, current_user: current_user, params: params)
    if employees_data.success?
      render json: employees_data.result
    else
      render_unprocessable_entity(employees_data.errors.full_messages.join(', '))
    end
  end

  def list_employees
    authorize @account, :employees_data?, policy_class: AccountPolicy

    employees_data = EmployeeList.call(account: @account, current_user: current_user, params: params)
    if employees_data.success?
      render json: employees_data.result
    else
      render_unprocessable_entity(employees_data.errors.full_messages.join(', '))
    end
  end

  # This method uses bad approach to find the resource.
  # Here we are using :emp_id to find the employee instead if using :id.
  # This is very bad and needs to be fixed.
  # Reason this is bad: Now there are 3 employee objects that we need to check for security and authorization:
  #   - Current user's employee from the auth_token
  #   - Employee found with :id param
  #   - Employee found with :emp_id param
  def update_employees
    emp = Employee.find(params[:emp_id])
    old_email = emp.user.email
    authorize emp
    relationship_updated = employee_params[:manager_id].present? ? true : false
    emp_update_params = employee_params.merge(relationship_updated: relationship_updated)
    if emp.update(emp_update_params) && update_idp_position(emp)
      audit_email_change(emp, old_email, params)
      render json: emp, status: :ok
    else
      render json: emp.errors.to_json(full_messages: true), status: :unprocessable_entity
    end
  end

  def update_billing_status
    emp = Employee.find(params[:emp_id])
    render_forbidden('Access Denied') and return if emp.account_id != @employee.account_id

    if update_billing_status_params[:billing_status].nil? || emp.nil?
      render_bad_request('Wrong input value') and return
    end

    result = EmployeeBilling::UpdateBillingStatus.call(emp.id, update_billing_status_params[:billing_status])
    render_with_status(result[:message], result[:status]) if result
  end

  def product_feedback_form
    # get the feedback question for the employee
    feedback_json = FetchFeedbackQuestions.call(@employee.id, product_feedback_params[:category])
    render json: feedback_json, status: :ok
  end

  def get_nps_keys
    render json: {
      goals_nps_feedback_question: load_nps_product_feedback_question('goals'),
      biz_reviews_nps_feedback_question: load_nps_product_feedback_question('biz_reviews'),
    }, status: :ok
  end

  def nps_product_feedback_form
    # get the NPS feedback question - goals & Biz review for the employee
    unanswered_questions = FetchNpsFeedbackQuestions.call(@employee.id, nps_product_feedback_params[:question])
    render json: unanswered_questions, status: :ok
  end

  def update_employee_question_status
    authorize @account, policy_class: AccountPolicy
    unless update_employee_question_status_params[:id] && update_employee_question_status_params[:status]
      render_bad_request('Missing parameter') and return
    end

    @employee_question_status = EmployeeQuestionStatus.find(update_employee_question_status_params[:id])
    render_not_found('Employee question status object not found') and return if @employee_question_status.nil?

    question_status = update_employee_question_status_params[:status]
    @employee_question_status.update!(
      status: question_status,
      next_trigger_date: question_status == 'skipped' ? Date.today + 30 : nil,
    )
    render json: { employee_question_status: @employee_question_status }, status: :ok
  end

  def submit_product_feedback
    # saving the response of the employee for feedback question
    authorize @account, policy_class: AccountPolicy

    unless params['question_id'] && (params['nps_score'] || params['comments'])
      render_bad_request('Missing parameter') and return
    end

    render json: { feedback_response: SubmitProductFeedback.call(@employee.id, params) }, status: :ok
  end

  def update_product_feedback
    authorize @account, policy_class: AccountPolicy

    unless params[:id] && (params[:comments] || params[:nps_score])
      render_bad_request('Missing parameter') and return
    end

    render json: { employee_satisfaction: UpdateProductFeedback.call(@employee.id, params) }, status: :ok
  end

  # No longer being used
  # def invite
  #   authorize @employee
  #   to_employees = Employee.find(params[:id])
  #   InviteEmployee.delay.call(to_employee, @employee)
  #   to_employee.reload
  #   render json: to_employee
  # end

  def create_bulk_employees
    authorize @account, policy_class: AccountPolicy
    employee_arr = []
    employees = employees_params[:employees]

    # Except Production env all other staging env having invalid chargebee_sub_id
    update_subscription(employees.count) if Rails.env.production?

    employees.each do |employee|
      CreateEmployee.call(
        email: employee['email'],
        name: employee['name'],
        account: @account,
        department_id: employee['department_id'],
        invited: false,
      )
    end
    employees.each do |employee|
      employee_arr << UpdateManager.call(@account, employee)
    end

    InviteEmployees.delay(queue: 'high').call(employee_arr.map(&:id), @employee) if params['to_be_invited'] == 'true'
    CreateGoalFiltersForNewEmployees.call(@account, @employee, employee_arr)
    CreateGoalFiltersForNewEmployees.delay(queue: 'low').call(@account, @employee, employee_arr, for_admin: false)
    render json: employee_arr.compact.uniq, each_serializer: EmployeeSerializer
  end

  def reportee_list
    authorize @account, policy_class: AccountPolicy
    reportees = @employee.relationship_updated_reports
    render json: reportees
  end

  def upload_profile_picture
    authorize @employee
    params[:profile_pic].nil? ? @employee.remove_profile_picture = true : @employee.profile_picture = params[:profile_pic]
    @employee.save!
    render json: @employee
  end

  def show
    authorize @employee, :employee_show?
    emp = Employee.find(params[:id])
    render json: emp, serializer: Base::KpiBoard::EmployeeSerializer
  end

  def create_custom_homepage_actions
    emp = Employee.find_by(id: params[:id])
    return render_not_found('Employee not found') if emp.blank?

    authorize emp
    result = HomePageActions::CreateCustomHomepageAction.call(account: @account.id, employee: emp.id,
                                                              params: custom_homepage_params)

    if result.success?
      render_success('Custom homepage action created successfully')
    else
      render_unprocessable_entity(result.errors)
    end
  end

  private

  def set_employee
    @employee = current_user.employee
  end

  def load_account
    @account = Account.find(params['account_id'])
  end

  def employee_params
    allowed_params = [:full_name, :first_name, :date_of_joining, :gender, :location,
                      :send_manager_checkin, :invited, :age, :title, :date_of_exit,
                      :date_of_joining, :personal_email_address, :phone_number,
                      :level, :business_unit, :division, :employment_type,
                      :company_employee_id, :search, :timezone, :manager_id, :department_id,
                      :relationship_updated, :org_role, { user_attributes: [:email] }]

    unless @employee.admin?
      # Temp change only.
      # If employee is not admin then it should not be able to change the following attributes.
      deletable_attrs = [:manager_id, :department_id, :relationship_updated, :org_role, { user_attributes: [:email] }]
      allowed_params.delete_if { |e| deletable_attrs.include?(e) }
    end

    permitted_params = params.require(:employee).permit(allowed_params)

    [:full_name, :first_name, :title, :level, :gender, :location].each do |key|
      next unless permitted_params.key?(key) && permitted_params[key].present?

      permitted_params[key] = permitted_params[key].gsub(/[<>~`!#$%^*()={}'"]/, '')
    end

    permitted_params
  end

  # `~!#$%^*()={}[]'"<>
  def audit_email_change(emp, old_email, _params)
    new_email = emp.user.email
    # Here we are only checking if the email was changed. Validations are ran when we filter params and save obj.
    return if new_email == old_email

    payload = {
      previous_email: old_email,
      new_email: emp.user.email,
    }

    audit! :employee_email_update, emp.user, payload: payload
  end

  def custom_homepage_params
    {
      review_cycle_id: params[:review_cycle_id],
      content_params: params.fetch(:content_params, {}).permit(:subtitle, :button_text, :link, :start_date, :end_date),
    }
  end

  def employees_params
    params.permit(:account_id, :id, :status, employees: [:name, :email, :manager_email, :department_id])
  end

  def filters_param
    params.require(:filters).permit(manager_id: [], department_id: [], location: [], org_role: [])
  end

  def update_subscription(added_employees_size)
    subscription = @account.active_subscription
    plan_quantity = @account.employees.active.size + added_employees_size
    return if subscription.max_seats >= plan_quantity

    if auto_debit_eligible?(@account.active_subscription&.chargebee_subscription_id)
      response = ChargeBee::Subscription.update(subscription.chargebee_subscription_id, {
                                                  plan_quantity: plan_quantity,
                                                  end_of_term: false,
                                                })
      message_data = if response&.subscription.plan_quantity&.to_i == plan_quantity
                       {
                         title: "Updated Subscription Plan Quantity for #{@account&.company_name}",
                         company_name: @account&.company_name,
                         plan_quantity: @account.active_subscription&.max_seats,
                         chargebee_subscription_id: @account.active_subscription&.chargebee_subscription_id,
                         status: ':white_check_mark:',
                         message: 'Plan Quantity Updated.',
                       }
                     else
                       {
                         title: "Updation of Plan Quantity Failed for #{@account&.company_name}",
                         company_name: @account&.company_name,
                         plan_quantity: @account.active_subscription&.max_seats,
                         chargebee_subscription_id: @account.active_subscription&.chargebee_subscription_id,
                         status: ':x:',
                         message: 'Plan Update having some issue.',
                       }
                     end
    else
      message_data = {
        title: "Updation of Plan Quantity Failed for #{@account&.company_name}",
        company_name: @account&.company_name,
        plan_quantity: @account.active_subscription&.max_seats,
        chargebee_subscription_id: @account.active_subscription&.chargebee_subscription_id,
        status: ':x:',
        message: "Customer tried to Activate #{added_employees_size} Employees, Card Not Added or Auto Collection turned off.",
      }
    end
    EmployeeBilling::SendBillingUpdateSlackNotification.delay(queue: 'high').call(message_data)
  end

  def nps_product_feedback_params
    params.permit(:question)
  end

  def product_feedback_params
    params.permit(:category)
  end

  def update_employee_question_status_params
    params.require(:employee_question_status).permit(:id, :status)
  end

  def update_billing_status_params
    permitted_params = params.require(:employee).permit(:billing_status)

    # Restrict billing_status to allowed values
    permitted_params[:billing_status] = nil unless %w[active inactive
                                                      to_be_activated].include?(permitted_params[:billing_status])

    permitted_params
  end

  def load_nps_product_feedback_question(question_type)
    response = FetchNpsFeedbackQuestions.call(@employee.id, question_type)
    response[:feedback_questions].present?
  end

  def auto_debit_eligible?(chargebee_subscription_id)
    return true if @account.active_subscription&.in_trial?

    response =
      begin
        ChargeBee::Subscription.retrieve(chargebee_subscription_id)
      rescue StandardError
        nil
      end
    response&.customer.card_status == 'valid' && response&.customer.auto_collection == 'on'
  end

  def update_idp_position(emp)
    return true unless params[:employee].has_key?(:idp_track_position_id)

    idp_track_position_id = params[:employee][:idp_track_position_id]
    return true if EmployeeIdpTrackPosition.exists?(employee_id: emp.id, idp_track_position: idp_track_position_id, current: true)

    response = EmployeeIdpTrackPosition::CreateOrUpdate.call(
      current_employee: @employee, employee: emp, new_position_id: idp_track_position_id
    )
    return true if response.success?

    emp.errors.add(:base, response.errors.full_messages.join(', '))
    false
  end
end
