class ReviewerTypesController < ApiController
  before_action :load_account, :load_review_cycle
  before_action :load_reviewer_type, only: [:show]

  def index
    authorize ReviewerType
    render json: { reviewers: reviewers }
  end

  def reviewer_type_options
    authorize @review_cycle
    phase_list = ReviewerType::DEFAULT_REVIEWER_TYPES.map do |reviewer_type|
      ReviewerType::REVIEWER_TYPE_META[reviewer_type]
    end

    render json: phase_list
  end

  def create
    authorize @review_cycle, policy_class: ReviewCyclePolicy

    create_reviewer_types = PerformanceReview::WebPerformanceReview::CreateReviewerTypes.call(
      @review_cycle.id,
      reviewer_types_params.to_hash.with_indifferent_access,
    )

    if create_reviewer_types.success?
      render json: @review_cycle.reviewer_types.normal
    else
      render json: { error: create_reviewer_types.errors.values.join('. ') }, status: :bad_request
    end
  end

  private

  def load_review_cycle
    @review_cycle = ReviewCycle.find(params[:review_cycle_id])
  end

  def load_reviewer_type
    @reviewer_type = ReviewerType.find(params[:reviewer_type_id])
  end

  def reviewer_types_params
    params.require(:reviewer_types_summary).permit(
                    goals_selection_by_reviewee: {},
                    goals_approval_by_manager: {},
                    peer_review: [:enabled, :min_reviewers, :max_reviewers, :limit_to_participants, :approval_role,
                                  :approval_required, :selection_role, :selection_required],
                    write_self_review: {},
                    write_peer_review: {},
                    write_direct_report_review: {},
                    write_manager_summary_review: {},
                    goal_in_self_review: {},
                    goal_in_manager_review: {},
                    goal_in_peer_review: {},
                    goal_in_direct_report_review: {},
                    goal_in_custom_review: [
                      :reviewer_type_id,
                      :enabled,
                    ],
                    goals_selection_and_approval_by_manager: {},
                    auto_create_one_on_one: {},
                    review_visibility: {},
                    define_goal_weights: {},
                    release_reviews: {},
                    competency_configs: [:enabled, :is_weights_enabled],
                    custom_reviewer_types: [
                      :reviewer_type_id,
                      :reviewer_label,
                      :reviewee_label,
                      :can_read_reviews,
                      :can_review_anyone,
                      :standalone_launch,
                    ],
                    calibration_view: {},
                    goal_configs: [
                      :goal_visibility, :review_cycle_goal_type, :minimum_goal_weightage,
                      :maximum_goal_weightage, :allow_zero_weightage_goals
                    ],
                    enforce_manager_change: [:enabled, :end_date],
                  )
  end
end
