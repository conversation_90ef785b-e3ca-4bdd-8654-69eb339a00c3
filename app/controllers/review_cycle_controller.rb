class ReviewCycleController < ApiController
  include RevieweeDataConcern # reviewee_data_concern

  before_action :load_account
  before_action :load_review_cycle, except: [:sync_google_sheets, :show, :index, :destroy, :create]
  before_action :soft_load_review_cycle,
                only: [:show, :destroy, :release_review, :calibration_table, :add_calibration_question,
                       :get_addable_reviewees_list, :validate_goal_weightage_range]
  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized

  def release_review
    authorize @review_cycle
    reviewee = Reviewee.find(params[:reviewee_id])
    @reviewee_employee = reviewee&.employee
    current_reviewer_type = reviewee.reviewers.where(employee: @employee)&.first&.reviewer_type
    authorized = false

    if @review_cycle.release_summary? &&
        (reviewee.manager == @employee || @reviewee_employee.manager == @employee) &&
        current_reviewer_type.reviewer_type == 'manager'
      authorized = true
    end

    if authorized
      manager_reviewer = reviewee.reviewers
        .joins(:reviewer_type)
        .where(reviewer_types: { reviewer_type: 'manager' })
        .where(employee: @employee)
        .first

      Slack::V2::Submissions::PerformanceReviewReleaseActions.call(@review_cycle, manager_reviewer)
      if reviewee.reload.release_review_done
        PerformanceReview::WebPerformanceReview::CreateReviewCycleHomePageActions.delay(queue: 'immediate').call(
          {
            review_cycle_id: @review_cycle.id,
            employee_ids: [reviewee.employee_id],
            review_cycle_phase_name: 'release_review_done',
            review_cycle_phase_id: @review_cycle.review_cycle_phases.find_by(phase_type: 'release_reviews')&.id,
          },
        )
        render_success('Review Released Successfully')
      else
        error_message = "There was an issue releasing review. <NAME_EMAIL>, we'll help you fix it."
        render_unprocessable_entity(error_message)
      end
    else
      error_message = "You are not authorized to release this review. Please contact your review administrator #{@review_cycle.creator.user.email}"
      render_forbidden(error_message)
    end
  end

  def destroy
    authorize @review_cycle
    @review_cycle.discard
    render json: { response: 'Review cycle has be discarded' }, status: :ok
  end

  def show
    authorize @review_cycle
    case review_cycle_intent
    when 'write_review', nil
      render json: { review_cycle: ReviewCycleSerializer.new(@review_cycle,
                                                             current_employee: current_user.employee).to_h }
    when 'manage_review'
      render json: { review_cycle: ReviewCycleManageSerializer.new(
        @review_cycle,
        current_employee: current_user.employee,
        fields: params[:fields]&.split(','),
      ).to_h }
    when 'manage_goals'
      render json: {
        review_cycle: ReviewCycleManageSerializer.new(
          @review_cycle,
          current_employee: current_user.employee,
          fields: %i[id title start_date status show_key_results goal_cycles],
        ).to_h,
      }
    end
  end

  def duplicate
    authorize @review_cycle
    if duplicate_review_cycle_params.key?('title') &&
        duplicate_review_cycle_params['title'].length.positive?
      @duplicated_review_cycle = PerformanceReview::DuplicateReviewCycle.call(@review_cycle,
                                                                              duplicate_review_cycle_params['title'])
      render json: { review_cycle: ReviewCycleManageSerializer.new(@duplicated_review_cycle,
                                                                   current_employee: current_user.employee).to_h }
    else
      render json: { status: 'Title is required' }
    end
  end

  def send_review_cycle_zip_to_admin
    authorize @review_cycle

    current_employee = current_user.employee
    if current_employee.admin? || current_employee.acl_role_admin?(product_action_keys: [:reviews_grant_all_permissions])
      # Identify user role ( reviewee_id can will be passed nil from here, for review packet pdfs zip generation)
      role_service = get_user_role

      # Check if user role identification was successful
      return render_forbidden(role_service.errors.full_messages.join(', ')) unless role_service.success?

      user_role = role_service.result

      PerformanceReview::SendReviewCycleZipToAdmin.delay(queue: 'high').call(@review_cycle.id, current_employee.id, user_role, {})
      render_success('Request Queued')
    else
      render_forbidden
    end
  end

  def create
    authorize ReviewCycle
    create_params = review_cycle_params
    create_params.merge!(account: current_user.employee.account,
                         creator: current_user.employee)
    rc_creation = ReviewCycles::Create.call(create_params)
    if rc_creation.success?
      @review_cycle = rc_creation.result
      render json: ReviewCycleManageSerializer.new(@review_cycle, current_employee: current_user.employee).to_h,
             status: :created
    else
      render json: rc_creation.errors.full_messages, status: :unprocessable_entity
    end
  end

  def update
    authorize @review_cycle, :manage_archived?
    update_review_cycle = PerformanceReview::WebPerformanceReview::UpdateReviewCycle.call(
      review_cycle_params.merge(id: @review_cycle.id), params[:step_name]
    )

    if update_review_cycle.success?
      render json: ReviewCycleManageSerializer.new(
        update_review_cycle.result,
        current_employee: current_user.employee,
        fields: params[:fields].to_s.split(',').map(&:strip).compact,
      ).to_h
    else
      render json: update_review_cycle.errors, status: :unprocessable_entity
    end
  end

  def index
    # use headless policy
    # passing a symbol :review_cycle
    # all admin only, creator yes but given no specific rc, not specific
    authorize :review_cycle, :review_cycle_index?

    serialized_review_list = PerformanceReview::WebPerformanceReview::Index.call(
      current_user.employee,
      review_cycle_list_params,
    )

    render json: serialized_review_list
  end

  def reviewee_report_pdf
    # We are deprecating this in favor of release review pdf.
    # The conditions handled here will be handled there

    redirect_to release_review_pdf_account_review_cycle_path(request.parameters)
  end

  # => Params
  # {
  #   "reviewee_id"=>"101800",
  #   "screen"=>"admin_right_panel",
  #   "controller"=>"review_cycle",
  #   "action"=>"release_review_pdf",
  #   "account_id"=>"430",
  #   "id"=>"2913"
  # }
  def release_review_pdf
    return render_forbidden_pdf_download(@review_cycle) if @review_cycle.archived?

    # Identify user role
    role_service = get_user_role

    # Check if user role identification was successful
    return render_forbidden_pdf_download(@review_cycle, role_service.errors.full_messages.join(', ')) unless role_service.success?

    # Get the result from the command
    user_role = role_service.result

    rc_id, _, _ = resolve_review_cycle_context
    resolved_rc = ReviewCycle.find_by(id: rc_id)

    return render_bad_request('Review Cycle not found.') unless resolved_rc.present?

    if resolved_rc.three_sixty_review?
      review_pdf = PerformanceReview::ReviewPdf::Factory.call(
        params[:reviewee_id], user_role, resolved_rc.review_type.to_sym, { current_employee_id: current_user&.employee&.id }
      )
      raise review_pdf.errors.full_messages.join(', ').presence || 'PDF generation error' if review_pdf.failure?

      pdf_file = review_pdf.result
      send_pdf(pdf_file)
    else
      # Generate Release review with the identified user role
      pdf_service = GenerateReleaseReviewPdfWithUserRole.call(params[:reviewee_id], current_user, user_role)
      # pdf_file = GenerateReleaseReviewPdf.call(params[:reviewee_id], current_user)

      return render_forbidden_pdf_download(resolved_rc, pdf_service.errors.full_messages.join(', ')) unless pdf_service.success?
      send_pdf(pdf_service.result)
    end
  end

  # => <ActionController::Parameters {
  #   "controller"=>"review_cycle", 
  #   "action"=>"get_reviewer_type_tabs",
  #   "account_id"=>"430", 
  #   "id"=>"2934",
  #   "reviewee_id"=>"",
  #   "reviewer_id"=>"",
  #   "role_type"=>"",
  #   "current_rc_only"=>true/false,
  # } permitted: false>
  # WILL BE USED AFTER STANDARDISATION OF APIs RELEASE
  def get_reviewer_type_tabs
    authorize @review_cycle, :soft_load_review_cycle?, policy_class: ReviewCyclePolicy

    # Identify user role using our new service class
    role_service = get_user_role

    # Check if user role identification was successful
    return render_unprocessable_entity(role_service.errors.full_messages.join(', ')) unless role_service.success?

    # Get the result from the command
    user_role = role_service.result

    tabs_service = PerformanceReview::WebPerformanceReview::GetReviewerTypeTabs.call(
      {
        reviewee_id: params[:reviewee_id],
        reviewer_id: params[:reviewer_id],
        fetch_current_rc_only: params[:current_rc_only] == 'true',
        current_employee_id: current_user.employee&.id,
        user_role: user_role,
      },
    )

    if tabs_service.success?
      render json: { reviewer_types: tabs_service.result }, status: :ok
    else
      render_unprocessable_entity(tabs_service.errors.full_messages.join(', '))
    end
  end

  def sync_google_sheets
    authorize @account, :sync_review_google_sheets?
    sheet_id = params[:sheet_id]
    settings_range = params[:settings_range]
    reviewer_types_range = params[:reviewer_types_range]
    participants_range = params[:participants_range]
    questions_range = params[:questions_range]
    preset_reviewers_range = params[:preset_reviewers_range]

    SyncReviewSheet.delay(queue: 'high').call(
      @account.id, sheet_id, settings_range, reviewer_types_range, participants_range,
      questions_range, preset_reviewers_range
    )
  end

  def get_manager_list
    return if filter_reviewees_by_scope(reviewee_filter_params['reviewee_scope'], scoped_reviewees).blank?

    filter_reviewees_by_scope(reviewee_filter_params['reviewee_scope'], scoped_reviewees)
      .includes(employee: :manager)
      .map { |reviewee| reviewee.employee.manager }
      .compact.uniq
      .map { |mgr| { id: mgr.id, name: mgr.full_name } }
  end
  
  def get_department_list
    return if filter_reviewees_by_scope(reviewee_filter_params['reviewee_scope'], scoped_reviewees).blank?

    filter_reviewees_by_scope(reviewee_filter_params['reviewee_scope'], scoped_reviewees)
      .includes(employee: :department)
      .map(&:employee).uniq.compact.map do |emp|
      if emp.department&.name.present?
        { id: emp.department.id, name: emp.department.name }
      end
    end.compact.uniq
  end
  
  def reviewee_filters
    # Returns the filters applicable
    # TODO Authorize review cycle
    # TODO Order ascending
    authorize @review_cycle
    filters = {}
    managers = get_manager_list
    filters[:managers] = { values: managers, search: true } if managers.present?
  
    departments = get_department_list
    filters[:departments] = { values: departments, search: true } if departments.present?
  
    peer_phases = %w[peer_approval peer_selection]
    selected = peer_phases.select { |phase| @review_cycle.review_cycle_phases.map(&:phase_type).include?(phase) }
    if selected.present?
      selected.unshift 'not_started'
      filters.merge!(peers: { values: selected, search: false })
    end

    goal_phases = %w[goal_approval goal_selection]
    selected = goal_phases.select { |phase| @review_cycle.review_cycle_phases.map(&:phase_type).include?(phase) }
    if selected.present?
      selected.unshift 'not_started'
      filters.merge!(goals: { values: selected, search: false })
    end

    competency_phases = %w[competency_approval competency_selection]
    selected = competency_phases.select { |phase| @review_cycle.review_cycle_phases.map(&:phase_type).include?(phase) }
    if selected.present?
      selected.unshift 'not_started'
      filters.merge!(competencies: { values: selected, search: false })
    end

    self_review_phases = ['self']
    selected = self_review_phases.select { |phase| @review_cycle.reviewer_types.map(&:reviewer_type).include?(phase) }
    if selected.present?
      selected.unshift 'pending'
      filters.merge!(self_review: { values: selected, search: false })
    end

    manager_summary_phases = ['manager_summary']
    selected = manager_summary_phases.select do |phase|
      @review_cycle.review_cycle_phases.map(&:phase_type).include?(phase)
    end
    if selected.present?
      selected.unshift 'pending'
      filters.merge!(manager_summary: { values: selected, search: false })
    end

    release_reviews_phases = ['release_reviews']
    selected = release_reviews_phases.select do |phase|
      @review_cycle.review_cycle_phases.map(&:phase_type).include?(phase)
    end
    if selected.present?
      selected.unshift 'pending'
      filters.merge!(release_reviews: { values: selected, search: false })
    end

    render json: { reviewee_filters: filters }
  end

  def reviewee_filters_calibration
    # Returns the filters applicable for calibration
    authorize @review_cycle
    filters = PerformanceReview::Calibration::RevieweeFilters.call(@current_user, @review_cycle)

    render json: { reviewee_filters: filters }
  end

  # GET a list of reviewees that can be added to a review cycle, this API is used to add reviewees
  def get_addable_reviewees_list
    authorize @review_cycle, :get_addable_reviewees_list?, policy_class: ReviewCyclePolicy
    addable_reviewees = Pundit.policy_scope!(current_user, Employee.active.where(account: @review_cycle.account))
      .where.not(
        id: @review_cycle.reviewees.pluck(:employee_id),
      ) # excluding current reviewees
    render json: addable_reviewees, each_serializer: EmployeeMiniSerializer
  end

  # GET a list of calibratable questions, only manager questions can be calibrated
  def get_calibratable_questions
    authorize @review_cycle, :get_calibratable_questions?, policy_class: ReviewCyclePolicy
    calibratable_questions = PerformanceReview::WebPerformanceReview::GetCalibratableQuestions.call(@review_cycle.id)

    if calibratable_questions.success?
      render json: calibratable_questions.result
    else
      render_bad_request(calibratable_questions.errors.full_messages)
    end
  end

  def send_write_review_email
    authorize @review_cycle
    PerformanceReview::WriteReviewEmailNotification.call(@review_cycle.id, employee_ids)
  end

  def send_write_review_email_reminder
    authorize @review_cycle
    PerformanceReview::WriteReviewEmailNotification.call(@review_cycle.id, employee_ids, true)
  end

  def send_write_review_manager_email
    authorize @review_cycle
    PerformanceReview::WriteReviewManagerEmailNotification.call(@review_cycle.id, employee_ids)
  end

  def send_write_review_manager_email_reminder
    authorize @review_cycle
    PerformanceReview::WriteReviewManagerEmailNotification.call(@review_cycle.id, employee_ids, true)
  end

  def send_peer_selection_email_reminder
    authorize @review_cycle
    PerformanceReview::PeerSelectionEmailNotification.delay(queue: 'immediate').call(@review_cycle.id, employee_ids,
                                                                                     true)
  end

  def send_peer_selection_email
    authorize @review_cycle
    PerformanceReview::PeerSelectionEmailNotification.delay(queue: 'immediate').call(@review_cycle.id, employee_ids)
  end

  def send_peer_approval_email_reminder
    authorize @review_cycle
    PerformanceReview::PeerApprovalEmailNotification.delay(queue: 'immediate').call(@review_cycle.id, employee_ids,
                                                                                    true)
  end

  def send_peer_approval_email
    authorize @review_cycle
    PerformanceReview::PeerApprovalEmailNotification.delay(queue: 'immediate').call(@review_cycle.id, employee_ids)
  end

  def send_goal_approval_email_reminder
    authorize @review_cycle
    PerformanceReview::GoalApprovalEmailNotification.delay(queue: 'immediate').call(@review_cycle.id, employee_ids,
                                                                                    true)
  end

  def send_goal_approval_email
    authorize @review_cycle
    PerformanceReview::GoalApprovalEmailNotification.delay(queue: 'immediate').call(@review_cycle.id, employee_ids)
  end

  def send_goal_selection_email
    authorize @review_cycle
    PerformanceReview::GoalSelectionEmailNotification.delay(queue: 'immediate').call(@review_cycle.id, employee_ids,
                                                                                     params[:reminder].present?)
  end

  def create_review_cycle_home_page_actions
    authorize @review_cycle
    return if @review_cycle.archived?

    phase = review_cycle_phase
    return if phase.nil?

    PerformanceReview::WebPerformanceReview::CreateReviewCycleHomePageActions.delay(queue: 'immediate').call(
        {
          review_cycle_id: @review_cycle.id,
          employee_ids: employee_ids,
          review_cycle_phase_name: phase[:review_cycle_phase_name],
          review_cycle_phase_id: phase[:review_cycle_phase_id],
        },
      )
  end

  def send_peer_selection_notification
    authorize @review_cycle
    Slack::PerformanceReviewActions::PeerSelectionNotification.delay(queue: 'immediate')
      .call(@review_cycle.id, employee_ids)
  end

  def send_peer_selection_reminder
    authorize @review_cycle
    Slack::PerformanceReviewActions::PeerSelectionReminderNotification.delay(queue: 'immediate')
      .call(@review_cycle.id, employee_ids)
  end

  def send_peer_approval_notification
    authorize @review_cycle
    Slack::PerformanceReviewActions::ManagerPeerApprovalNotification.delay(queue: 'immediate')
      .call(@review_cycle.id, employee_ids)
  end

  def send_manager_approval_notification
    authorize @review_cycle
    Slack::PerformanceReviewActions::ManagerPeerApprovalNotification.delay(queue: 'immediate')
      .call(@review_cycle.id, employee_ids)
  end

  def send_manager_summary_notification
    authorize @review_cycle
    Slack::PerformanceReviewActions::ManagerSummaryNotification.delay(queue: 'immediate')
      .call(@review_cycle.id, employee_ids)
  end

  def send_manager_goals_approval_notification
    authorize @review_cycle
    Slack::PerformanceReviewActions::ManagerGoalsApprovalNotification.delay(queue: 'immediate')
      .call(@review_cycle.id, employee_ids)
  end

  def convert_to_one_on_one
    authorize @review_cycle
    if params.key? :all && params[:all] == 'true'
      @completed_manager_reviewers = @review_cycle.reviewers
        .joins(:reviewer_type, :reviewee)
        .where(reviewer_types: { reviewer_type: 'manager' }, review_submitted: true)

      employees = @completed_manager_reviewers.map(&:reviewee).map(&:employee)
      ReviewToOneOnOne.delay(queue: 'normal').call(@review_cycle.id, employees.map(&:id))
      render json: employees.map(&:full_name)
    else
      ReviewToOneOnOne.delay(queue: 'normal').call(@review_cycle.id, employee_ids)
      render json: { status: 'OK' }
    end
  end

  def calibration_table
    # returns the data for calibration table for review cycle
    authorize @review_cycle
    calibration_data = PerformanceReview::Calibration::TableData.call(calibration_table_params)
    if calibration_data.success?
      render json: calibration_data.result
    else
      render_unprocessable_entity(calibration_data.errors.full_messages)
    end
  end

  def calibration_table_csv
    authorize @review_cycle
    PerformanceReview::Calibration::TableCsv.delay(queue: 'high').call(
      calibration_table_params.merge(should_send_email: true),
    )

    render head: :ok
  end

  def add_reviewee
    # add a new reviewee to a review cycle
    authorize @review_cycle, :add_reviewee?
    added_reviewee = PerformanceReview::WebPerformanceReview::AddReviewee.call(
      @review_cycle.id, params[:employee_id], params[:manager_id]
    )
    if added_reviewee.success?
      render json: { message: 'reviewee added', reviewee: added_reviewee.result }
    else
      render_unprocessable_entity('Could not add reviewee')
    end
  end

  def add_reviewers
    authorize @review_cycle
    added_reviewers = PerformanceReview::WebPerformanceReview::AddReviewers.call(
      add_reviewers_params,
    )

    if added_reviewers.success?
      render json: { reviewers: added_reviewers.result }
    else
      render_unprocessable_entity(added_reviewers.errors.full_messages)
    end
  end

  def creation_progress
    authorize @review_cycle
    result = PerformanceReview::WebPerformanceReview::CreationProgress.call(@review_cycle.id)
    render_with_status(result[:message], result[:status])
  end

  def export_reviewees_competency_errors
    authorize @review_cycle, :add_reviewee?
    export_errors = RevieweeCompetency::ValidateReviewees.call(@review_cycle, true)

    if export_errors.success?
      send_data export_errors.result,
                filename: "#{@account.company_name}-#{@review_cycle.title}-RevieweeCompetencyErrors.csv"
    else
      render_unprocessable_entity(export_errors.errors.full_messages)
    end
  end

  def competency_scores
    authorize @review_cycle
    result = PerformanceReview::WebPerformanceReview::CompetencyResponses.call(
      @review_cycle, current_user.employee
    )

    if result.success?
      render json: result.result
    else
      render_unprocessable_entity(result.errors.full_messages)
    end
  end

  def validate_goal_weightage_range
    authorize @review_cycle

    service = PerformanceReview::WebPerformanceReview::ValidateGoalWeightageRange.call(review_cycle_weightage_params)

    if service.success?
      render json: service.result
    else
      render_unprocessable_entity(service.errors.full_messages)
    end
  end

  private

  def get_user_role
    # This method determines the user's role for the current or past review cycle.
    # Depending on the screen (Admin, Manager, Employee), the required params differ.
    # Reference:
    # - Admin Screen: id, reviewee_id
    # - Manager Right Panel: id, reviewer_id, reviewee_id
    # - Manager Your Org: id, reviewee_id
    # - Release Review Screen: id, reviewee_id
    # - Employee Profile: id, reviewee_id
  
    rc_id, reviewee, reviewer = resolve_review_cycle_context
    params.merge!(review_cycle_id: rc_id)

    # Identify the user role using the IdentifyReviewCycleUserRole service.
    service = PerformanceReview::IdentifyReviewCycleUserRole.call(current_user&.employee, params)

    # If the role identification failed, return the service with failure.
    return service unless service.success?
    
    # If the reviewer is accessing a past RC (i.e., reviewee != reviewer.reviewee):
    if reviewer.present? && reviewer.reviewee != reviewee
      user_role = service.result
      role_type = user_role&.role_type == 'custom' ? 'manager' : user_role&.role_type
      
      # Build new params specifically for past RC context
      temp_params = {
        reviewee_id: params[:reviewee_id],
        role_type: role_type,
        past_rc_access: true
      }

      # Identify the user role again for the past RC context
      service = PerformanceReview::IdentifyReviewCycleUserRole.call(current_user&.employee, temp_params)
    end

    service
  end

  def resolve_review_cycle_context
    @reviewer ||= Reviewer.find_by(id: params[:reviewer_id])
    @reviewee ||= if params[:reviewee_id].present?
                    Reviewee.find_by(id: params[:reviewee_id])
                  else
                    @reviewer&.reviewee
                  end
  
    @rc_id ||= if @reviewer.present?
                 params[:id]
               else
                 @reviewee&.review_cycle_id || params[:id]
               end
  
    [@rc_id, @reviewee, @reviewer]
  end

  def review_pdf_authorization_and_anonymization
    reviewee = Reviewee.find(params[:reviewee_id])
    reviewee_employee = reviewee.employee
    current_reviewer_type = reviewee.reviewers.where(employee: @employee).first&.reviewer_type
    authorized = false
    anonymize = true

    reviewee_access = (reviewee_employee == @employee) && (@review_cycle.release_summary? || reviewee.release_review_done?)
    manager_access = reviewee.manager == @employee
    dotted_manager_access = current_reviewer_type&.reviewer_type == 'dotted_manager'
    admin_access = ((@review_cycle&.creator == @employee) || @employee.admin? || @employee.acl_role_admin?(product_action_keys: [:reviews_grant_all_permissions])) &&
      reviewee.in?(scoped_reviewees) &&
      (reviewee.try(:confidential) == false)

    if manager_access
      authorized = true
      anonymize = @review_cycle.anonymize_reports?
    elsif reviewee_access || dotted_manager_access || admin_access
      authorized = true
    end

    [authorized, anonymize]
  end

  def render_forbidden_pdf_download(review_cycle, custom_message = nil)
    error_message = custom_message || "You are not authorized to download this pdf. Please contact your review administrator #{review_cycle.creator.user.email}"
    render_forbidden(error_message)
  end

  def employee_ids
    if params.key?(:employee_ids)
      JSON.parse(params[:employee_ids]).length.positive? ? JSON.parse(params[:employee_ids]) : []
    else
      []
    end
  end

  def review_cycle_phase
    return nil unless params.key?(:review_cycle_phase) && params.key?(:review_cycle_phase_id)

    {
      review_cycle_phase_name: params[:review_cycle_phase],
      review_cycle_phase_id: params[:review_cycle_phase_id],
    }
  end

  def load_review_cycle
    @review_cycle = ReviewCycle.find_by(id: params[:id])
  end

  def soft_load_review_cycle
    @review_cycle = ReviewCycle.find(params[:id])
  end

  def load_account
    @employee = current_user.employee
    @account = @employee.account
  end

  def review_cycle_intent
    if params.key?(:intent)
      params.require(:intent)
    end
  end

  def review_cycle_list_params
    params.permit(:list, :query, :page, :limit)
  end

  def review_cycle_params
    raw_params = params.require(:review_cycle).permit(
      :title, :review_type, :start_date, :status, :show_key_results,
      :archive, :end_date, :default_channel, :automation_enabled,
      enforce_manager_change: [:enabled, :end_date],
      charts_metadata: { labels: {} }
    )

    if raw_params[:enforce_manager_change].present?
      enforce_change = raw_params[:enforce_manager_change]
      if enforce_change[:enabled] && enforce_change[:end_date].present?
        begin
          parse_time =  ActiveSupport::TimeZone[current_user.employee.account.timezone].parse(enforce_change[:end_date]).end_of_day
          raw_params[:enforce_system_manager_mapping] = parse_time
        rescue ArgumentError
          # Handle invalid date format
          raw_params[:enforce_system_manager_mapping] = nil
        end
      else
        raw_params[:enforce_system_manager_mapping] = nil
      end
      raw_params.delete(:enforce_manager_change)
    end

    raw_params
  end

  def review_cycle_visibility_matrix_params
    params.permit(:platform_type, :has_same_visibility_config, web_config_changes: {}, pdf_config_changes: {})
  end

  def duplicate_review_cycle_params
    params.require(:review_cycle).permit(:title)
  end

  def calibration_table_params
    params.permit(
      :managers, :departments, :titles, :locations, :levels, :divisions, :business_units,
      :employment_types, :search_text, :page, :sort_column, :sort_sub_column, :sort_direction
    ).merge(review_cycle_id: @review_cycle.id, current_user_id: current_user.id)
  end

  def user_not_authorized(exception)
    return render json: { error: 'review_archived' }, status: :forbidden if @review_cycle&.archived?

    super(exception)
  end

  def review_cycle_weightage_params
    params.permit(:min_weight, :max_weight)
  end

  def reviewee_filter_params
    params.permit(:reviewee_scope)
  end

  def add_reviewers_params
    params.permit(
      reviewers: [:reviewer_employee_id, :reviewee_id, :reviewer_type_id, :nominator_role],
    ).merge(review_cycle_id: @review_cycle.id, current_employee: current_user.employee)
  end

  def scoped_reviewees
    policy_scope(@review_cycle.reviewees, policy_scope_class: RevieweePolicy::Scope)
  end

  def send_pdf(pdf_file)
    send_data File.open(pdf_file), filename: File.basename(pdf_file)
    File.delete(pdf_file) if File.exist?(pdf_file)
  end
end