# frozen_string_literal: true

class ApiController < ActionController::API
  include Pundit
  include ActionView::Helpers::DateHelper

  SKIP_AUTH_METHODS = %i[
    root version sentiment_analysis
    start_chat next_week_chat email_fail google_notifications microsoft_teams_notifications
    slack_interactive_notifications slack_event_notifications
    coffee_connect_pairings
    coffee_connect_checkins
    wootric_intercom
    slack_message_email_reminder
    slack_message_manager_reminder
    slack_message_ceo_reminder
    send_participation_feedback_survey_reminder
    send_participation_ceo_reminder
    destroy_coffee_connect_pairings
    seed_intrest_tags
    send_talking_points_reminder
    coffee_connect_summary
    slack_options_load_url
    create_review_cycle
    send_initial_pr_notification
    send_manager_pr_notification
    send_write_pr_notification
    reset_calvin_account
    onboarding_details
    google_sheet_update_apna_peoplebox
    gdrive_access
    google_sheet_hris_sync
    sync_to_hubspot
    account_analytics_employees
    send_slack_notification
  ].freeze

  before_action :authenticate_request, except: SKIP_AUTH_METHODS
  # before_action :authorize_product_action!, except: SKIP_AUTH_METHODS # TOOD: Enable when we have multiple product_actions for each product.
  before_action :set_sentry_context
  before_action :set_paper_trail_whodunnit
  before_action :authorize_account_level_access
  attr_reader :current_user, :account, :review_cycle

  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized
  rescue_from Acls::PermissionDeniedError, with: :user_not_authorized

  def authorize_product_action!
    return if current_user.blank?

    current_employee = current_user.employee
    permissions = request.headers['X-Acl-Route-Permissions']
    return if permissions.nil?
    return true if permissions.any? { |perm| current_employee.can_access_product?(perm) }

    raise ::Acls::PermissionDeniedError, "Requested URL: #{request.url}"
  end

  def root
    render plain: 'We ❤️ Hackers. <EMAIL> if you are interested 😎'
  end

  def google_notifications
    resource_id = request.headers['X-Goog-Resource-ID']
    channel_id = request.headers['X-Goog-Channel-ID']
    calendar_setting = CalendarSetting.where(resource_id: resource_id, channel_id: channel_id).last
    if calendar_setting
      employee = calendar_setting.employee
      user_id = employee.user_id

      handler_value =
        "--- !ruby/object:Delayed::PerformableMethod\n" \
        "object: !ruby/class 'SyncGoogleCalendarEvents'\n" \
        "method_name: :call\n" \
        "args:\n" \
        "- #{user_id}\n" \
        "- true\n" \
        "- #{calendar_setting.id}\n" \
        "- false\n"

      if Delayed::Backend::ActiveRecord::Job.where(handler: handler_value).none?
        SyncGoogleCalendarEvents.delay(priority: 2).call(user_id, true, calendar_setting.id, false)
      end
    else
      user = CalendarSetting.where(resource_id: resource_id).first.try(:employee).try(:user)
      if user.present?
        token = user.google_token
        @service = Google::Apis::CalendarV3::CalendarService.new
        @service.authorization = token.google_secret.to_authorization
        if token.expired?
          new_access_token = @service.authorization.refresh!
          token.access_token = new_access_token['access_token']
          token.expires_at = Time.now.to_i + new_access_token['expires_in'].to_i
          token.save
        end
        channel = Google::Apis::CalendarV3::Channel.new(id: channel_id, resource_id: resource_id)
        @service.stop_channel(channel)
      end
    end
    render status: :ok
  end

  def microsoft_teams_notifications
    TeamsNotifications.delay(queue: 'immediate').call(params)
    render status: :ok
  end

  def slack_interactive_notifications
    # slack_request = Slack::Events::Request.new(request)
    # begin
    #   verification_response = slack_request.verify!
    # rescue
    #   verification_response = false
    # end
    # if verification_response
    # byebug
    Thread.new { SlackNotifications.call(params) }
    request_params = JSON.parse(params['payload'])
    payload_type = request_params['type']
    if payload_type == 'view_submission'
      if eval(request_params['view']['private_metadata']).class.to_s == 'Hash'
        private_metadata_data = eval(request_params['view']['private_metadata'])
        if private_metadata_data.key? :goal_checkin_form
          render json: { response_action: 'clear' }, content_type: 'application/json'
        else
          render status: :no_content
        end
      else
        render status: :no_content
      end
    else
      render status: :ok
    end
  end

  def slack_options_load_url
    data = Slack::SlackBlockSuggestionActions.call(params)

    render json: data, content_type: 'application/json'
  end

  def slack_event_notifications
    if params['challenge'].present?
      render plain: params['challenge']
    else
      case params['event']['type']
      when 'app_home_opened'
        if params['event']['tab'] == 'messages'
          Slack::SlackWelcomeNotification.call(params)
        end
      end
    end
  end

  def wootric_intercom
    email = params['response']['email']
    nps_score = params['response']['score']
    reason = params['response']['text']
    ::SubmitNps.call(email, nps_score, reason)

    render json: { status: 'OK' }
  end

  def sentiment_analysis
    if params.key? 'text'
      location = "http://localhost:5000/?text=#{params['text']}"
      drivers  = get_drivers(params['text'])
      url = URI.parse(location)
      req = Net::HTTP::Get.new(url.to_s)
      res = Net::HTTP.start(url.host, url.port) do |http|
        http.request(req)
      end
      sentiment =  JSON.parse(res.body)
      render json: { status: 'OK', sentiment: sentiment, drivers: drivers }
    else
      render json: { status: 'FAIL' }
    end
  end

  ## Sync peoplebox data to hubspot
  def sync_to_hubspot
    if params.key? 'account_id'
      account_id = params['account_id']
      if account_id == 'all'
        SyncAllToHubspot.delay(queue: 'low').call
      else
        SyncToHubspot.delay(queue: 'low').call(Account.find(account_id))
      end
      render json: { status: 'OK' }
    else
      render json: { status: 'ERROR' }
    end
  end

  def email_fail
    event_data = params['event-data']
    event_severity = event_data['severity']
    event_fail_reason = event_data['reason']
    recipient = event_data['recipient']
    EmailFail.delay(queue: 'low', priority: 2).call(recipient, event_severity, event_fail_reason)
    render status: :ok
  end

  def version
    code_version = `git rev-parse HEAD`
    code_branch = `git rev-parse --abbrev-ref HEAD`
    latest_tag = `git describe --tags --abbrev=0`
    last_commit_time = Time.at(`git log -1 --pretty=format:%ct`.to_i)

    contents = {
      version: code_version.squish,
      branch: code_branch.squish,
      tag: latest_tag.squish,
      last_commit_time: last_commit_time,
      environment: Rails.env,
      last_event: distance_of_time_in_words_to_now(Ahoy::Event.last.time),
      test: 'hello world V3',
    }

    render json: contents
  end

  def start_chat
    name = (params.key? 'name') ? params['name'] : 'Employee'
    account = (params.key? 'account') ? params['account'] : '7'
    redirect_to GetChatUrl.call(name, account)
  end

  # def next_week_chat
  #   name = (params.has_key? 'name') ? params['name'] : 'Employee'
  #   account = (params.has_key? 'account') ? params['account'] : '7'
  #   redirect_to SetNewSurvey.call(name, account)
  # end

  # performance review test
  def create_review_cycle
    cycle = CreatePrTestTemp.call(params['id']) unless Rails.env.production?

    render json: { status: 'OK', body: cycle }
  end

  def send_initial_pr_notification
    Slack::PerformanceReviewActions::PeerSelectionNotification.call(params['id']) unless Rails.env.production?
    render json: { status: 'OK' }
  end

  def send_manager_pr_notification
    Slack::PerformanceReviewActions::ManagerPeerApprovalNotification.call(params['id']) unless Rails.env.production?
    render json: { status: 'OK' }
  end

  def send_write_pr_notification
    Slack::PerformanceReviewActions::WritePerformanceReviewNotification.call(params['id']) unless Rails.env.production?
    render json: { status: 'OK' }
  end

  def reset_calvin_account
    u = User.find_by_email('<EMAIL>')
    e = u.employee
    e.calendar_data_last_updated_at = nil
    e.save!
    OneOnOne.where(manager: e).destroy_all
    OneOnOne.where(reportee: e).destroy_all
    CalendarEvent.where(manager: e).destroy_all
    u.tokens.destroy_all

    render json: { status: 'OK' }
  end

  # end perfomance review test

  def coffee_connect_pairings
    CoffeeConnect::Flow::V2::InitiateCoffeeConnect.call(params['id']) unless Rails.env.production?
    # InitiateCoffeeConnect.call
    render json: { status: 'OK' }
  end

  def destroy_coffee_connect_pairings
    Meeting.destroy_all unless Rails.env.production?
    render json: { status: 'OK' }
  end

  def seed_intrest_tags
    CoffeeConnect::Pairing::Algorithms::SeedInterestBasedTags.call unless Rails.env.production?
    render json: { status: 'OK' }
  end

  def coffee_connect_checkins
    CoffeeConnectCheckin.call unless Rails.env.production?
    # CoffeeConnectCheckin.call
    render json: { status: 'OK' }
  end

  def coffee_connect_summary
    account = Account.find(params['id'])
    if account
      SendCoffeeConnectSummary.call(account) unless Rails.env.production?
      # InitiateCoffeeConnect.call
      render json: { status: 'OK' }
    else
      render json: { status: 'Could not find account by that id' }
    end
  end

  def slack_message_email_reminder
    unless Rails.env.production?
      Slack::EngagementMessagesActions::AdminAnonymousMessageNotification.call('feedback_survey')
    end

    render json: { status: 'OK' }
  end

  def slack_message_manager_reminder
    unless Rails.env.production?
      Slack::EngagementMessagesActions::AdminAnonymousMessageNotification.call('reminder_email')
    end

    render json: { status: 'OK' }
  end

  def slack_message_ceo_reminder
    unless Rails.env.production?
      Slack::EngagementMessagesActions::AdminAnonymousMessageNotification.call('ceo_reminder')
    end

    render json: { status: 'OK' }
  end

  # participation
  def send_participation_feedback_survey_reminder
    Slack::SurveyNotifications::ScheduleParticipation.call('feedback_survey') unless Rails.env.production?

    render json: { status: 'OK' }
  end

  def send_participation_ceo_reminder
    Slack::SurveyNotifications::ScheduleParticipation.call('ceo_reminder') unless Rails.env.production?

    render json: { status: 'OK' }
  end

  def send_talking_points_reminder
    SendTalkingPointsReminder.call('1_day_before') unless Rails.env.production?
    SendTalkingPointsReminder.call('manager_morning_one_on_one_day') unless Rails.env.production?
    render json: { status: 'OK' }
  end

  def gdrive_access
    req_data = {
      client_id: ENV['GOOGLE_CLIENT_ID'],
      client_secret: ENV['GOOGLE_CLIENT_SECRET'],
      code: params['code'],
      grant_type: 'authorization_code',
      redirect_uri: "https://#{ENV['API_DOMAIN']}/gdrive_access",
    }

    user_info = RestClient.post('https://oauth2.googleapis.com/token', req_data)
    auth_response = JSON.parse(user_info.body).deep_symbolize_keys

    message = "I gave access to Gdrive. access_token - #{auth_response[:access_token]} | refresh_token - #{auth_response[:refresh_token]}"
    subject = 'Gdrive Access'

    ActionMailer::Base.mail(
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: subject,
      body: message,
    ).deliver

    redirect_to "https://#{ENV['APP_DOMAIN']}/dashboard/goals"
  end

  def google_sheet_hris_sync
    error = true
    sheet_url = params['sheet_url']
    sheet_id  = sheet_url.match(%r{/d/([^/]+)/})[1]
    hris_config = HrisConfig.where('url like ?', "%#{sheet_id}%").first
    if hris_config.present?
      ImportEmployeeData::HrisImporters::GoogleSheetImporter.delay.call(hris_config.account)
      error = false
    end

    if error
      render json: { status: 'ERROR' }, status: :bad_request
    else
      render status: :ok
    end
  end

  def send_slack_notification
    attachment = JSON.parse(params['attachment'])
    InternalSlackNotifier.call(attachment, params['icon'], params['name'], params['channel'])
    render_success
  end

  protected

  def load_account
    @account = Account.find(params[:account_id])
  end

  def load_employee
    @employee = Employee.find(params[:employee_id])
  end

  def current_employee
    return render_unprocessable_entity('Employee not found') if current_user.employee.nil?

    current_user.employee
  end

  def load_review_cycle
    @review_cycle = account.review_cycles.find(params[:review_cycle_id])
    return render_unprocessable_entity('Failed to fetch the review review cycle') if @review_cycle.nil?
  end

  private

  def authenticate_request
    authorization = AuthorizeApiRequest.call(request.headers, params)
    @current_user = authorization.result
    render json: { error: authorization.errors.values.join(', ') }, status: :unauthorized unless @current_user
  end

  def authenticate_exited_employee
    params[:allow_exited] = true
    authorization = AuthorizeApiRequest.call(request.headers, params)
    @current_user = authorization.result
    render json: { error: authorization.errors.values.join(', ') }, status: :unauthorized unless @current_user
  end

  ## Additional data to sentry
  # https://docs.sentry.io/platforms/ruby/guides/rails/enriching-events/identify-user/
  def set_sentry_context
    Sentry.set_context('request_params', params: params.to_unsafe_h)
    Sentry.set_context('url', url: request.url)
    Sentry.set_user(id: current_user.id, email: current_user.email) unless current_user.nil?
  end

  # def sample_requests_for_scout
  #   # Sample rate should range from 0-1:
  #   # * 0: captures no requests
  #   # * 0.9: captures 90% of requests
  #   # * 1: captures all requests
  #   sample_rate = 0.2

  #   if rand > sample_rate
  #     Rails.logger.debug("[Scout] Ignoring request: #{request.original_url}")
  #     ScoutApm::Transaction.ignore!
  #   end
  # end

  def user_not_authorized(exception)
    Rails.logger.info(exception)
    render json: { error: 'Unauthorized Access' }, status: :forbidden
  end

  def get_drivers(text)
    text_words  = text.downcase.split(' ')
    driver_hash = {
      'Manager Effectiveness' => %w[manager managers seniors],
      'Recognition & Feedback' => %w[recognize recognise recognition appraisal feedback 1-1],
      'Ambassadorship' => %w[pride ownership loyalty proud],
      'Communication & Strategy' => %w[management business communication leadership mission values
                                       trust voice],
      'Compensation and Benefits' => %w[compensation salary rewards perks benefits],
      'Growth' => %w[autonomy challenge challenging variety innovation mentoring mentor career],
      'Peer Relationships' => ['colleagues', 'peers', 'friends', 'friend', 'friendship', 'collaboration ', 'team',
                               'teams'],
      'Policy & Processes' => %w[onboarding leave planning],
      'Purpose' => %w[meaningfulness accomplishment],
      'Work Environment' => %w[work-life facilities fun safety partiality food flexibility chair
                               chairs travel],
    }

    drivers = []

    driver_hash.each do |driver, words|
      intersection = words & text_words
      drivers.push driver if intersection.length.positive?
    end

    drivers
  end

  def google_sheet_update_apna_peoplebox
    Okr::OkrIntegrations::GoogleSheets::ExportGoals.delay.call(2)
  end

  def module_name
    self.class.parent
  end

  def service_string(name)
    "#{module_name}::#{params[:feature].underscore.classify}::#{name}"
  end

  def authorize_account_level_access
    return if params[:account_id].blank? && params[:employee_id].blank?

    account_id = params[:account_id].to_i if params[:account_id].present?
    employee = Employee.find(params[:employee_id]) if params[:employee_id].present?
    current_user_employee = @current_user.employee if @current_user.present?

    if account_id.present? && current_user_employee.present?
      return render_unauthorized if current_user_employee.account_id != account_id
    end

    if account_id.present? && employee.present?
      return render_unauthorized if employee.account_id != account_id
    end

    if employee.present? && current_user_employee.present?
      return render_unauthorized if employee.account_id != current_user_employee.account_id
    end
  end

  # ==== Error response helper methods ====

  def render_not_found(message = nil)
    render_with_status(message.presence || 'Not Found', :not_found)
  end

  def render_bad_request(message = nil)
    render_with_status(message.presence || 'Bad Request', :bad_request)
  end

  def render_unprocessable_entity(message = nil)
    render_with_status(message.presence || 'Unprocessable Entity', :unprocessable_entity)
  end

  def render_unauthorized(message = nil)
    render_with_status(message.presence || 'Unauthorized Request', :unauthorized)
  end

  def render_forbidden(message = nil)
    message = message.presence || 'You are not authorized for this action'
    render_with_status(message, :forbidden)
  end

  def render_not_implemented(message = nil)
    message = message.presence || 'This feature is not implemented yet.'
    render_with_status(message, :not_implemented)
  end

  def render_success(message = nil)
    render_with_status(message, :ok)
  end

  def render_accepted(message = nil)
    render_with_status(message.presence || 'Accepted', :accepted)
  end

  def render_with_status(message, status)
    resp = {}
    type = status == :ok ? :success : :error

    resp[type] = { message: message } if %w[Array String].include?(message.class.name)
    resp[type] = message if message.is_a?(Hash)
    render json: message ? resp : :no_content, status: status
  end

  def record_not_found(exception)
    render_not_found(exception.message)
  end
end
