class ManagersController < ApiController

  before_action :load_account, :load_manager

  def key_metrics
    authorize @manager
    dateRange = params["dateRange"].to_i
    key_metrics = GetKeyMetrics.call(@account, dateRange, @manager)

    render json: { key_metrics: key_metrics }
  end

  def trends
    authorize @manager
    trends = GetKeyMetricsTrends.call(@account, @manager)

    render json: trends
  end

  def key_drivers
    authorize @manager
    dateRange = params["dateRange"].to_i
    key_drivers = GetDashboardKeyDrivers.call(@account, dateRange, @manager)

    render json: {key_drivers: key_drivers}
  end

  def manager_reportees
    authorize @manager
    manager_reportees = GetManagerReportees.call(@manager)
    render json: {manager_reportees: manager_reportees}
  end

  def add_reportees
    authorize @manager
    if params["reportees"].first.key?('error')
      @reportees = AddReportees.call(@manager, params["reportees"], nil, 'manager_reportee_page')
    else
      @reportees = AddReportees.call(@manager, params["reportees"], nil, 'one_on_one_calendar_create_popup')
    end
  end

  def invite_reportees
    authorize @manager
    @reportees = InviteReportees.call(@manager, params["reportees"])
    render json: @reportees
  end

  def remove_reportee
    # authorize @manager
    reportee = Employee.find(params[:emp_id])
    reportee.manager = nil
    reportee.invited = false

    reportee.save!
    upcoming_one_on_ones = OneOnOne.where(manager: @manager, reportee: reportee).where('start_time > ?', DateTime.now)
    upcoming_one_on_ones.destroy_all
  end

  def redirect_to_nudge_thanks
    authorize @manager
    @schedule = Schedule.find(params["schedule_id"])
    @recipients = Employee.joins(:employee_chats).where(employee_chats: {schedule_id: @schedule.id})
    @reportees = @manager.direct_reports
    @reportees.each do |reportee|
      if @recipients.include? reportee
        @employee_chat = EmployeeChat.where(employee: reportee, schedule: @schedule).first
        @responses = Response.where(employee_chat: @employee_chat)
        if (!@responses.present?)
          EmployeeMailer.delay.manager_nudged(@schedule, @manager, reportee, @employee_chat)
        end
      end
    end
    app_server = "https://#{ENV['CHAT_DOMAIN']}"
    path = "nudged-team"
    app_server_url = "#{app_server}/#{path}"
    redirect_to app_server_url
  end

  def fetch_calendar_data
    authorize @manager
    res_arr = FilterReporteeAndEvents.call(@manager)
    render json: res_arr
  end

  def refresh_calendar_data
    authorize @manager
    if @manager.account.email_client == 'gmail'
      calendar_setting_id = CalendarSetting.where(employee_id: @manager.id).ids.last
      SyncGoogleCalendarEvents.delay(queue: 'high').call(@manager.user.id, false, calendar_setting_id, true)
    elsif @manager.account.email_client == 'outlook'
      FetchOutlookCalendarEvents.delay(queue: 'high').call(@manager.user.id, false)
    end
    @manager.calendar_data_last_updated_at = nil
    @manager.save!
  end

  def one_on_one_page_polling
    authorize @manager
    one_on_ones = @manager.manager_one_on_ones.count
    events_last_updated = @manager.calendar_data_last_updated_at
    one_on_ones_last_updated = @manager.one_on_ones_updated_at
    calendar_events = @manager.calendar_events.count
    if one_on_ones_last_updated.present?
      if one_on_ones > 0
        res = { upcoming_api: true, create_one_on_one: false, add_from_calendar: false, keep_polling: false }
      elsif one_on_ones <= 0 && events_last_updated.present? && calendar_events > 0
        res = { upcoming_api: false, create_one_on_one: false, add_from_calendar: true, keep_polling: false }
      elsif one_on_ones <= 0 && events_last_updated.present? && calendar_events <= 0
        res = { upcoming_api: false, create_one_on_one: true, add_from_calendar: false, keep_polling: false }
      end
    else
      res = { upcoming_api: false, create_one_on_one: false, add_from_calendar: false, keep_polling: true }
    end
    render json: res
  end

  def add_reportees_events
    authorize @manager
    selcted_events = params[:events]
    cal_events = ::CalendarEvent.where(id: selcted_events)
    reportees = []
    res = []
    cal_events.each do |event|
      attendee_one = User.where(email: event.attendee_email).first.employee
      attendee_two = event.manager
      if attendee_one.manager == attendee_two
        report = attendee_one
        manager = attendee_two
      else
        report = attendee_two
        manager = attendee_one
      end

      one_on_one = ::OneOnOne.where(cal_id: event.cal_event_id).first_or_create!(
        manager: manager, creator: manager, reportee: report, scheduled_time: event.start_time,
        start_time: event.start_time, end_time: event.end_time, title: event.title, cal_id: event.cal_event_id,
        ical_uid: event.ical_uid, recurring_event_id: event.recurring_event_id
      )
      one_on_one.create_pulse
      res << one_on_one
      # if one_on_one.reportee.invited?
      #   if @manager.account.email_client == 'outlook'
      #     CreateUpdateEventsOutlook.delay.call(one_on_one.id, 'update')
      #   else
      #     CreateUpdateEventsGoogle.delay.call(one_on_one.id, 'update')
      #   end
      # end
    end
    UpdateCalendarEvents.delay(queue: 'normal').call(res)
    render json: res, current_employee: @manager
  end

  def show
    authorize @manager

    profile = GetManagerProfile.call(@manager)

    render json: {manager: profile}
  end

  def update_relationship
    authorize @manager
    relationship = params[:relationship]
    other_party = Employee.find(params[:other_party_id])
    if relationship == 'manager'
      other_party.update_attributes(manager_id: nil)
      @manager.update_attributes(manager_id: params[:other_party_id], invited: true, relationship_updated: true)
      @one_on_ones = OneOnOne.where(manager: @manager, reportee: other_party)
      @one_on_ones.each do |one_on_one|
        one_on_one.update_attributes(manager: other_party, reportee: @manager)
        pulses = one_on_one.pulses
        manager_pulse = pulses.find { |p| p.pulse_type == 'manager_pulse' }
        report_pulse = pulses.find { |p| p.pulse_type == 'reportee_pulse' }
        manager_pulse.update_attributes(manager: other_party, reportee: @manager, employee: other_party)
        report_pulse.update_attributes(manager: other_party, reportee: @manager, employee: @manager)
      end
    elsif relationship == 'direct_report'
      other_party.update_attributes(manager_id: @manager.id, relationship_updated: true)
    end
  end

  def update_relationship_from_email
    authorize @manager
    relationship = params[:relationship]
    other_party = Employee.find(params[:other_party_id])

    if relationship == 'manager'
      other_party.update_attributes(manager_id: nil)
      @manager.update_attributes(manager_id: params[:other_party_id], invited: true, relationship_updated: true)
      @one_on_ones = OneOnOne.where(manager: @manager, reportee: other_party)
      @one_on_ones.each do |one_on_one|
        one_on_one.update_attributes(manager: other_party, reportee: @manager)
        pulses = one_on_one.pulses
        manager_pulse = pulses.find { |p| p.pulse_type == 'manager_pulse' }
        report_pulse = pulses.find { |p| p.pulse_type == 'reportee_pulse' }
        manager_pulse.update_attributes(manager: other_party, reportee: @manager, employee: other_party)
        report_pulse.update_attributes(manager: other_party, reportee: @manager, employee: @manager)
      end
    elsif relationship == 'direct_report'
      other_party.update_attributes(manager_id: @manager.id, relationship_updated: true)
    end

    @url = "https://#{ENV['APP_DOMAIN']}/dashboard/one-on-one/#{params[:one_on_one_id]}"
    redirect_to @url
  end

  def manager_trends
    authorize @manager
    reportee = Employee.find(params[:reportee_id])
    result = GetTrends.call(@manager, reportee, 'manager')
    render json: result
  end


  private

    def load_manager
      @manager = @account.employees.find(params[:id])
    end

end
