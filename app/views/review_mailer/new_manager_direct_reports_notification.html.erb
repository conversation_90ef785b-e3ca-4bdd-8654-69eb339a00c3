<tr>
    <td class="content-block alignleft" style="padding-bottom: 10px; text-align: left;">
        <p>Hi <%= @manager.display_name %>,</p>
    </td>
</tr>

<tr>
    <td class="content-block alignleft" style="padding-bottom: 10px; text-align: left;">
        <p>
            You've been assigned as the new manager for the following team members in the ongoing <span style="font-weight: bold;"><%= @review_cycle.title %></span>.
            Here's a summary of what's changed and what actions are pending:
        </p>
    </td>
</tr>

<% if @reviewees_data.present? %>
<tr>
    <td class="content-block" style="padding-bottom: 20px; text-align: left;">
        <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #dee2e6; padding: 6px; text-align: left; font-weight: bold;">Reviewee</th>
                    <th style="border: 1px solid #dee2e6; padding: 6px; text-align: left; font-weight: bold;">Prev Manager</th>
                    <% if @goal_required %>
                    <th style="border: 1px solid #dee2e6; padding: 6px; text-align: left; font-weight: bold;">Goals</th>
                    <% end %>
                    <% if @peer_required %>
                    <th style="border: 1px solid #dee2e6; padding: 6px; text-align: left; font-weight: bold;">Peers</th>
                    <% end %>
                    <th style="border: 1px solid #dee2e6; padding: 6px; text-align: left; font-weight: bold;">Review by Prev Mgr</th>
                    <th style="border: 1px solid #dee2e6; padding: 6px; text-align: left; font-weight: bold;">Your Action</th>
                </tr>
            </thead>
            <tbody>
                <% @reviewees_data.each do |reviewee_data| %>
                <tr>
                    <td style="border: 1px solid #dee2e6; padding: 4px 6px; text-align: left; white-space: nowrap; line-height: 1.2;">
                        <strong><%= reviewee_data['Reviewee Name'] %></strong>
                    </td>
                    <td style="border: 1px solid #dee2e6; padding: 4px 6px; text-align: left; line-height: 1.2;">
                        <%= reviewee_data['Previous Manager'] %>
                    </td>
                    <% if @goal_required %>
                    <td style="border: 1px solid #dee2e6; padding: 4px 6px; text-align: left; line-height: 1.2;">
                        <%= reviewee_data['Goals Status'] %>
                    </td>
                    <% end %>
                    <% if @peer_required %>
                    <td style="border: 1px solid #dee2e6; padding: 4px 6px; text-align: left; line-height: 1.2;">
                        <%= reviewee_data['Peers Status'] %>
                    </td>
                    <% end %>
                    <td style="border: 1px solid #dee2e6; padding: 4px 6px; text-align: left; line-height: 1.2;">
                        <span style="<%= review_status_color(reviewee_data['Review by Old Manager']) %>">
                            <%= reviewee_data['Review by Old Manager'] %>
                        </span>
                    </td>
                    <% if reviewee_data['Your Action'] == 'Write Review' %>
                    <td style="padding: 4px 6px; text-align: left; white-space: nowrap; line-height: 1.2;">
                        <a href="<%= reviewee_data['Form Link'] %>" style="color: #007bff; text-decoration: underline; font-weight: bold;">
                            <%= reviewee_data['Your Action'] %>
                        </a>
                    </td>
                    <% else %>
                    <td style="border: 1px solid #dee2e6; padding: 4px 6px; text-align: left; line-height: 1.2;">
                        <span style="<%= action_status_color(reviewee_data['Your Action']) %>">
                            <%= reviewee_data['Your Action'] %>
                        </span>
                    </td>
                    <% end %>
                </tr>
                <% end %>
            </tbody>
        </table>
    </td>
</tr>
<% end %>

<tr>
    <td class="content-block alignleft" style="padding-bottom: 20px; text-align: left;">
        <p>
            If you have any questions about your new team members or need help with the review process,
            please reach out to your review administrator.
        </p>
    </td>
</tr>

<tr>
    <td class="content-block alignleft" style="text-align: left;">
        <p>
            Best regards,<br>
            The Peoplebox Team
        </p>
    </td>
</tr>