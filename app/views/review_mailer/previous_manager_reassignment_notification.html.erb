<tr>
    <td class="content-block alignleft" style="padding-bottom: 10px; text-align: left;">
        <p>Hi <%= @previous_manager.first_name %>,</p>
    </td>
</tr>

<tr>
    <td class="content-block alignleft" style="padding-bottom: 10px; text-align: left;">
        <p>
            Some of your Direct Reports have been reassigned to new managers in the ongoing <span style="font-weight: bold;"><%= @review_cycle.title %></span>.
        </p>
    </td>
</tr>

<tr>
    <td class="content-block alignleft" style="padding-bottom: 20px; text-align: left;">
        <p style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 6px; padding: 15px; color: #0c5460;">
            <strong>No action required from your end.</strong> Below is a summary of the changes for your reference.
        </p>
    </td>
</tr>

<% if @reviewees_data.present? %>
<tr>
    <td class="content-block alignleft" style="padding-bottom: 10px; text-align: left;">
        <p><strong>🔄 Reassigned Reviewees Summary</strong></p>
    </td>
</tr>

<tr>
    <td class="content-block" style="padding-bottom: 20px; text-align: left;">
        <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left; font-weight: bold;">Reviewee</th>
                    <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left; font-weight: bold;">New Manager</th>
                    <% if @goal_required %>
                    <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left; font-weight: bold;">Goals Status</th>
                    <% end %>
                    <% if @peer_required %>
                    <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left; font-weight: bold;">Peers Status</th>
                    <% end %>
                </tr>
            </thead>
            <tbody>
                <% @reviewees_data.each do |reviewee_data| %>
                <tr>
                    <td style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">
                        <strong><%= reviewee_data['Reviewee Name'] %></strong>
                    </td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">
                        <strong><%= reviewee_data['New Manager'] %></strong>
                    </td>
                    <% if @goal_required %>
                    <td style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">
                        <span style="padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">
                            <%= reviewee_data['Goals Status'] %>
                        </span>
                    </td>
                    <% end %>
                    <% if @peer_required %>
                    <td style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">
                        <span style="padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">
                            <%= reviewee_data['Peers Status'] %>
                        </span>
                    </td>
                    <% end %>
                </tr>
                <% end %>
            </tbody>
        </table>
    </td>
</tr>
<% end %>

<tr>
    <td class="content-block alignleft" style="text-align: left;">
        <p>
            Best regards,<br>
            The Peoplebox Team
        </p>
    </td>
</tr>