# frozen_string_literal: true

module ReviewMailerHelper
  # Helper methods for status color styling in email templates
  def status_color(_status)
    'color: #1c1d1f;'
  end

  def review_status_color(_status)
    'color: #1c1d1f;'
  end

  def action_status_color(_action)
    'color: #1c1d1f;'
  end

  def generate_manager_changes_csv(summary_data)
    require 'csv'

    # Define CSV headers (excluding metadata fields)
    csv_headers = [
      'Reviewee Name',
      'Reviewee Email',
      'New Manager',
      'New Manager Email',
      'Previous Managers',
      'Previous Managers Emails',
      'Goals Status',
      'Peers Status',
    ]

    CSV.generate(headers: true) do |csv|
      csv << csv_headers

      summary_data.each do |row|
        csv << csv_headers.map { |header| row[header] }
      end
    end
  end
end
