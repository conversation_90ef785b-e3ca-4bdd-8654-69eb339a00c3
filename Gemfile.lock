GIT
  remote: https://github.com/JerryGreen/shortener.git
  revision: cffae2ea4eb9e0d9d5aed03aff1da1a5107e79f5
  branch: rails5
  specs:
    shortener (0.6.2)
      rails (>= 3.0.7)
      voight_kampff (~> 1.1, ~> 1.0)

GIT
  remote: https://github.com/Peoplebox/reverse_markdown.git
  revision: a22cf95d309c74c70189bd4ee8a7237ac9474701
  branch: master
  specs:
    reverse_markdown (2.1.1)
      nokogiri

GIT
  remote: https://github.com/ffaker/ffaker.git
  revision: f244724bb044a538093a8bae5202f7f6ebcbabcd
  specs:
    ffaker (2.17.0)

GIT
  remote: https://github.com/stympy/faker.git
  revision: a65677484e246165735806da09a8f6e7c62f9c61
  branch: master
  specs:
    faker (2.14.0)
      i18n (>= 1.6, < 2)

PATH
  remote: simple_chat
  specs:
    simple_chat (0.1.0)
      rails (~> 5.2.1)
      railties (>= 4.1.0, < 6.0)

GEM
  remote: https://rubygems.org/
  specs:
    Ascii85 (1.1.0)
    actioncable (*******)
      actionpack (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.0.8)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    active_admin_datetimepicker (0.7.4)
      activeadmin (>= 1.1, < 3.a)
      coffee-rails
      xdan-datetimepicker-rails (~> 2.5.4)
    active_material (1.4.2)
    active_model_serializers (0.10.10)
      actionpack (>= 4.1, < 6.1)
      activemodel (>= 4.1, < 6.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    activeadmin (2.8.1)
      arbre (~> 1.2, >= 1.2.1)
      formtastic (>= 3.1, < 5.0)
      formtastic_i18n (~> 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (~> 4.2)
      kaminari (~> 1.0, >= 1.2.1)
      railties (>= 5.2, < 6.1)
      ransack (~> 2.1, >= 2.1.1)
      sassc-rails (~> 2.1)
      sprockets (>= 3.0, < 4.1)
    activeadmin_addons (1.7.1)
      active_material
      railties
      require_all (~> 1.5)
      sass
      select2-rails (~> 4.0)
      xdan-datetimepicker-rails (~> 2.5.1)
    activeadmin_trumbowyg (0.2.8)
      activeadmin (~> 2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      arel (>= 9.0)
    activerecord-import (1.4.1)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activerecord (= *******)
      marcel (~> 0.3.1)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 0.7, < 2)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
    acts-as-taggable-on (6.5.0)
      activerecord (>= 5.0, < 6.1)
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    afm (0.2.2)
    after_the_deadline (0.1.3)
      crack
    aggregate_root (2.1.0)
      ruby2_keywords
      ruby_event_store (= 2.1.0)
    ahoy_email (1.1.0)
      actionmailer (>= 5)
      addressable (>= 2.3.2)
      nokogiri
      safely_block (>= 0.1.1)
    ahoy_matey (3.0.5)
      activesupport (>= 5)
      device_detector
      geocoder (>= 1.4.5)
      safely_block (>= 0.2.1)
    airbrussh (1.4.0)
      sshkit (>= 1.6.1, != 1.7.0)
    ancestry (3.2.1)
      activerecord (>= 4.2.0)
    arbre (1.3.0)
      activesupport (>= 3.0.0, < 6.1)
      ruby2_keywords (>= 0.0.2, < 1.0)
    arel (9.0.0)
    arkency-command_bus (0.4.1)
      concurrent-ruby
    ast (2.4.2)
    audit-log (1.2.0)
      kaminari (>= 0.15)
      rails (>= 5.2)
    awesome_print (1.9.2)
    aws-eventstream (1.1.0)
    aws-partitions (1.388.0)
    aws-sdk-core (3.109.1)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.239.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1.0)
    aws-sdk-kms (1.39.0)
      aws-sdk-core (~> 3, >= 3.109.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.83.1)
      aws-sdk-core (~> 3, >= 3.109.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.1)
    aws-sigv4 (1.2.2)
      aws-eventstream (~> 1, >= 1.0.2)
    backport (1.2.0)
    bcrypt (3.1.16)
    benchmark (0.2.1)
    binding_of_caller (0.8.0)
      debug_inspector (>= 0.0.1)
    bootsnap (1.5.0)
      msgpack (~> 1.0)
    builder (3.2.4)
    bullet (6.1.0)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (11.1.3)
    capistrano (3.14.1)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.0.1)
      capistrano (~> 3.1)
    capistrano-rails (1.6.1)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rails-console (2.3.0)
      capistrano (>= 3.5.0, < 4.0.0)
      sshkit-interactive (~> 0.3.0)
    capistrano-rbenv (2.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.3)
    capistrano-thin (2.0.1)
      capistrano (~> 3.0)
      thin (~> 1.6)
    carrierwave (2.1.0)
      activemodel (>= 5.0.0)
      activesupport (>= 5.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      mimemagic (>= 0.3.0)
      mini_mime (>= 0.1.3)
    case_transform (0.2)
      activesupport
    chargebee (2.8.1)
      json_pure (~> 2.1)
      rest-client (>= 1.8, < 3.0)
    childprocess (4.1.0)
    classifier-reborn (2.2.0)
      fast-stemmer (~> 1.0)
    clockwork (2.0.4)
      activesupport
      tzinfo
    coderay (1.1.3)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.1.7)
    config (2.2.1)
      deep_merge (~> 1.2, >= 1.2.1)
      dry-validation (~> 1.0, >= 1.0.0)
    crack (0.4.4)
    crass (1.0.6)
    css_parser (1.7.1)
      addressable
    daemons (1.3.1)
    database_cleaner-active_record (2.0.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    debug_inspector (0.0.3)
    declarative (0.0.20)
    declarative-option (0.1.0)
    deep_cloneable (3.2.0)
      activerecord (>= 3.1.0, < 8)
    deep_merge (1.2.1)
    delayed_job (4.1.8)
      activesupport (>= 3.0, < 6.1)
    delayed_job_active_record (4.1.4)
      activerecord (>= 3.0, < 6.1)
      delayed_job (>= 3.0, < 5)
    dentaku (3.5.1)
      concurrent-ruby
    device_detector (1.0.5)
    diff-lcs (1.4.4)
    discard (1.2.0)
      activerecord (>= 4.2, < 7)
    docile (1.4.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.7.6)
    dotenv-rails (2.7.6)
      dotenv (= 2.7.6)
      railties (>= 3.2)
    dry-configurable (0.11.6)
      concurrent-ruby (~> 1.0)
      dry-core (~> 0.4, >= 0.4.7)
      dry-equalizer (~> 0.2)
    dry-container (0.7.2)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 0.1, >= 0.1.3)
    dry-core (0.4.9)
      concurrent-ruby (~> 1.0)
    dry-equalizer (0.3.0)
    dry-inflector (0.2.0)
    dry-initializer (3.0.4)
    dry-logic (1.0.8)
      concurrent-ruby (~> 1.0)
      dry-core (~> 0.2)
      dry-equalizer (~> 0.2)
    dry-schema (1.5.6)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 0.8, >= 0.8.3)
      dry-core (~> 0.4)
      dry-equalizer (~> 0.2)
      dry-initializer (~> 3.0)
      dry-logic (~> 1.0)
      dry-types (~> 1.4)
    dry-types (1.4.0)
      concurrent-ruby (~> 1.0)
      dry-container (~> 0.3)
      dry-core (~> 0.4, >= 0.4.4)
      dry-equalizer (~> 0.3)
      dry-inflector (~> 0.1, >= 0.1.2)
      dry-logic (~> 1.0, >= 1.0.2)
    dry-validation (1.5.6)
      concurrent-ruby (~> 1.0)
      dry-container (~> 0.7, >= 0.7.1)
      dry-core (~> 0.4)
      dry-equalizer (~> 0.2)
      dry-initializer (~> 3.0)
      dry-schema (~> 1.5, >= 1.5.2)
    e2mmap (0.1.0)
    errbase (0.2.0)
    erubi (1.9.0)
    ethon (0.12.0)
      ffi (>= 1.3.0)
    eventmachine (1.2.7)
    excon (0.78.0)
    execjs (2.7.0)
    factory_bot (6.1.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.1.0)
      factory_bot (~> 6.1.0)
      railties (>= 5.0.0)
    faraday (1.1.0)
      multipart-post (>= 1.2, < 3)
      ruby2_keywords
    faraday_middleware (1.0.0)
      faraday (~> 1.0)
    fast-stemmer (1.0.2)
    ffi (1.15.5)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    fog-aws (3.6.7)
      fog-core (~> 2.1)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
      ipaddress (~> 0.8)
    fog-core (2.2.3)
      builder
      excon (~> 0.71)
      formatador (~> 0.2)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.3)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (0.2.5)
    formtastic (3.1.5)
      actionpack (>= 3.2.13)
    formtastic_i18n (0.6.0)
    geocoder (1.6.4)
    gli (2.19.2)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    god (0.13.7)
    google-api-client (0.48.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 0.9)
      httpclient (>= 2.8.1, < 3.0)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.0)
      rexml
      signet (~> 0.12)
    googleauth (0.14.0)
      faraday (>= 0.17.3, < 2.0)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (~> 0.14)
    has_scope (0.7.2)
      actionpack (>= 4.1)
      activesupport (>= 4.1)
    hashdiff (1.1.0)
    hashery (2.1.2)
    hashie (4.1.0)
    highline (2.0.3)
    http-cookie (1.0.3)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    i18n (1.8.5)
      concurrent-ruby (~> 1.0)
    icalendar (2.5.3)
      ice_cube (~> 0.16)
    ice_cube (0.16.3)
    iconv (1.0.8)
    image_processing (1.12.0)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    inherited_resources (1.11.0)
      actionpack (>= 5.0, < 6.1)
      has_scope (~> 0.6)
      railties (>= 5.0, < 6.1)
      responders (>= 2, < 4)
    iniparse (1.5.0)
    interception (0.5)
    ipaddress (0.8.3)
    jaro_winkler (1.6.0)
    jmespath (1.4.0)
    jquery-rails (4.4.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json-diff (0.4.1)
    json-schema (2.8.1)
      addressable (>= 2.4)
    json_pure (2.3.1)
    jsonapi-renderer (0.2.2)
    jsonpath (0.5.8)
      multi_json
    jwt (2.2.2)
    kaminari (1.2.1)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.1)
      kaminari-activerecord (= 1.2.1)
      kaminari-core (= 1.2.1)
    kaminari-actionview (1.2.1)
      actionview
      kaminari-core (= 1.2.1)
    kaminari-activerecord (1.2.1)
      activerecord
      kaminari-core (= 1.2.1)
    kaminari-core (1.2.1)
    kramdown (2.4.0)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    launchy (2.5.0)
      addressable (~> 2.7)
    letter_opener (1.7.0)
      launchy (~> 2.2)
    letter_opener_web (1.4.0)
      actionmailer (>= 3.2)
      letter_opener (~> 1.0)
      railties (>= 3.2)
    liquid (4.0.3)
    listen (3.1.5)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
      ruby_dep (~> 1.2)
    loofah (2.7.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    mailgun-ruby (1.2.0)
      rest-client (~> 2.0.2)
    marcel (0.3.3)
      mimemagic (~> 0.3.2)
    memoist (0.16.2)
    method_source (1.0.0)
    microsoft_graph (0.1.3)
      nokogiri (~> 1.8.0)
    mime-types (3.3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2020.0512)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_magick (4.10.1)
    mini_mime (1.0.2)
    mini_portile2 (2.3.0)
    minitest (5.14.2)
    msgpack (1.3.3)
    multi_json (1.15.0)
    multipart-post (2.1.1)
    mustermann (2.0.2)
      ruby2_keywords (~> 0.0.1)
    mysql2 (0.5.3)
    net-scp (3.0.0)
      net-ssh (>= 2.6.5, < 7.0.0)
    net-ssh (6.1.0)
    netrc (0.11.0)
    newrelic_rpm (6.13.1)
    ngrok-tunnel (2.1.1)
    nio4r (2.5.4)
    nokogiri (1.8.5)
      mini_portile2 (~> 2.3.0)
    os (1.1.1)
    overcommit (0.58.0)
      childprocess (>= 0.6.3, < 5)
      iniparse (~> 1.4)
      rexml (~> 3.2)
    paper_trail (11.0.0)
      activerecord (>= 5.2)
      request_store (~> 1.1)
    parallel (1.19.2)
    parser (*******)
      ast (~> 2.4.1)
    pdf-reader (2.12.0)
      Ascii85 (~> 1.0)
      afm (~> 0.2.1)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pdftoimage (0.1.7)
      iconv (~> 1.0)
    pg (0.18.4)
    polyamorous (2.3.2)
      activerecord (>= 5.2.1)
    posthog-ruby (2.5.0)
      concurrent-ruby (~> 1)
    pry (0.13.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-nav (1.0.0)
      pry (>= 0.9.10, < 0.15)
    pry-rescue (1.5.2)
      interception (>= 0.5)
      pry (>= 0.12.0)
    pry-stack_explorer (0.4.12)
      binding_of_caller (~> 0.7)
      pry (~> 0.13)
    public_suffix (4.0.6)
    puma (3.12.6)
    pundit (2.1.0)
      activesupport (>= 3.0.0)
    rack (2.2.3)
    rack-cors (1.1.1)
      rack (>= 2.0.0)
    rack-protection (2.2.4)
      rack
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rails (*******)
      actioncable (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.3.0)
      railties (= *******)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.3.0)
      loofah (~> 2.3)
    rails_event_store (2.1.0)
      activejob (>= 3.0)
      activemodel (>= 3.0)
      activesupport (>= 3.0)
      aggregate_root (= 2.1.0)
      arkency-command_bus (>= 0.4)
      rails_event_store_active_record (= 2.1.0)
      ruby_event_store (= 2.1.0)
      ruby_event_store-browser (= 2.1.0)
    rails_event_store_active_record (2.1.0)
      activerecord (>= 5.0)
      activerecord-import (>= 1.0.2)
      ruby_event_store (= 2.1.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 0.8.7)
      thor (>= 0.19.0, < 2.0)
    rainbow (3.0.0)
    rake (13.0.1)
    ransack (2.3.2)
      activerecord (>= 5.2.1)
      activesupport (>= 5.2.1)
      i18n
      polyamorous (= 2.3.2)
    rb-fsevent (0.10.4)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rbzip2 (0.3.0)
    redcarpet (3.5.0)
    redis (4.2.2)
    regexp_parser (2.1.1)
    representable (3.0.4)
      declarative (< 0.1.0)
      declarative-option (< 0.2.0)
      uber (< 0.2.0)
    request_store (1.5.0)
      rack (>= 1.4)
    require_all (1.5.0)
    responders (3.0.1)
      actionpack (>= 5.0)
      railties (>= 5.0)
    rest-client (2.0.2)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    restforce (5.0.6)
      faraday (>= 0.9.0, <= 2.0)
      faraday_middleware (>= 0.8.8, <= 2.0)
      hashie (>= 1.2.0, < 5.0)
      jwt (>= 1.5.6)
    retriable (3.1.2)
    rexml (3.2.4)
    roadie (4.0.0)
      css_parser (~> 1.4)
      nokogiri (~> 1.8)
    roadie-rails (2.1.1)
      railties (>= 5.1, < 6.1)
      roadie (>= 3.1, < 5.0)
    rspec-core (3.10.1)
      rspec-support (~> 3.10.0)
    rspec-expectations (3.10.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.10.0)
    rspec-mocks (3.10.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.10.0)
    rspec-rails (5.0.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.10.2)
    rubocop (1.19.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml
      rubocop-ast (>= 1.9.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.10.0)
      parser (>= *******)
    rubocop-rails (2.11.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.7.0, < 2.0)
    ruby-odbc (0.999991)
    ruby-progressbar (1.10.1)
    ruby-rc4 (0.1.5)
    ruby-saml (1.11.0)
      nokogiri (>= 1.5.10)
    ruby-vips (2.0.17)
      ffi (~> 1.9)
    ruby2_keywords (0.0.2)
    ruby_dep (1.5.0)
    ruby_event_store (2.1.0)
      concurrent-ruby (~> 1.0, >= 1.1.6)
    ruby_event_store-browser (2.1.0)
      ruby_event_store (= 2.1.0)
      sinatra
    rubyzip (2.3.0)
    safely_block (0.3.0)
      errbase (>= 0.1.1)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    scenic (1.6.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    scenic-mysql_adapter (1.0.1)
      mysql2
      scenic (>= 1.4.0)
    select2-rails (4.0.13)
    sentimental (1.5.0)
    sentry-delayed_job (5.7.0)
      delayed_job (>= 4.0)
      sentry-ruby (~> 5.7.0)
    sentry-rails (5.7.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.7.0)
    sentry-ruby (5.7.0)
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sequel (5.62.0)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    signet (0.14.0)
      addressable (~> 2.3)
      faraday (>= 0.17.3, < 2.0)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_command (0.1.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    sinatra (2.2.4)
      mustermann (~> 2.0)
      rack (~> 2.2)
      rack-protection (= 2.2.4)
      tilt (~> 2.0)
    slack-notifier (2.3.2)
    slack-ruby-client (0.15.1)
      faraday (>= 1.0)
      faraday_middleware
      gli
      hashie
      websocket-driver
    solargraph (0.44.0)
      backport (~> 1.2)
      benchmark
      bundler (>= 1.17.2)
      diff-lcs (~> 1.4)
      e2mmap
      jaro_winkler (~> 1.5)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      parser (~> 3.0)
      reverse_markdown (>= 1.0.5, < 3)
      rubocop (>= 0.52)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
    spring (3.0.0)
    spring-commands-rspec (1.0.4)
      spring (>= 0.9.1)
    sprockets (4.0.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.2)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    sshkit (1.21.0)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    sshkit-interactive (0.3.0)
      sshkit (~> 1.12)
    thin (1.7.2)
      daemons (~> 1.0, >= 1.0.9)
      eventmachine (~> 1.0, >= 1.0.4)
      rack (>= 1, < 3)
    thor (1.0.1)
    thread_safe (0.3.6)
    tilt (2.0.10)
    ttfunk (1.7.0)
    typhoeus (1.4.0)
      ethon (>= 0.9.0)
    tzinfo (1.2.7)
      thread_safe (~> 0.1)
    tzinfo-data (1.2020.4)
      tzinfo (>= 1.0.0)
    uber (0.1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (2.0.0)
    uniform_notifier (1.13.0)
    voight_kampff (1.1.4)
      rack (>= 1.4, < 3.0)
    webmock (3.13.0)
      addressable (>= 2.3.6)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webp-ffi (0.4.0)
      ffi (>= 1.9.0)
      ffi-compiler (>= 0.1.2)
    websocket-driver (0.7.3)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wicked_pdf (2.1.0)
      activesupport
    will_paginate (3.1.8)
    wkhtmltopdf-binary (********)
    xdan-datetimepicker-rails (2.5.4)
      jquery-rails
      rails (>= 3.2.16)
    xmldsig (0.7.0)
      nokogiri (>= 1.6.8, < 2.0.0)
    yard (0.9.37)

PLATFORMS
  ruby

DEPENDENCIES
  active_admin_datetimepicker
  active_material
  active_model_serializers (~> 0.10.6)
  activeadmin
  activeadmin_addons
  activeadmin_trumbowyg
  activerecord-import
  acts-as-taggable-on
  after_the_deadline
  ahoy_email
  ahoy_matey
  ancestry
  audit-log (~> 1.2.0)
  awesome_print
  aws-sdk-s3 (~> 1)
  bcrypt (~> 3.1.7)
  benchmark
  bootsnap (>= 1.1.0)
  bullet
  byebug
  capistrano
  capistrano-bundler
  capistrano-rails
  capistrano-rails-console
  capistrano-rbenv
  capistrano-thin
  carrierwave
  chargebee (~> 2)
  classifier-reborn
  clockwork
  config
  database_cleaner-active_record
  deep_cloneable (~> 3.2.0)
  delayed_job_active_record
  dentaku
  discard (~> 1.2)
  dotenv-rails
  factory_bot_rails
  faker!
  ffaker!
  fog-aws
  god
  google-api-client (~> 0.11)
  highline
  icalendar (~> 2.5.3)
  json-diff (~> 0.4.1)
  json-schema
  jsonpath (~> 0.5.8)
  jwt
  kaminari
  kramdown
  letter_opener_web
  liquid
  listen (>= 3.0.5, < 3.2)
  mail
  mailgun-ruby (~> 1.2.0)
  microsoft_graph
  mini_magick
  mysql2 (>= 0.4.4, < 0.6.0)
  newrelic_rpm
  ngrok-tunnel
  overcommit (~> 0.58.0)
  paper_trail
  parallel
  pdf-reader
  pdftoimage (~> 0.1.7)
  pg (~> 0.18.4)
  posthog-ruby
  pry-nav
  pry-rescue
  pry-stack_explorer
  puma (~> 3.11)
  pundit
  rack-cors
  rails (~> 5.2.0)
  rails_event_store
  rails_event_store_active_record
  rbzip2
  redcarpet
  redis (~> 4.0)
  rest-client
  restforce (~> 5.0.6)
  reverse_markdown!
  roadie-rails
  rspec-rails (~> 5.0.0)
  rubocop (~> 1.19.0)
  rubocop-rails (= 2.11)
  ruby-odbc
  ruby-progressbar
  ruby-saml (~> 1.11)
  ruby_event_store
  rubyzip (>= 1.0.0)
  scenic-mysql_adapter
  sentimental
  sentry-delayed_job
  sentry-rails
  sentry-ruby
  sequel
  shortener!
  shoulda-matchers (~> 4.5.0)
  simple_chat!
  simple_command
  simplecov
  slack-notifier
  slack-ruby-client (~> 0.15.1)
  solargraph (= 0.44)
  spring
  spring-commands-rspec
  thin
  thor
  typhoeus
  tzinfo-data
  webmock
  webp-ffi (~> 0.4.0)
  wicked_pdf
  will_paginate (~> 3.1.0)
  wkhtmltopdf-binary
  xmldsig

RUBY VERSION
   ruby 2.5.1p57

BUNDLED WITH
   1.17.3
